(()=>{var a={};a.id=974,a.ids=[974],a.modules={261:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/app-paths")},440:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>e});var d=c(1658);let e=async a=>[{type:"image/x-icon",sizes:"16x16",url:(0,d.fillMetadataSegment)(".",await a.params,"favicon.ico")+""}]},757:(a,b,c)=>{Promise.resolve().then(c.bind(c,5921))},846:a=>{"use strict";a.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},1025:a=>{"use strict";a.exports=require("next/dist/server/app-render/dynamic-access-async-storage.external.js")},1135:()=>{},1204:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>d});let d=(0,c(1369).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Web_dev\\\\coding_jr\\\\voice-assistant\\\\src\\\\app\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Web_dev\\coding_jr\\voice-assistant\\src\\app\\page.tsx","default")},1420:()=>{},3033:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3295:a=>{"use strict";a.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3873:a=>{"use strict";a.exports=require("path")},4431:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>j,metadata:()=>i});var d=c(7413),e=c(2376),f=c.n(e),g=c(8726),h=c.n(g);c(1135);let i={title:"Voice Assistant",description:"Offline-capable voice assistant with local STT and TTS",manifest:"/manifest.json",themeColor:"#000000",viewport:"width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no",appleWebApp:{capable:!0,statusBarStyle:"default",title:"Voice Assistant"},icons:{icon:[{url:"/icon-192x192.png",sizes:"192x192",type:"image/png"},{url:"/icon-512x512.png",sizes:"512x512",type:"image/png"}],apple:[{url:"/icon-192x192.png",sizes:"192x192",type:"image/png"}]}};function j({children:a}){return(0,d.jsx)("html",{lang:"en",children:(0,d.jsx)("body",{className:`${f().variable} ${h().variable} antialiased`,children:a})})}},5921:(a,b,c)=>{"use strict";let d,e,f,g;c.r(b),c.d(b,{default:()=>db});var h,i,j,k,l,m,n,o,p,q,r,s,t,u,v,w,x,y,z,A,B,C,D,E,F,G,H,I,J,K,L,M,N,O,P,Q,R,S,T,U,V,W,X,Y,Z,$,_,aa,ab,ac,ad,ae,af,ag,ah,ai,aj,ak,al,am,an,ao,ap,aq,ar,as,at,au,av,aw,ax,ay,az,aA,aB,aC,aD=c(687),aE=c(3210);class aF{constructor(a={}){this.mediaStream=null,this.audioContext=null,this.analyser=null,this.processor=null,this.workletNode=null,this.isRecording=!1,this.events={},this.vadThreshold=.01,this.vadSilenceFrames=0,this.vadSilenceThreshold=30,this.vadSpeechFrames=0,this.vadSpeechThreshold=5,this.isSpeaking=!1,this.audioBuffer=[],this.bufferSize=4096,this.events=a}async initialize(){try{this.mediaStream=await navigator.mediaDevices.getUserMedia({audio:{sampleRate:16e3,channelCount:1,echoCancellation:!0,noiseSuppression:!0,autoGainControl:!0}}),this.audioContext=new AudioContext({sampleRate:16e3}),this.analyser=this.audioContext.createAnalyser(),this.analyser.fftSize=2048,this.analyser.smoothingTimeConstant=.8;let a=this.audioContext.createMediaStreamSource(this.mediaStream);a.connect(this.analyser);try{await this.audioContext.audioWorklet.addModule("/audio-processor.js"),this.workletNode=new AudioWorkletNode(this.audioContext,"audio-processor"),this.workletNode.port.onmessage=a=>{let{audioData:b,timestamp:c}=a.data;this.handleAudioData(b,c)},a.connect(this.workletNode)}catch{console.warn("AudioWorklet not available, falling back to ScriptProcessor"),this.processor=this.audioContext.createScriptProcessor(this.bufferSize,1,1),this.processor.onaudioprocess=a=>{let b=a.inputBuffer.getChannelData(0);this.handleAudioData(new Float32Array(b),this.audioContext.currentTime)},a.connect(this.processor),this.processor.connect(this.audioContext.destination)}console.log("Audio manager initialized successfully")}catch(b){let a=Error(`Failed to initialize audio: ${b}`);throw this.events.onError?.(a),a}}handleAudioData(a,b){if(!this.isRecording)return;let c=this.calculateRMS(a);this.events.onVolumeChange?.(c),this.performVAD(c),this.audioBuffer.push(new Float32Array(a));let d={data:new Float32Array(a),timestamp:b,sampleRate:this.audioContext?.sampleRate||16e3};this.events.onAudioChunk?.(d),this.audioBuffer.length>=10&&this.sendBufferedAudio()}calculateRMS(a){let b=0;for(let c=0;c<a.length;c++)b+=a[c]*a[c];return Math.sqrt(b/a.length)}performVAD(a){a>this.vadThreshold?(this.vadSpeechFrames++,this.vadSilenceFrames=0,!this.isSpeaking&&this.vadSpeechFrames>=this.vadSpeechThreshold&&(this.isSpeaking=!0,this.events.onSpeechStart?.())):(this.vadSilenceFrames++,this.vadSpeechFrames=0,this.isSpeaking&&this.vadSilenceFrames>=this.vadSilenceThreshold&&(this.isSpeaking=!1,this.events.onSpeechEnd?.(),this.sendBufferedAudio()))}sendBufferedAudio(){if(0===this.audioBuffer.length)return;let a=new Float32Array(this.audioBuffer.reduce((a,b)=>a+b.length,0)),b=0;for(let c of this.audioBuffer)a.set(c,b),b+=c.length;this.audioBuffer=[];let c={data:a,timestamp:this.audioContext?.currentTime||Date.now(),sampleRate:this.audioContext?.sampleRate||16e3};this.events.onAudioChunk?.(c)}startRecording(){if(!this.audioContext)throw Error("Audio manager not initialized");"suspended"===this.audioContext.state&&this.audioContext.resume(),this.isRecording=!0,this.audioBuffer=[],console.log("Recording started")}stopRecording(){this.isRecording=!1,this.sendBufferedAudio(),console.log("Recording stopped")}setVADThreshold(a){this.vadThreshold=a}getVADThreshold(){return this.vadThreshold}isCurrentlyRecording(){return this.isRecording}isCurrentlySpeaking(){return this.isSpeaking}async cleanup(){this.stopRecording(),this.processor&&(this.processor.disconnect(),this.processor=null),this.workletNode&&(this.workletNode.disconnect(),this.workletNode=null),this.analyser&&(this.analyser.disconnect(),this.analyser=null),this.audioContext&&(await this.audioContext.close(),this.audioContext=null),this.mediaStream&&(this.mediaStream.getTracks().forEach(a=>a.stop()),this.mediaStream=null),console.log("Audio manager cleaned up")}}function aG(a,b,c,d,e){if("m"===d)throw TypeError("Private method is not writable");if("a"===d&&!e)throw TypeError("Private accessor was defined without a setter");if("function"==typeof b?a!==b||!e:!b.has(a))throw TypeError("Cannot write private member to an object whose class did not declare it");return"a"===d?e.call(a,c):e?e.value=c:b.set(a,c),c}function aH(a,b,c,d){if("a"===c&&!d)throw TypeError("Private accessor was defined without a getter");if("function"==typeof b?a!==b||!d:!b.has(a))throw TypeError("Cannot read private member from an object whose class did not declare it");return"m"===c?d:"a"===c?d.call(a):d?d.value:b.get(a)}let aI=function(){let{crypto:a}=globalThis;if(a?.randomUUID)return aI=a.randomUUID.bind(a),a.randomUUID();let b=new Uint8Array(1),c=a?()=>a.getRandomValues(b)[0]:()=>255*Math.random()&255;return"10000000-1000-4000-8000-100000000000".replace(/[018]/g,a=>(a^c()&15>>a/4).toString(16))};function aJ(a){return"object"==typeof a&&null!==a&&("name"in a&&"AbortError"===a.name||"message"in a&&String(a.message).includes("FetchRequestCanceledException"))}let aK=a=>{if(a instanceof Error)return a;if("object"==typeof a&&null!==a){try{if("[object Error]"===Object.prototype.toString.call(a)){let b=Error(a.message,a.cause?{cause:a.cause}:{});return a.stack&&(b.stack=a.stack),a.cause&&!b.cause&&(b.cause=a.cause),a.name&&(b.name=a.name),b}}catch{}try{return Error(JSON.stringify(a))}catch{}}return Error(a)};class aL extends Error{}class aM extends aL{constructor(a,b,c,d){super(`${aM.makeMessage(a,b,c)}`),this.status=a,this.headers=d,this.requestID=d?.get("x-request-id"),this.error=b,this.code=b?.code,this.param=b?.param,this.type=b?.type}static makeMessage(a,b,c){let d=b?.message?"string"==typeof b.message?b.message:JSON.stringify(b.message):b?JSON.stringify(b):c;return a&&d?`${a} ${d}`:a?`${a} status code (no body)`:d||"(no status code or body)"}static generate(a,b,c,d){if(!a||!d)return new aO({message:c,cause:aK(b)});let e=b?.error;return 400===a?new aQ(a,e,c,d):401===a?new aR(a,e,c,d):403===a?new aS(a,e,c,d):404===a?new aT(a,e,c,d):409===a?new aU(a,e,c,d):422===a?new aV(a,e,c,d):429===a?new aW(a,e,c,d):a>=500?new aX(a,e,c,d):new aM(a,e,c,d)}}class aN extends aM{constructor({message:a}={}){super(void 0,void 0,a||"Request was aborted.",void 0)}}class aO extends aM{constructor({message:a,cause:b}){super(void 0,void 0,a||"Connection error.",void 0),b&&(this.cause=b)}}class aP extends aO{constructor({message:a}={}){super({message:a??"Request timed out."})}}class aQ extends aM{}class aR extends aM{}class aS extends aM{}class aT extends aM{}class aU extends aM{}class aV extends aM{}class aW extends aM{}class aX extends aM{}class aY extends aL{constructor(){super("Could not parse response content as the length limit was reached")}}class aZ extends aL{constructor(){super("Could not parse response content as the request was rejected by the content filter")}}class a$ extends Error{constructor(a){super(a)}}let a_=/^[a-z][a-z0-9+.-]*:/i,a0=a=>(a0=Array.isArray)(a),a1=a0;function a2(a){return null!=a&&"object"==typeof a&&!Array.isArray(a)}let a3=a=>new Promise(b=>setTimeout(b,a)),a4="5.11.0",a5=a=>"x32"===a?"x32":"x86_64"===a||"x64"===a?"x64":"arm"===a?"arm":"aarch64"===a||"arm64"===a?"arm64":a?`other:${a}`:"unknown",a6=a=>(a=a.toLowerCase()).includes("ios")?"iOS":"android"===a?"Android":"darwin"===a?"MacOS":"win32"===a?"Windows":"freebsd"===a?"FreeBSD":"openbsd"===a?"OpenBSD":"linux"===a?"Linux":a?`Other:${a}`:"Unknown";function a7(...a){let b=globalThis.ReadableStream;if(void 0===b)throw Error("`ReadableStream` is not defined as a global; You will need to polyfill it, `globalThis.ReadableStream = ReadableStream`");return new b(...a)}function a8(a){let b=Symbol.asyncIterator in a?a[Symbol.asyncIterator]():a[Symbol.iterator]();return a7({start(){},async pull(a){let{done:c,value:d}=await b.next();c?a.close():a.enqueue(d)},async cancel(){await b.return?.()}})}function a9(a){if(a[Symbol.asyncIterator])return a;let b=a.getReader();return{async next(){try{let a=await b.read();return a?.done&&b.releaseLock(),a}catch(a){throw b.releaseLock(),a}},async return(){let a=b.cancel();return b.releaseLock(),await a,{done:!0,value:void 0}},[Symbol.asyncIterator](){return this}}}async function ba(a){if(null===a||"object"!=typeof a)return;if(a[Symbol.asyncIterator])return void await a[Symbol.asyncIterator]().return?.();let b=a.getReader(),c=b.cancel();b.releaseLock(),await c}let bb=({headers:a,body:b})=>({bodyHeaders:{"content-type":"application/json"},body:JSON.stringify(b)}),bc="RFC3986",bd=a=>String(a),be={RFC1738:a=>String(a).replace(/%20/g,"+"),RFC3986:bd},bf=(a,b)=>(bf=Object.hasOwn??Function.prototype.call.bind(Object.prototype.hasOwnProperty))(a,b),bg=(()=>{let a=[];for(let b=0;b<256;++b)a.push("%"+((b<16?"0":"")+b.toString(16)).toUpperCase());return a})();function bh(a,b){if(a0(a)){let c=[];for(let d=0;d<a.length;d+=1)c.push(b(a[d]));return c}return b(a)}let bi={brackets:a=>String(a)+"[]",comma:"comma",indices:(a,b)=>String(a)+"["+b+"]",repeat:a=>String(a)},bj=function(a,b){Array.prototype.push.apply(a,a0(b)?b:[b])},bk={addQueryPrefix:!1,allowDots:!1,allowEmptyArrays:!1,arrayFormat:"indices",charset:"utf-8",charsetSentinel:!1,delimiter:"&",encode:!0,encodeDotInKeys:!1,encoder:(a,b,c,d,e)=>{if(0===a.length)return a;let f=a;if("symbol"==typeof a?f=Symbol.prototype.toString.call(a):"string"!=typeof a&&(f=String(a)),"iso-8859-1"===c)return escape(f).replace(/%u[0-9a-f]{4}/gi,function(a){return"%26%23"+parseInt(a.slice(2),16)+"%3B"});let g="";for(let a=0;a<f.length;a+=1024){let b=f.length>=1024?f.slice(a,a+1024):f,c=[];for(let a=0;a<b.length;++a){let d=b.charCodeAt(a);if(45===d||46===d||95===d||126===d||d>=48&&d<=57||d>=65&&d<=90||d>=97&&d<=122||"RFC1738"===e&&(40===d||41===d)){c[c.length]=b.charAt(a);continue}if(d<128){c[c.length]=bg[d];continue}if(d<2048){c[c.length]=bg[192|d>>6]+bg[128|63&d];continue}if(d<55296||d>=57344){c[c.length]=bg[224|d>>12]+bg[128|d>>6&63]+bg[128|63&d];continue}a+=1,d=65536+((1023&d)<<10|1023&b.charCodeAt(a)),c[c.length]=bg[240|d>>18]+bg[128|d>>12&63]+bg[128|d>>6&63]+bg[128|63&d]}g+=c.join("")}return g},encodeValuesOnly:!1,format:bc,formatter:bd,indices:!1,serializeDate:a=>(e??(e=Function.prototype.call.bind(Date.prototype.toISOString)))(a),skipNulls:!1,strictNullHandling:!1},bl={};function bm(a){let b;return(f??(f=(b=new globalThis.TextEncoder).encode.bind(b)))(a)}function bn(a){let b;return(g??(g=(b=new globalThis.TextDecoder).decode.bind(b)))(a)}class bo{constructor(){h.set(this,void 0),i.set(this,void 0),aG(this,h,new Uint8Array,"f"),aG(this,i,null,"f")}decode(a){let b;if(null==a)return[];let c=a instanceof ArrayBuffer?new Uint8Array(a):"string"==typeof a?bm(a):a;aG(this,h,function(a){let b=0;for(let c of a)b+=c.length;let c=new Uint8Array(b),d=0;for(let b of a)c.set(b,d),d+=b.length;return c}([aH(this,h,"f"),c]),"f");let d=[];for(;null!=(b=function(a,b){for(let c=b??0;c<a.length;c++){if(10===a[c])return{preceding:c,index:c+1,carriage:!1};if(13===a[c])return{preceding:c,index:c+1,carriage:!0}}return null}(aH(this,h,"f"),aH(this,i,"f")));){if(b.carriage&&null==aH(this,i,"f")){aG(this,i,b.index,"f");continue}if(null!=aH(this,i,"f")&&(b.index!==aH(this,i,"f")+1||b.carriage)){d.push(bn(aH(this,h,"f").subarray(0,aH(this,i,"f")-1))),aG(this,h,aH(this,h,"f").subarray(aH(this,i,"f")),"f"),aG(this,i,null,"f");continue}let a=null!==aH(this,i,"f")?b.preceding-1:b.preceding,c=bn(aH(this,h,"f").subarray(0,a));d.push(c),aG(this,h,aH(this,h,"f").subarray(b.index),"f"),aG(this,i,null,"f")}return d}flush(){return aH(this,h,"f").length?this.decode("\n"):[]}}h=new WeakMap,i=new WeakMap,bo.NEWLINE_CHARS=new Set(["\n","\r"]),bo.NEWLINE_REGEXP=/\r\n|[\n\r]/g;let bp={off:0,error:200,warn:300,info:400,debug:500},bq=(a,b,c)=>{if(a){if(Object.prototype.hasOwnProperty.call(bp,a))return a;bv(c).warn(`${b} was set to ${JSON.stringify(a)}, expected one of ${JSON.stringify(Object.keys(bp))}`)}};function br(){}function bs(a,b,c){return!b||bp[a]>bp[c]?br:b[a].bind(b)}let bt={error:br,warn:br,info:br,debug:br},bu=new WeakMap;function bv(a){let b=a.logger,c=a.logLevel??"off";if(!b)return bt;let d=bu.get(b);if(d&&d[0]===c)return d[1];let e={error:bs("error",b,c),warn:bs("warn",b,c),info:bs("info",b,c),debug:bs("debug",b,c)};return bu.set(b,[c,e]),e}let bw=a=>(a.options&&(a.options={...a.options},delete a.options.headers),a.headers&&(a.headers=Object.fromEntries((a.headers instanceof Headers?[...a.headers]:Object.entries(a.headers)).map(([a,b])=>[a,"authorization"===a.toLowerCase()||"cookie"===a.toLowerCase()||"set-cookie"===a.toLowerCase()?"***":b]))),"retryOfRequestLogID"in a&&(a.retryOfRequestLogID&&(a.retryOf=a.retryOfRequestLogID),delete a.retryOfRequestLogID),a);class bx{constructor(a,b,c){this.iterator=a,j.set(this,void 0),this.controller=b,aG(this,j,c,"f")}static fromSSEResponse(a,b,c){let d=!1,e=c?bv(c):console;async function*f(){if(d)throw new aL("Cannot iterate over a consumed stream, use `.tee()` to split the stream.");d=!0;let c=!1;try{for await(let d of by(a,b))if(!c){if(d.data.startsWith("[DONE]")){c=!0;continue}if(null!==d.event&&d.event.startsWith("thread.")){let a;try{a=JSON.parse(d.data)}catch(a){throw console.error("Could not parse message into JSON:",d.data),console.error("From chunk:",d.raw),a}if("error"==d.event)throw new aM(void 0,a.error,a.message,void 0);yield{event:d.event,data:a}}else{let b;try{b=JSON.parse(d.data)}catch(a){throw e.error("Could not parse message into JSON:",d.data),e.error("From chunk:",d.raw),a}if(b&&b.error)throw new aM(void 0,b.error,void 0,a.headers);yield b}}c=!0}catch(a){if(aJ(a))return;throw a}finally{c||b.abort()}}return new bx(f,b,c)}static fromReadableStream(a,b,c){let d=!1;async function*e(){let b=new bo;for await(let c of a9(a))for(let a of b.decode(c))yield a;for(let a of b.flush())yield a}return new bx(async function*(){if(d)throw new aL("Cannot iterate over a consumed stream, use `.tee()` to split the stream.");d=!0;let a=!1;try{for await(let b of e())!a&&b&&(yield JSON.parse(b));a=!0}catch(a){if(aJ(a))return;throw a}finally{a||b.abort()}},b,c)}[(j=new WeakMap,Symbol.asyncIterator)](){return this.iterator()}tee(){let a=[],b=[],c=this.iterator(),d=d=>({next:()=>{if(0===d.length){let d=c.next();a.push(d),b.push(d)}return d.shift()}});return[new bx(()=>d(a),this.controller,aH(this,j,"f")),new bx(()=>d(b),this.controller,aH(this,j,"f"))]}toReadableStream(){let a,b=this;return a7({async start(){a=b[Symbol.asyncIterator]()},async pull(b){try{let{value:c,done:d}=await a.next();if(d)return b.close();let e=bm(JSON.stringify(c)+"\n");b.enqueue(e)}catch(a){b.error(a)}},async cancel(){await a.return?.()}})}}async function*by(a,b){if(!a.body){if(b.abort(),void 0!==globalThis.navigator&&"ReactNative"===globalThis.navigator.product)throw new aL("The default react-native fetch implementation does not support streaming. Please use expo/fetch: https://docs.expo.dev/versions/latest/sdk/expo/#expofetch-api");throw new aL("Attempted to iterate over a response with no body")}let c=new bA,d=new bo;for await(let b of bz(a9(a.body)))for(let a of d.decode(b)){let b=c.decode(a);b&&(yield b)}for(let a of d.flush()){let b=c.decode(a);b&&(yield b)}}async function*bz(a){let b=new Uint8Array;for await(let c of a){let a;if(null==c)continue;let d=c instanceof ArrayBuffer?new Uint8Array(c):"string"==typeof c?bm(c):c,e=new Uint8Array(b.length+d.length);for(e.set(b),e.set(d,b.length),b=e;-1!==(a=function(a){for(let b=0;b<a.length-1;b++){if(10===a[b]&&10===a[b+1]||13===a[b]&&13===a[b+1])return b+2;if(13===a[b]&&10===a[b+1]&&b+3<a.length&&13===a[b+2]&&10===a[b+3])return b+4}return -1}(b));)yield b.slice(0,a),b=b.slice(a)}b.length>0&&(yield b)}class bA{constructor(){this.event=null,this.data=[],this.chunks=[]}decode(a){if(a.endsWith("\r")&&(a=a.substring(0,a.length-1)),!a){if(!this.event&&!this.data.length)return null;let a={event:this.event,data:this.data.join("\n"),raw:this.chunks};return this.event=null,this.data=[],this.chunks=[],a}if(this.chunks.push(a),a.startsWith(":"))return null;let[b,c,d]=function(a,b){let c=a.indexOf(":");return -1!==c?[a.substring(0,c),b,a.substring(c+b.length)]:[a,"",""]}(a,":");return d.startsWith(" ")&&(d=d.substring(1)),"event"===b?this.event=d:"data"===b&&this.data.push(d),null}}async function bB(a,b){let{response:c,requestLogID:d,retryOfRequestLogID:e,startTime:f}=b,g=await (async()=>{if(b.options.stream)return(bv(a).debug("response",c.status,c.url,c.headers,c.body),b.options.__streamClass)?b.options.__streamClass.fromSSEResponse(c,b.controller,a):bx.fromSSEResponse(c,b.controller,a);if(204===c.status)return null;if(b.options.__binaryResponse)return c;let d=c.headers.get("content-type"),e=d?.split(";")[0]?.trim();return e?.includes("application/json")||e?.endsWith("+json")?bC(await c.json(),c):await c.text()})();return bv(a).debug(`[${d}] response parsed`,bw({retryOfRequestLogID:e,url:c.url,status:c.status,body:g,durationMs:Date.now()-f})),g}function bC(a,b){return!a||"object"!=typeof a||Array.isArray(a)?a:Object.defineProperty(a,"_request_id",{value:b.headers.get("x-request-id"),enumerable:!1})}class bD extends Promise{constructor(a,b,c=bB){super(a=>{a(null)}),this.responsePromise=b,this.parseResponse=c,k.set(this,void 0),aG(this,k,a,"f")}_thenUnwrap(a){return new bD(aH(this,k,"f"),this.responsePromise,async(b,c)=>bC(a(await this.parseResponse(b,c),c),c.response))}asResponse(){return this.responsePromise.then(a=>a.response)}async withResponse(){let[a,b]=await Promise.all([this.parse(),this.asResponse()]);return{data:a,response:b,request_id:b.headers.get("x-request-id")}}parse(){return this.parsedPromise||(this.parsedPromise=this.responsePromise.then(a=>this.parseResponse(aH(this,k,"f"),a))),this.parsedPromise}then(a,b){return this.parse().then(a,b)}catch(a){return this.parse().catch(a)}finally(a){return this.parse().finally(a)}}k=new WeakMap;class bE{constructor(a,b,c,d){l.set(this,void 0),aG(this,l,a,"f"),this.options=d,this.response=b,this.body=c}hasNextPage(){return!!this.getPaginatedItems().length&&null!=this.nextPageRequestOptions()}async getNextPage(){let a=this.nextPageRequestOptions();if(!a)throw new aL("No next page expected; please check `.hasNextPage()` before calling `.getNextPage()`.");return await aH(this,l,"f").requestAPIList(this.constructor,a)}async *iterPages(){let a=this;for(yield a;a.hasNextPage();)a=await a.getNextPage(),yield a}async *[(l=new WeakMap,Symbol.asyncIterator)](){for await(let a of this.iterPages())for(let b of a.getPaginatedItems())yield b}}class bF extends bD{constructor(a,b,c){super(a,b,async(a,b)=>new c(a,b.response,await bB(a,b),b.options))}async *[Symbol.asyncIterator](){for await(let a of(await this))yield a}}class bG extends bE{constructor(a,b,c,d){super(a,b,c,d),this.data=c.data||[],this.object=c.object}getPaginatedItems(){return this.data??[]}nextPageRequestOptions(){return null}}class bH extends bE{constructor(a,b,c,d){super(a,b,c,d),this.data=c.data||[],this.has_more=c.has_more||!1}getPaginatedItems(){return this.data??[]}hasNextPage(){return!1!==this.has_more&&super.hasNextPage()}nextPageRequestOptions(){var a;let b=this.getPaginatedItems(),c=b[b.length-1]?.id;return c?{...this.options,query:{..."object"!=typeof(a=this.options.query)?{}:a??{},after:c}}:null}}let bI=()=>{if("undefined"==typeof File){let{process:a}=globalThis;throw Error("`File` is not defined as a global, which is required for file uploads."+("string"==typeof a?.versions?.node&&20>parseInt(a.versions.node.split("."))?" Update to Node 20 LTS or newer, or set `globalThis.File` to `import('node:buffer').File`.":""))}};function bJ(a,b,c){return bI(),new File(a,b??"unknown_file",c)}function bK(a){return("object"==typeof a&&null!==a&&("name"in a&&a.name&&String(a.name)||"url"in a&&a.url&&String(a.url)||"filename"in a&&a.filename&&String(a.filename)||"path"in a&&a.path&&String(a.path))||"").split(/[\\/]/).pop()||void 0}let bL=a=>null!=a&&"object"==typeof a&&"function"==typeof a[Symbol.asyncIterator],bM=async(a,b)=>({...a,body:await bO(a.body,b)}),bN=new WeakMap,bO=async(a,b)=>{if(!await function(a){let b="function"==typeof a?a:a.fetch,c=bN.get(b);if(c)return c;let d=(async()=>{try{let a="Response"in b?b.Response:(await b("data:,")).constructor,c=new FormData;if(c.toString()===await new a(c).text())return!1;return!0}catch{return!0}})();return bN.set(b,d),d}(b))throw TypeError("The provided fetch function does not support file uploads with the current global FormData class.");let c=new FormData;return await Promise.all(Object.entries(a||{}).map(([a,b])=>bR(c,a,b))),c},bP=a=>a instanceof Blob&&"name"in a,bQ=a=>{if((a=>"object"==typeof a&&null!==a&&(a instanceof Response||bL(a)||bP(a)))(a))return!0;if(Array.isArray(a))return a.some(bQ);if(a&&"object"==typeof a){for(let b in a)if(bQ(a[b]))return!0}return!1},bR=async(a,b,c)=>{if(void 0!==c){if(null==c)throw TypeError(`Received null for "${b}"; to pass null in FormData, you must use the string 'null'`);if("string"==typeof c||"number"==typeof c||"boolean"==typeof c)a.append(b,String(c));else if(c instanceof Response)a.append(b,bJ([await c.blob()],bK(c)));else if(bL(c))a.append(b,bJ([await new Response(a8(c)).blob()],bK(c)));else if(bP(c))a.append(b,c,bK(c));else if(Array.isArray(c))await Promise.all(c.map(c=>bR(a,b+"[]",c)));else if("object"==typeof c)await Promise.all(Object.entries(c).map(([c,d])=>bR(a,`${b}[${c}]`,d)));else throw TypeError(`Invalid value given to form, expected a string, number, boolean, object, Array, File or Blob but got ${c} instead`)}},bS=a=>null!=a&&"object"==typeof a&&"number"==typeof a.size&&"string"==typeof a.type&&"function"==typeof a.text&&"function"==typeof a.slice&&"function"==typeof a.arrayBuffer;async function bT(a,b,c){let d,e;if(bI(),null!=(d=a=await a)&&"object"==typeof d&&"string"==typeof d.name&&"number"==typeof d.lastModified&&bS(d))return a instanceof File?a:bJ([await a.arrayBuffer()],a.name);if(null!=(e=a)&&"object"==typeof e&&"string"==typeof e.url&&"function"==typeof e.blob){let d=await a.blob();return b||(b=new URL(a.url).pathname.split(/[\\/]/).pop()),bJ(await bU(d),b,c)}let f=await bU(a);if(b||(b=bK(a)),!c?.type){let a=f.find(a=>"object"==typeof a&&"type"in a&&a.type);"string"==typeof a&&(c={...c,type:a})}return bJ(f,b,c)}async function bU(a){let b=[];if("string"==typeof a||ArrayBuffer.isView(a)||a instanceof ArrayBuffer)b.push(a);else if(bS(a))b.push(a instanceof Blob?a:await a.arrayBuffer());else if(bL(a))for await(let c of a)b.push(...await bU(c));else{let b=a?.constructor?.name;throw Error(`Unexpected data type: ${typeof a}${b?`; constructor: ${b}`:""}${function(a){if("object"!=typeof a||null===a)return"";let b=Object.getOwnPropertyNames(a);return`; props: [${b.map(a=>`"${a}"`).join(", ")}]`}(a)}`)}return b}class bV{constructor(a){this._client=a}}function bW(a){return a.replace(/[^A-Za-z0-9\-._~!$&'()*+,;=:@]+/g,encodeURIComponent)}let bX=Object.freeze(Object.create(null)),bY=((a=bW)=>function(b,...c){let d;if(1===b.length)return b[0];let e=!1,f=[],g=b.reduce((b,d,g)=>{/[?#]/.test(d)&&(e=!0);let h=c[g],i=(e?encodeURIComponent:a)(""+h);return g!==c.length&&(null==h||"object"==typeof h&&h.toString===Object.getPrototypeOf(Object.getPrototypeOf(h.hasOwnProperty??bX)??bX)?.toString)&&(i=h+"",f.push({start:b.length+d.length,length:i.length,error:`Value of type ${Object.prototype.toString.call(h).slice(8,-1)} is not a valid path parameter`})),b+d+(g===c.length?"":i)},""),h=g.split(/[?#]/,1)[0],i=/(?<=^|\/)(?:\.|%2e){1,2}(?=\/|$)/gi;for(;null!==(d=i.exec(h));)f.push({start:d.index,length:d[0].length,error:`Value "${d[0]}" can't be safely passed as a path parameter`});if(f.sort((a,b)=>a.start-b.start),f.length>0){let a=0,b=f.reduce((b,c)=>{let d=" ".repeat(c.start-a),e="^".repeat(c.length);return a=c.start+c.length,b+d+e},"");throw new aL(`Path parameters result in path with invalid segments:
${f.map(a=>a.error).join("\n")}
${g}
${b}`)}return g})(bW);class bZ extends bV{list(a,b={},c){return this._client.getAPIList(bY`/chat/completions/${a}/messages`,bH,{query:b,...c})}}let b$=a=>a?.role==="assistant",b_=a=>a?.role==="tool";class b0{constructor(){m.add(this),this.controller=new AbortController,n.set(this,void 0),o.set(this,()=>{}),p.set(this,()=>{}),q.set(this,void 0),r.set(this,()=>{}),s.set(this,()=>{}),t.set(this,{}),u.set(this,!1),v.set(this,!1),w.set(this,!1),x.set(this,!1),aG(this,n,new Promise((a,b)=>{aG(this,o,a,"f"),aG(this,p,b,"f")}),"f"),aG(this,q,new Promise((a,b)=>{aG(this,r,a,"f"),aG(this,s,b,"f")}),"f"),aH(this,n,"f").catch(()=>{}),aH(this,q,"f").catch(()=>{})}_run(a){setTimeout(()=>{a().then(()=>{this._emitFinal(),this._emit("end")},aH(this,m,"m",y).bind(this))},0)}_connected(){this.ended||(aH(this,o,"f").call(this),this._emit("connect"))}get ended(){return aH(this,u,"f")}get errored(){return aH(this,v,"f")}get aborted(){return aH(this,w,"f")}abort(){this.controller.abort()}on(a,b){return(aH(this,t,"f")[a]||(aH(this,t,"f")[a]=[])).push({listener:b}),this}off(a,b){let c=aH(this,t,"f")[a];if(!c)return this;let d=c.findIndex(a=>a.listener===b);return d>=0&&c.splice(d,1),this}once(a,b){return(aH(this,t,"f")[a]||(aH(this,t,"f")[a]=[])).push({listener:b,once:!0}),this}emitted(a){return new Promise((b,c)=>{aG(this,x,!0,"f"),"error"!==a&&this.once("error",c),this.once(a,b)})}async done(){aG(this,x,!0,"f"),await aH(this,q,"f")}_emit(a,...b){if(aH(this,u,"f"))return;"end"===a&&(aG(this,u,!0,"f"),aH(this,r,"f").call(this));let c=aH(this,t,"f")[a];if(c&&(aH(this,t,"f")[a]=c.filter(a=>!a.once),c.forEach(({listener:a})=>a(...b))),"abort"===a){let a=b[0];aH(this,x,"f")||c?.length||Promise.reject(a),aH(this,p,"f").call(this,a),aH(this,s,"f").call(this,a),this._emit("end");return}if("error"===a){let a=b[0];aH(this,x,"f")||c?.length||Promise.reject(a),aH(this,p,"f").call(this,a),aH(this,s,"f").call(this,a),this._emit("end")}}_emitFinal(){}}function b1(a){return a?.$brand==="auto-parseable-response-format"}function b2(a){return a?.$brand==="auto-parseable-tool"}function b3(a,b){let c=a.choices.map(a=>{var c,d;if("length"===a.finish_reason)throw new aY;if("content_filter"===a.finish_reason)throw new aZ;return{...a,message:{...a.message,...a.message.tool_calls?{tool_calls:a.message.tool_calls?.map(a=>(function(a,b){let c=a.tools?.find(a=>a.function?.name===b.function.name);return{...b,function:{...b.function,parsed_arguments:b2(c)?c.$parseRaw(b.function.arguments):c?.function.strict?JSON.parse(b.function.arguments):null}}})(b,a))??void 0}:void 0,parsed:a.message.content&&!a.message.refusal?(c=b,d=a.message.content,c.response_format?.type!=="json_schema"?null:c.response_format?.type==="json_schema"?"$parseRaw"in c.response_format?c.response_format.$parseRaw(d):JSON.parse(d):null):null}}});return{...a,choices:c}}function b4(a){return!!b1(a.response_format)||(a.tools?.some(a=>b2(a)||"function"===a.type&&!0===a.function.strict)??!1)}n=new WeakMap,o=new WeakMap,p=new WeakMap,q=new WeakMap,r=new WeakMap,s=new WeakMap,t=new WeakMap,u=new WeakMap,v=new WeakMap,w=new WeakMap,x=new WeakMap,m=new WeakSet,y=function(a){if(aG(this,v,!0,"f"),a instanceof Error&&"AbortError"===a.name&&(a=new aN),a instanceof aN)return aG(this,w,!0,"f"),this._emit("abort",a);if(a instanceof aL)return this._emit("error",a);if(a instanceof Error){let b=new aL(a.message);return b.cause=a,this._emit("error",b)}return this._emit("error",new aL(String(a)))};class b5 extends b0{constructor(){super(...arguments),z.add(this),this._chatCompletions=[],this.messages=[]}_addChatCompletion(a){this._chatCompletions.push(a),this._emit("chatCompletion",a);let b=a.choices[0]?.message;return b&&this._addMessage(b),a}_addMessage(a,b=!0){if("content"in a||(a.content=null),this.messages.push(a),b){if(this._emit("message",a),b_(a)&&a.content)this._emit("functionToolCallResult",a.content);else if(b$(a)&&a.tool_calls)for(let b of a.tool_calls)"function"===b.type&&this._emit("functionToolCall",b.function)}}async finalChatCompletion(){await this.done();let a=this._chatCompletions[this._chatCompletions.length-1];if(!a)throw new aL("stream ended without producing a ChatCompletion");return a}async finalContent(){return await this.done(),aH(this,z,"m",A).call(this)}async finalMessage(){return await this.done(),aH(this,z,"m",B).call(this)}async finalFunctionToolCall(){return await this.done(),aH(this,z,"m",C).call(this)}async finalFunctionToolCallResult(){return await this.done(),aH(this,z,"m",D).call(this)}async totalUsage(){return await this.done(),aH(this,z,"m",E).call(this)}allChatCompletions(){return[...this._chatCompletions]}_emitFinal(){let a=this._chatCompletions[this._chatCompletions.length-1];a&&this._emit("finalChatCompletion",a);let b=aH(this,z,"m",B).call(this);b&&this._emit("finalMessage",b);let c=aH(this,z,"m",A).call(this);c&&this._emit("finalContent",c);let d=aH(this,z,"m",C).call(this);d&&this._emit("finalFunctionToolCall",d);let e=aH(this,z,"m",D).call(this);null!=e&&this._emit("finalFunctionToolCallResult",e),this._chatCompletions.some(a=>a.usage)&&this._emit("totalUsage",aH(this,z,"m",E).call(this))}async _createChatCompletion(a,b,c){let d=c?.signal;d&&(d.aborted&&this.controller.abort(),d.addEventListener("abort",()=>this.controller.abort())),aH(this,z,"m",F).call(this,b);let e=await a.chat.completions.create({...b,stream:!1},{...c,signal:this.controller.signal});return this._connected(),this._addChatCompletion(b3(e,b))}async _runChatCompletion(a,b,c){for(let a of b.messages)this._addMessage(a,!1);return await this._createChatCompletion(a,b,c)}async _runTools(a,b,c){let d="tool",{tool_choice:e="auto",stream:f,...g}=b,h="string"!=typeof e&&e?.function?.name,{maxChatCompletions:i=10}=c||{},j=b.tools.map(a=>{if(b2(a)){if(!a.$callback)throw new aL("Tool given to `.runTools()` that does not have an associated function");return{type:"function",function:{function:a.$callback,name:a.function.name,description:a.function.description||"",parameters:a.function.parameters,parse:a.$parseRaw,strict:!0}}}return a}),k={};for(let a of j)"function"===a.type&&(k[a.function.name||a.function.function.name]=a.function);let l="tools"in b?j.map(a=>"function"===a.type?{type:"function",function:{name:a.function.name||a.function.function.name,parameters:a.function.parameters,description:a.function.description,strict:a.function.strict}}:a):void 0;for(let a of b.messages)this._addMessage(a,!1);for(let b=0;b<i;++b){let b=await this._createChatCompletion(a,{...g,tool_choice:e,tools:l,messages:[...this.messages]},c),f=b.choices[0]?.message;if(!f)throw new aL("missing message in ChatCompletion response");if(!f.tool_calls?.length)break;for(let a of f.tool_calls){let b;if("function"!==a.type)continue;let c=a.id,{name:e,arguments:f}=a.function,g=k[e];if(g){if(h&&h!==e){let a=`Invalid tool_call: ${JSON.stringify(e)}. ${JSON.stringify(h)} requested. Please try again`;this._addMessage({role:d,tool_call_id:c,content:a});continue}}else{let a=`Invalid tool_call: ${JSON.stringify(e)}. Available options are: ${Object.keys(k).map(a=>JSON.stringify(a)).join(", ")}. Please try again`;this._addMessage({role:d,tool_call_id:c,content:a});continue}try{b="function"==typeof g.parse?await g.parse(f):f}catch(b){let a=b instanceof Error?b.message:String(b);this._addMessage({role:d,tool_call_id:c,content:a});continue}let i=await g.function(b,this),j=aH(this,z,"m",G).call(this,i);if(this._addMessage({role:d,tool_call_id:c,content:j}),h)return}}}}z=new WeakSet,A=function(){return aH(this,z,"m",B).call(this).content??null},B=function(){let a=this.messages.length;for(;a-- >0;){let b=this.messages[a];if(b$(b))return{...b,content:b.content??null,refusal:b.refusal??null}}throw new aL("stream ended without producing a ChatCompletionMessage with role=assistant")},C=function(){for(let a=this.messages.length-1;a>=0;a--){let b=this.messages[a];if(b$(b)&&b?.tool_calls?.length)return b.tool_calls.at(-1)?.function}},D=function(){for(let a=this.messages.length-1;a>=0;a--){let b=this.messages[a];if(b_(b)&&null!=b.content&&"string"==typeof b.content&&this.messages.some(a=>"assistant"===a.role&&a.tool_calls?.some(a=>"function"===a.type&&a.id===b.tool_call_id)))return b.content}},E=function(){let a={completion_tokens:0,prompt_tokens:0,total_tokens:0};for(let{usage:b}of this._chatCompletions)b&&(a.completion_tokens+=b.completion_tokens,a.prompt_tokens+=b.prompt_tokens,a.total_tokens+=b.total_tokens);return a},F=function(a){if(null!=a.n&&a.n>1)throw new aL("ChatCompletion convenience helpers only support n=1 at this time. To use n>1, please use chat.completions.create() directly.")},G=function(a){return"string"==typeof a?a:void 0===a?"undefined":JSON.stringify(a)};class b6 extends b5{static runTools(a,b,c){let d=new b6,e={...c,headers:{...c?.headers,"X-Stainless-Helper-Method":"runTools"}};return d._run(()=>d._runTools(a,b,e)),d}_addMessage(a,b=!0){super._addMessage(a,b),b$(a)&&a.content&&this._emit("content",a.content)}}let b7={STR:1,NUM:2,ARR:4,OBJ:8,NULL:16,BOOL:32,NAN:64,INFINITY:128,MINUS_INFINITY:256,INF:384,ALL:511};class b8 extends Error{}class b9 extends Error{}let ca=a=>(function(a,b=b7.ALL){if("string"!=typeof a)throw TypeError(`expecting str, got ${typeof a}`);if(!a.trim())throw Error(`${a} is empty`);return((a,b)=>{let c=a.length,d=0,e=a=>{throw new b8(`${a} at position ${d}`)},f=a=>{throw new b9(`${a} at position ${d}`)},g=()=>(l(),d>=c&&e("Unexpected end of input"),'"'===a[d])?h():"{"===a[d]?i():"["===a[d]?j():"null"===a.substring(d,d+4)||b7.NULL&b&&c-d<4&&"null".startsWith(a.substring(d))?(d+=4,null):"true"===a.substring(d,d+4)||b7.BOOL&b&&c-d<4&&"true".startsWith(a.substring(d))?(d+=4,!0):"false"===a.substring(d,d+5)||b7.BOOL&b&&c-d<5&&"false".startsWith(a.substring(d))?(d+=5,!1):"Infinity"===a.substring(d,d+8)||b7.INFINITY&b&&c-d<8&&"Infinity".startsWith(a.substring(d))?(d+=8,1/0):"-Infinity"===a.substring(d,d+9)||b7.MINUS_INFINITY&b&&1<c-d&&c-d<9&&"-Infinity".startsWith(a.substring(d))?(d+=9,-1/0):"NaN"===a.substring(d,d+3)||b7.NAN&b&&c-d<3&&"NaN".startsWith(a.substring(d))?(d+=3,NaN):k(),h=()=>{let g=d,h=!1;for(d++;d<c&&('"'!==a[d]||h&&"\\"===a[d-1]);)h="\\"===a[d]&&!h,d++;if('"'==a.charAt(d))try{return JSON.parse(a.substring(g,++d-Number(h)))}catch(a){f(String(a))}else if(b7.STR&b)try{return JSON.parse(a.substring(g,d-Number(h))+'"')}catch(b){return JSON.parse(a.substring(g,a.lastIndexOf("\\"))+'"')}e("Unterminated string literal")},i=()=>{d++,l();let f={};try{for(;"}"!==a[d];){if(l(),d>=c&&b7.OBJ&b)return f;let e=h();l(),d++;try{let a=g();Object.defineProperty(f,e,{value:a,writable:!0,enumerable:!0,configurable:!0})}catch(a){if(b7.OBJ&b)return f;throw a}l(),","===a[d]&&d++}}catch(a){if(b7.OBJ&b)return f;e("Expected '}' at end of object")}return d++,f},j=()=>{d++;let c=[];try{for(;"]"!==a[d];)c.push(g()),l(),","===a[d]&&d++}catch(a){if(b7.ARR&b)return c;e("Expected ']' at end of array")}return d++,c},k=()=>{if(0===d){"-"===a&&b7.NUM&b&&e("Not sure what '-' is");try{return JSON.parse(a)}catch(c){if(b7.NUM&b)try{if("."===a[a.length-1])return JSON.parse(a.substring(0,a.lastIndexOf(".")));return JSON.parse(a.substring(0,a.lastIndexOf("e")))}catch(a){}f(String(c))}}let g=d;for("-"===a[d]&&d++;a[d]&&!",]}".includes(a[d]);)d++;d!=c||b7.NUM&b||e("Unterminated number literal");try{return JSON.parse(a.substring(g,d))}catch(c){"-"===a.substring(g,d)&&b7.NUM&b&&e("Not sure what '-' is");try{return JSON.parse(a.substring(g,a.lastIndexOf("e")))}catch(a){f(String(a))}}},l=()=>{for(;d<c&&" \n\r	".includes(a[d]);)d++};return g()})(a.trim(),b)})(a,b7.ALL^b7.NUM);class cb extends b5{constructor(a){super(),H.add(this),I.set(this,void 0),J.set(this,void 0),K.set(this,void 0),aG(this,I,a,"f"),aG(this,J,[],"f")}get currentChatCompletionSnapshot(){return aH(this,K,"f")}static fromReadableStream(a){let b=new cb(null);return b._run(()=>b._fromReadableStream(a)),b}static createChatCompletion(a,b,c){let d=new cb(b);return d._run(()=>d._runChatCompletion(a,{...b,stream:!0},{...c,headers:{...c?.headers,"X-Stainless-Helper-Method":"stream"}})),d}async _createChatCompletion(a,b,c){super._createChatCompletion;let d=c?.signal;d&&(d.aborted&&this.controller.abort(),d.addEventListener("abort",()=>this.controller.abort())),aH(this,H,"m",L).call(this);let e=await a.chat.completions.create({...b,stream:!0},{...c,signal:this.controller.signal});for await(let a of(this._connected(),e))aH(this,H,"m",N).call(this,a);if(e.controller.signal?.aborted)throw new aN;return this._addChatCompletion(aH(this,H,"m",Q).call(this))}async _fromReadableStream(a,b){let c,d=b?.signal;d&&(d.aborted&&this.controller.abort(),d.addEventListener("abort",()=>this.controller.abort())),aH(this,H,"m",L).call(this),this._connected();let e=bx.fromReadableStream(a,this.controller);for await(let a of e)c&&c!==a.id&&this._addChatCompletion(aH(this,H,"m",Q).call(this)),aH(this,H,"m",N).call(this,a),c=a.id;if(e.controller.signal?.aborted)throw new aN;return this._addChatCompletion(aH(this,H,"m",Q).call(this))}[(I=new WeakMap,J=new WeakMap,K=new WeakMap,H=new WeakSet,L=function(){this.ended||aG(this,K,void 0,"f")},M=function(a){let b=aH(this,J,"f")[a.index];return b||(b={content_done:!1,refusal_done:!1,logprobs_content_done:!1,logprobs_refusal_done:!1,done_tool_calls:new Set,current_tool_call_index:null},aH(this,J,"f")[a.index]=b),b},N=function(a){if(this.ended)return;let b=aH(this,H,"m",S).call(this,a);for(let c of(this._emit("chunk",a,b),a.choices)){let a=b.choices[c.index];null!=c.delta.content&&a.message?.role==="assistant"&&a.message?.content&&(this._emit("content",c.delta.content,a.message.content),this._emit("content.delta",{delta:c.delta.content,snapshot:a.message.content,parsed:a.message.parsed})),null!=c.delta.refusal&&a.message?.role==="assistant"&&a.message?.refusal&&this._emit("refusal.delta",{delta:c.delta.refusal,snapshot:a.message.refusal}),c.logprobs?.content!=null&&a.message?.role==="assistant"&&this._emit("logprobs.content.delta",{content:c.logprobs?.content,snapshot:a.logprobs?.content??[]}),c.logprobs?.refusal!=null&&a.message?.role==="assistant"&&this._emit("logprobs.refusal.delta",{refusal:c.logprobs?.refusal,snapshot:a.logprobs?.refusal??[]});let d=aH(this,H,"m",M).call(this,a);for(let b of(a.finish_reason&&(aH(this,H,"m",P).call(this,a),null!=d.current_tool_call_index&&aH(this,H,"m",O).call(this,a,d.current_tool_call_index)),c.delta.tool_calls??[]))d.current_tool_call_index!==b.index&&(aH(this,H,"m",P).call(this,a),null!=d.current_tool_call_index&&aH(this,H,"m",O).call(this,a,d.current_tool_call_index)),d.current_tool_call_index=b.index;for(let b of c.delta.tool_calls??[]){let c=a.message.tool_calls?.[b.index];c?.type&&(c?.type==="function"?this._emit("tool_calls.function.arguments.delta",{name:c.function?.name,index:b.index,arguments:c.function.arguments,parsed_arguments:c.function.parsed_arguments,arguments_delta:b.function?.arguments??""}):c?.type)}}},O=function(a,b){if(aH(this,H,"m",M).call(this,a).done_tool_calls.has(b))return;let c=a.message.tool_calls?.[b];if(!c)throw Error("no tool call snapshot");if(!c.type)throw Error("tool call snapshot missing `type`");if("function"===c.type){let a=aH(this,I,"f")?.tools?.find(a=>"function"===a.type&&a.function.name===c.function.name);this._emit("tool_calls.function.arguments.done",{name:c.function.name,index:b,arguments:c.function.arguments,parsed_arguments:b2(a)?a.$parseRaw(c.function.arguments):a?.function.strict?JSON.parse(c.function.arguments):null})}else c.type},P=function(a){let b=aH(this,H,"m",M).call(this,a);if(a.message.content&&!b.content_done){b.content_done=!0;let c=aH(this,H,"m",R).call(this);this._emit("content.done",{content:a.message.content,parsed:c?c.$parseRaw(a.message.content):null})}a.message.refusal&&!b.refusal_done&&(b.refusal_done=!0,this._emit("refusal.done",{refusal:a.message.refusal})),a.logprobs?.content&&!b.logprobs_content_done&&(b.logprobs_content_done=!0,this._emit("logprobs.content.done",{content:a.logprobs.content})),a.logprobs?.refusal&&!b.logprobs_refusal_done&&(b.logprobs_refusal_done=!0,this._emit("logprobs.refusal.done",{refusal:a.logprobs.refusal}))},Q=function(){if(this.ended)throw new aL("stream has ended, this shouldn't happen");let a=aH(this,K,"f");if(!a)throw new aL("request ended without sending any chunks");return aG(this,K,void 0,"f"),aG(this,J,[],"f"),function(a,b){var c;let{id:d,choices:e,created:f,model:g,system_fingerprint:h,...i}=a;return c={...i,id:d,choices:e.map(({message:b,finish_reason:c,index:d,logprobs:e,...f})=>{if(!c)throw new aL(`missing finish_reason for choice ${d}`);let{content:g=null,function_call:h,tool_calls:i,...j}=b,k=b.role;if(!k)throw new aL(`missing role for choice ${d}`);if(h){let{arguments:a,name:i}=h;if(null==a)throw new aL(`missing function_call.arguments for choice ${d}`);if(!i)throw new aL(`missing function_call.name for choice ${d}`);return{...f,message:{content:g,function_call:{arguments:a,name:i},role:k,refusal:b.refusal??null},finish_reason:c,index:d,logprobs:e}}return i?{...f,index:d,finish_reason:c,logprobs:e,message:{...j,role:k,content:g,refusal:b.refusal??null,tool_calls:i.map((b,c)=>{let{function:e,type:f,id:g,...h}=b,{arguments:i,name:j,...k}=e||{};if(null==g)throw new aL(`missing choices[${d}].tool_calls[${c}].id
${cc(a)}`);if(null==f)throw new aL(`missing choices[${d}].tool_calls[${c}].type
${cc(a)}`);if(null==j)throw new aL(`missing choices[${d}].tool_calls[${c}].function.name
${cc(a)}`);if(null==i)throw new aL(`missing choices[${d}].tool_calls[${c}].function.arguments
${cc(a)}`);return{...h,id:g,type:f,function:{...k,name:j,arguments:i}}})}}:{...f,message:{...j,content:g,role:k,refusal:b.refusal??null},finish_reason:c,index:d,logprobs:e}}),created:f,model:g,object:"chat.completion",...h?{system_fingerprint:h}:{}},b&&b4(b)?b3(c,b):{...c,choices:c.choices.map(a=>({...a,message:{...a.message,parsed:null,...a.message.tool_calls?{tool_calls:a.message.tool_calls}:void 0}}))}}(a,aH(this,I,"f"))},R=function(){let a=aH(this,I,"f")?.response_format;return b1(a)?a:null},S=function(a){var b,c,d,e;let f=aH(this,K,"f"),{choices:g,...h}=a;for(let{delta:g,finish_reason:i,index:j,logprobs:k=null,...l}of(f?Object.assign(f,h):f=aG(this,K,{...h,choices:[]},"f"),a.choices)){let a=f.choices[j];if(a||(a=f.choices[j]={finish_reason:i,index:j,message:{},logprobs:k,...l}),k)if(a.logprobs){let{content:d,refusal:e,...f}=k;Object.assign(a.logprobs,f),d&&((b=a.logprobs).content??(b.content=[]),a.logprobs.content.push(...d)),e&&((c=a.logprobs).refusal??(c.refusal=[]),a.logprobs.refusal.push(...e))}else a.logprobs=Object.assign({},k);if(i&&(a.finish_reason=i,aH(this,I,"f")&&b4(aH(this,I,"f")))){if("length"===i)throw new aY;if("content_filter"===i)throw new aZ}if(Object.assign(a,l),!g)continue;let{content:h,refusal:m,function_call:n,role:o,tool_calls:p,...q}=g;if(Object.assign(a.message,q),m&&(a.message.refusal=(a.message.refusal||"")+m),o&&(a.message.role=o),n&&(a.message.function_call?(n.name&&(a.message.function_call.name=n.name),n.arguments&&((d=a.message.function_call).arguments??(d.arguments=""),a.message.function_call.arguments+=n.arguments)):a.message.function_call=n),h&&(a.message.content=(a.message.content||"")+h,!a.message.refusal&&aH(this,H,"m",R).call(this)&&(a.message.parsed=ca(a.message.content))),p)for(let{index:b,id:c,type:d,function:f,...g}of(a.message.tool_calls||(a.message.tool_calls=[]),p)){let h=(e=a.message.tool_calls)[b]??(e[b]={});Object.assign(h,g),c&&(h.id=c),d&&(h.type=d),f&&(h.function??(h.function={name:f.name??"",arguments:""})),f?.name&&(h.function.name=f.name),f?.arguments&&(h.function.arguments+=f.arguments,function(a,b){if(!a)return!1;let c=a.tools?.find(a=>a.function?.name===b.function.name);return b2(c)||c?.function.strict||!1}(aH(this,I,"f"),h)&&(h.function.parsed_arguments=ca(h.function.arguments)))}}return f},Symbol.asyncIterator)](){let a=[],b=[],c=!1;return this.on("chunk",c=>{let d=b.shift();d?d.resolve(c):a.push(c)}),this.on("end",()=>{for(let a of(c=!0,b))a.resolve(void 0);b.length=0}),this.on("abort",a=>{for(let d of(c=!0,b))d.reject(a);b.length=0}),this.on("error",a=>{for(let d of(c=!0,b))d.reject(a);b.length=0}),{next:async()=>a.length?{value:a.shift(),done:!1}:c?{value:void 0,done:!0}:new Promise((a,c)=>b.push({resolve:a,reject:c})).then(a=>a?{value:a,done:!1}:{value:void 0,done:!0}),return:async()=>(this.abort(),{value:void 0,done:!0})}}toReadableStream(){return new bx(this[Symbol.asyncIterator].bind(this),this.controller).toReadableStream()}}function cc(a){return JSON.stringify(a)}class cd extends cb{static fromReadableStream(a){let b=new cd(null);return b._run(()=>b._fromReadableStream(a)),b}static runTools(a,b,c){let d=new cd(b),e={...c,headers:{...c?.headers,"X-Stainless-Helper-Method":"runTools"}};return d._run(()=>d._runTools(a,b,e)),d}}class ce extends bV{constructor(){super(...arguments),this.messages=new bZ(this._client)}create(a,b){return this._client.post("/chat/completions",{body:a,...b,stream:a.stream??!1})}retrieve(a,b){return this._client.get(bY`/chat/completions/${a}`,b)}update(a,b,c){return this._client.post(bY`/chat/completions/${a}`,{body:b,...c})}list(a={},b){return this._client.getAPIList("/chat/completions",bH,{query:a,...b})}delete(a,b){return this._client.delete(bY`/chat/completions/${a}`,b)}parse(a,b){for(let b of a.tools??[]){if("function"!==b.type)throw new aL(`Currently only \`function\` tool types support auto-parsing; Received \`${b.type}\``);if(!0!==b.function.strict)throw new aL(`The \`${b.function.name}\` tool is not marked with \`strict: true\`. Only strict function tools can be auto-parsed`)}return this._client.chat.completions.create(a,{...b,headers:{...b?.headers,"X-Stainless-Helper-Method":"chat.completions.parse"}})._thenUnwrap(b=>b3(b,a))}runTools(a,b){return a.stream?cd.runTools(this._client,a,b):b6.runTools(this._client,a,b)}stream(a,b){return cb.createChatCompletion(this._client,a,b)}}ce.Messages=bZ;class cf extends bV{constructor(){super(...arguments),this.completions=new ce(this._client)}}cf.Completions=ce;let cg=Symbol("brand.privateNullableHeaders"),ch=a=>{let b=new Headers,c=new Set;for(let d of a){let a=new Set;for(let[e,f]of function*(a){let b;if(!a)return;if(cg in a){let{values:b,nulls:c}=a;for(let a of(yield*b.entries(),c))yield[a,null];return}let c=!1;for(let d of(a instanceof Headers?b=a.entries():a1(a)?b=a:(c=!0,b=Object.entries(a??{})),b)){let a=d[0];if("string"!=typeof a)throw TypeError("expected header name to be a string");let b=a1(d[1])?d[1]:[d[1]],e=!1;for(let d of b)void 0!==d&&(c&&!e&&(e=!0,yield[a,null]),yield[a,d])}}(d)){let d=e.toLowerCase();a.has(d)||(b.delete(e),a.add(d)),null===f?(b.delete(e),c.add(d)):(b.append(e,f),c.delete(d))}}return{[cg]:!0,values:b,nulls:c}};class ci extends bV{create(a,b){return this._client.post("/audio/speech",{body:a,...b,headers:ch([{Accept:"application/octet-stream"},b?.headers]),__binaryResponse:!0})}}class cj extends bV{create(a,b){return this._client.post("/audio/transcriptions",bM({body:a,...b,stream:a.stream??!1,__metadata:{model:a.model}},this._client))}}class ck extends bV{create(a,b){return this._client.post("/audio/translations",bM({body:a,...b,__metadata:{model:a.model}},this._client))}}class cl extends bV{constructor(){super(...arguments),this.transcriptions=new cj(this._client),this.translations=new ck(this._client),this.speech=new ci(this._client)}}cl.Transcriptions=cj,cl.Translations=ck,cl.Speech=ci;class cm extends bV{create(a,b){return this._client.post("/batches",{body:a,...b})}retrieve(a,b){return this._client.get(bY`/batches/${a}`,b)}list(a={},b){return this._client.getAPIList("/batches",bH,{query:a,...b})}cancel(a,b){return this._client.post(bY`/batches/${a}/cancel`,b)}}class cn extends bV{create(a,b){return this._client.post("/assistants",{body:a,...b,headers:ch([{"OpenAI-Beta":"assistants=v2"},b?.headers])})}retrieve(a,b){return this._client.get(bY`/assistants/${a}`,{...b,headers:ch([{"OpenAI-Beta":"assistants=v2"},b?.headers])})}update(a,b,c){return this._client.post(bY`/assistants/${a}`,{body:b,...c,headers:ch([{"OpenAI-Beta":"assistants=v2"},c?.headers])})}list(a={},b){return this._client.getAPIList("/assistants",bH,{query:a,...b,headers:ch([{"OpenAI-Beta":"assistants=v2"},b?.headers])})}delete(a,b){return this._client.delete(bY`/assistants/${a}`,{...b,headers:ch([{"OpenAI-Beta":"assistants=v2"},b?.headers])})}}class co extends bV{create(a,b){return this._client.post("/realtime/sessions",{body:a,...b,headers:ch([{"OpenAI-Beta":"assistants=v2"},b?.headers])})}}class cp extends bV{create(a,b){return this._client.post("/realtime/transcription_sessions",{body:a,...b,headers:ch([{"OpenAI-Beta":"assistants=v2"},b?.headers])})}}class cq extends bV{constructor(){super(...arguments),this.sessions=new co(this._client),this.transcriptionSessions=new cp(this._client)}}cq.Sessions=co,cq.TranscriptionSessions=cp;class cr extends bV{create(a,b,c){return this._client.post(bY`/threads/${a}/messages`,{body:b,...c,headers:ch([{"OpenAI-Beta":"assistants=v2"},c?.headers])})}retrieve(a,b,c){let{thread_id:d}=b;return this._client.get(bY`/threads/${d}/messages/${a}`,{...c,headers:ch([{"OpenAI-Beta":"assistants=v2"},c?.headers])})}update(a,b,c){let{thread_id:d,...e}=b;return this._client.post(bY`/threads/${d}/messages/${a}`,{body:e,...c,headers:ch([{"OpenAI-Beta":"assistants=v2"},c?.headers])})}list(a,b={},c){return this._client.getAPIList(bY`/threads/${a}/messages`,bH,{query:b,...c,headers:ch([{"OpenAI-Beta":"assistants=v2"},c?.headers])})}delete(a,b,c){let{thread_id:d}=b;return this._client.delete(bY`/threads/${d}/messages/${a}`,{...c,headers:ch([{"OpenAI-Beta":"assistants=v2"},c?.headers])})}}class cs extends bV{retrieve(a,b,c){let{thread_id:d,run_id:e,...f}=b;return this._client.get(bY`/threads/${d}/runs/${e}/steps/${a}`,{query:f,...c,headers:ch([{"OpenAI-Beta":"assistants=v2"},c?.headers])})}list(a,b,c){let{thread_id:d,...e}=b;return this._client.getAPIList(bY`/threads/${d}/runs/${a}/steps`,bH,{query:e,...c,headers:ch([{"OpenAI-Beta":"assistants=v2"},c?.headers])})}}let ct=a=>void 0!==globalThis.process?globalThis.process.env?.[a]?.trim()??void 0:void 0!==globalThis.Deno?globalThis.Deno.env?.get?.(a)?.trim():void 0;class cu extends b0{constructor(){super(...arguments),T.add(this),V.set(this,[]),W.set(this,{}),X.set(this,{}),Y.set(this,void 0),Z.set(this,void 0),$.set(this,void 0),_.set(this,void 0),aa.set(this,void 0),ab.set(this,void 0),ac.set(this,void 0),ad.set(this,void 0),ae.set(this,void 0)}[(V=new WeakMap,W=new WeakMap,X=new WeakMap,Y=new WeakMap,Z=new WeakMap,$=new WeakMap,_=new WeakMap,aa=new WeakMap,ab=new WeakMap,ac=new WeakMap,ad=new WeakMap,ae=new WeakMap,T=new WeakSet,Symbol.asyncIterator)](){let a=[],b=[],c=!1;return this.on("event",c=>{let d=b.shift();d?d.resolve(c):a.push(c)}),this.on("end",()=>{for(let a of(c=!0,b))a.resolve(void 0);b.length=0}),this.on("abort",a=>{for(let d of(c=!0,b))d.reject(a);b.length=0}),this.on("error",a=>{for(let d of(c=!0,b))d.reject(a);b.length=0}),{next:async()=>a.length?{value:a.shift(),done:!1}:c?{value:void 0,done:!0}:new Promise((a,c)=>b.push({resolve:a,reject:c})).then(a=>a?{value:a,done:!1}:{value:void 0,done:!0}),return:async()=>(this.abort(),{value:void 0,done:!0})}}static fromReadableStream(a){let b=new U;return b._run(()=>b._fromReadableStream(a)),b}async _fromReadableStream(a,b){let c=b?.signal;c&&(c.aborted&&this.controller.abort(),c.addEventListener("abort",()=>this.controller.abort())),this._connected();let d=bx.fromReadableStream(a,this.controller);for await(let a of d)aH(this,T,"m",af).call(this,a);if(d.controller.signal?.aborted)throw new aN;return this._addRun(aH(this,T,"m",ag).call(this))}toReadableStream(){return new bx(this[Symbol.asyncIterator].bind(this),this.controller).toReadableStream()}static createToolAssistantStream(a,b,c,d){let e=new U;return e._run(()=>e._runToolAssistantStream(a,b,c,{...d,headers:{...d?.headers,"X-Stainless-Helper-Method":"stream"}})),e}async _createToolAssistantStream(a,b,c,d){let e=d?.signal;e&&(e.aborted&&this.controller.abort(),e.addEventListener("abort",()=>this.controller.abort()));let f={...c,stream:!0},g=await a.submitToolOutputs(b,f,{...d,signal:this.controller.signal});for await(let a of(this._connected(),g))aH(this,T,"m",af).call(this,a);if(g.controller.signal?.aborted)throw new aN;return this._addRun(aH(this,T,"m",ag).call(this))}static createThreadAssistantStream(a,b,c){let d=new U;return d._run(()=>d._threadAssistantStream(a,b,{...c,headers:{...c?.headers,"X-Stainless-Helper-Method":"stream"}})),d}static createAssistantStream(a,b,c,d){let e=new U;return e._run(()=>e._runAssistantStream(a,b,c,{...d,headers:{...d?.headers,"X-Stainless-Helper-Method":"stream"}})),e}currentEvent(){return aH(this,ac,"f")}currentRun(){return aH(this,ad,"f")}currentMessageSnapshot(){return aH(this,Y,"f")}currentRunStepSnapshot(){return aH(this,ae,"f")}async finalRunSteps(){return await this.done(),Object.values(aH(this,W,"f"))}async finalMessages(){return await this.done(),Object.values(aH(this,X,"f"))}async finalRun(){if(await this.done(),!aH(this,Z,"f"))throw Error("Final run was not received.");return aH(this,Z,"f")}async _createThreadAssistantStream(a,b,c){let d=c?.signal;d&&(d.aborted&&this.controller.abort(),d.addEventListener("abort",()=>this.controller.abort()));let e={...b,stream:!0},f=await a.createAndRun(e,{...c,signal:this.controller.signal});for await(let a of(this._connected(),f))aH(this,T,"m",af).call(this,a);if(f.controller.signal?.aborted)throw new aN;return this._addRun(aH(this,T,"m",ag).call(this))}async _createAssistantStream(a,b,c,d){let e=d?.signal;e&&(e.aborted&&this.controller.abort(),e.addEventListener("abort",()=>this.controller.abort()));let f={...c,stream:!0},g=await a.create(b,f,{...d,signal:this.controller.signal});for await(let a of(this._connected(),g))aH(this,T,"m",af).call(this,a);if(g.controller.signal?.aborted)throw new aN;return this._addRun(aH(this,T,"m",ag).call(this))}static accumulateDelta(a,b){for(let[c,d]of Object.entries(b)){if(!a.hasOwnProperty(c)){a[c]=d;continue}let b=a[c];if(null==b||"index"===c||"type"===c){a[c]=d;continue}if("string"==typeof b&&"string"==typeof d)b+=d;else if("number"==typeof b&&"number"==typeof d)b+=d;else if(a2(b)&&a2(d))b=this.accumulateDelta(b,d);else if(Array.isArray(b)&&Array.isArray(d)){if(b.every(a=>"string"==typeof a||"number"==typeof a)){b.push(...d);continue}for(let a of d){if(!a2(a))throw Error(`Expected array delta entry to be an object but got: ${a}`);let c=a.index;if(null==c)throw console.error(a),Error("Expected array delta entry to have an `index` property");if("number"!=typeof c)throw Error(`Expected array delta entry \`index\` property to be a number but got ${c}`);let d=b[c];null==d?b.push(a):b[c]=this.accumulateDelta(d,a)}continue}else throw Error(`Unhandled record type: ${c}, deltaValue: ${d}, accValue: ${b}`);a[c]=b}return a}_addRun(a){return a}async _threadAssistantStream(a,b,c){return await this._createThreadAssistantStream(b,a,c)}async _runAssistantStream(a,b,c,d){return await this._createAssistantStream(b,a,c,d)}async _runToolAssistantStream(a,b,c,d){return await this._createToolAssistantStream(b,a,c,d)}}U=cu,af=function(a){if(!this.ended)switch(aG(this,ac,a,"f"),aH(this,T,"m",aj).call(this,a),a.event){case"thread.created":break;case"thread.run.created":case"thread.run.queued":case"thread.run.in_progress":case"thread.run.requires_action":case"thread.run.completed":case"thread.run.incomplete":case"thread.run.failed":case"thread.run.cancelling":case"thread.run.cancelled":case"thread.run.expired":aH(this,T,"m",an).call(this,a);break;case"thread.run.step.created":case"thread.run.step.in_progress":case"thread.run.step.delta":case"thread.run.step.completed":case"thread.run.step.failed":case"thread.run.step.cancelled":case"thread.run.step.expired":aH(this,T,"m",ai).call(this,a);break;case"thread.message.created":case"thread.message.in_progress":case"thread.message.delta":case"thread.message.completed":case"thread.message.incomplete":aH(this,T,"m",ah).call(this,a);break;case"error":throw Error("Encountered an error event in event processing - errors should be processed earlier")}},ag=function(){if(this.ended)throw new aL("stream has ended, this shouldn't happen");if(!aH(this,Z,"f"))throw Error("Final run has not been received");return aH(this,Z,"f")},ah=function(a){let[b,c]=aH(this,T,"m",al).call(this,a,aH(this,Y,"f"));for(let a of(aG(this,Y,b,"f"),aH(this,X,"f")[b.id]=b,c)){let c=b.content[a.index];c?.type=="text"&&this._emit("textCreated",c.text)}switch(a.event){case"thread.message.created":this._emit("messageCreated",a.data);break;case"thread.message.in_progress":break;case"thread.message.delta":if(this._emit("messageDelta",a.data.delta,b),a.data.delta.content)for(let c of a.data.delta.content){if("text"==c.type&&c.text){let a=c.text,d=b.content[c.index];if(d&&"text"==d.type)this._emit("textDelta",a,d.text);else throw Error("The snapshot associated with this text delta is not text or missing")}if(c.index!=aH(this,$,"f")){if(aH(this,_,"f"))switch(aH(this,_,"f").type){case"text":this._emit("textDone",aH(this,_,"f").text,aH(this,Y,"f"));break;case"image_file":this._emit("imageFileDone",aH(this,_,"f").image_file,aH(this,Y,"f"))}aG(this,$,c.index,"f")}aG(this,_,b.content[c.index],"f")}break;case"thread.message.completed":case"thread.message.incomplete":if(void 0!==aH(this,$,"f")){let b=a.data.content[aH(this,$,"f")];if(b)switch(b.type){case"image_file":this._emit("imageFileDone",b.image_file,aH(this,Y,"f"));break;case"text":this._emit("textDone",b.text,aH(this,Y,"f"))}}aH(this,Y,"f")&&this._emit("messageDone",a.data),aG(this,Y,void 0,"f")}},ai=function(a){let b=aH(this,T,"m",ak).call(this,a);switch(aG(this,ae,b,"f"),a.event){case"thread.run.step.created":this._emit("runStepCreated",a.data);break;case"thread.run.step.delta":let c=a.data.delta;if(c.step_details&&"tool_calls"==c.step_details.type&&c.step_details.tool_calls&&"tool_calls"==b.step_details.type)for(let a of c.step_details.tool_calls)a.index==aH(this,aa,"f")?this._emit("toolCallDelta",a,b.step_details.tool_calls[a.index]):(aH(this,ab,"f")&&this._emit("toolCallDone",aH(this,ab,"f")),aG(this,aa,a.index,"f"),aG(this,ab,b.step_details.tool_calls[a.index],"f"),aH(this,ab,"f")&&this._emit("toolCallCreated",aH(this,ab,"f")));this._emit("runStepDelta",a.data.delta,b);break;case"thread.run.step.completed":case"thread.run.step.failed":case"thread.run.step.cancelled":case"thread.run.step.expired":aG(this,ae,void 0,"f"),"tool_calls"==a.data.step_details.type&&aH(this,ab,"f")&&(this._emit("toolCallDone",aH(this,ab,"f")),aG(this,ab,void 0,"f")),this._emit("runStepDone",a.data,b)}},aj=function(a){aH(this,V,"f").push(a),this._emit("event",a)},ak=function(a){switch(a.event){case"thread.run.step.created":return aH(this,W,"f")[a.data.id]=a.data,a.data;case"thread.run.step.delta":let b=aH(this,W,"f")[a.data.id];if(!b)throw Error("Received a RunStepDelta before creation of a snapshot");let c=a.data;if(c.delta){let d=U.accumulateDelta(b,c.delta);aH(this,W,"f")[a.data.id]=d}return aH(this,W,"f")[a.data.id];case"thread.run.step.completed":case"thread.run.step.failed":case"thread.run.step.cancelled":case"thread.run.step.expired":case"thread.run.step.in_progress":aH(this,W,"f")[a.data.id]=a.data}if(aH(this,W,"f")[a.data.id])return aH(this,W,"f")[a.data.id];throw Error("No snapshot available")},al=function(a,b){let c=[];switch(a.event){case"thread.message.created":return[a.data,c];case"thread.message.delta":if(!b)throw Error("Received a delta with no existing snapshot (there should be one from message creation)");let d=a.data;if(d.delta.content)for(let a of d.delta.content)if(a.index in b.content){let c=b.content[a.index];b.content[a.index]=aH(this,T,"m",am).call(this,a,c)}else b.content[a.index]=a,c.push(a);return[b,c];case"thread.message.in_progress":case"thread.message.completed":case"thread.message.incomplete":if(b)return[b,c];throw Error("Received thread message event with no existing snapshot")}throw Error("Tried to accumulate a non-message event")},am=function(a,b){return U.accumulateDelta(b,a)},an=function(a){switch(aG(this,ad,a.data,"f"),a.event){case"thread.run.created":case"thread.run.queued":case"thread.run.in_progress":break;case"thread.run.requires_action":case"thread.run.cancelled":case"thread.run.failed":case"thread.run.completed":case"thread.run.expired":case"thread.run.incomplete":aG(this,Z,a.data,"f"),aH(this,ab,"f")&&(this._emit("toolCallDone",aH(this,ab,"f")),aG(this,ab,void 0,"f"))}};class cv extends bV{constructor(){super(...arguments),this.steps=new cs(this._client)}create(a,b,c){let{include:d,...e}=b;return this._client.post(bY`/threads/${a}/runs`,{query:{include:d},body:e,...c,headers:ch([{"OpenAI-Beta":"assistants=v2"},c?.headers]),stream:b.stream??!1})}retrieve(a,b,c){let{thread_id:d}=b;return this._client.get(bY`/threads/${d}/runs/${a}`,{...c,headers:ch([{"OpenAI-Beta":"assistants=v2"},c?.headers])})}update(a,b,c){let{thread_id:d,...e}=b;return this._client.post(bY`/threads/${d}/runs/${a}`,{body:e,...c,headers:ch([{"OpenAI-Beta":"assistants=v2"},c?.headers])})}list(a,b={},c){return this._client.getAPIList(bY`/threads/${a}/runs`,bH,{query:b,...c,headers:ch([{"OpenAI-Beta":"assistants=v2"},c?.headers])})}cancel(a,b,c){let{thread_id:d}=b;return this._client.post(bY`/threads/${d}/runs/${a}/cancel`,{...c,headers:ch([{"OpenAI-Beta":"assistants=v2"},c?.headers])})}async createAndPoll(a,b,c){let d=await this.create(a,b,c);return await this.poll(d.id,{thread_id:a},c)}createAndStream(a,b,c){return cu.createAssistantStream(a,this._client.beta.threads.runs,b,c)}async poll(a,b,c){let d=ch([c?.headers,{"X-Stainless-Poll-Helper":"true","X-Stainless-Custom-Poll-Interval":c?.pollIntervalMs?.toString()??void 0}]);for(;;){let{data:e,response:f}=await this.retrieve(a,b,{...c,headers:{...c?.headers,...d}}).withResponse();switch(e.status){case"queued":case"in_progress":case"cancelling":let g=5e3;if(c?.pollIntervalMs)g=c.pollIntervalMs;else{let a=f.headers.get("openai-poll-after-ms");if(a){let b=parseInt(a);isNaN(b)||(g=b)}}await a3(g);break;case"requires_action":case"incomplete":case"cancelled":case"completed":case"failed":case"expired":return e}}}stream(a,b,c){return cu.createAssistantStream(a,this._client.beta.threads.runs,b,c)}submitToolOutputs(a,b,c){let{thread_id:d,...e}=b;return this._client.post(bY`/threads/${d}/runs/${a}/submit_tool_outputs`,{body:e,...c,headers:ch([{"OpenAI-Beta":"assistants=v2"},c?.headers]),stream:b.stream??!1})}async submitToolOutputsAndPoll(a,b,c){let d=await this.submitToolOutputs(a,b,c);return await this.poll(d.id,b,c)}submitToolOutputsStream(a,b,c){return cu.createToolAssistantStream(a,this._client.beta.threads.runs,b,c)}}cv.Steps=cs;class cw extends bV{constructor(){super(...arguments),this.runs=new cv(this._client),this.messages=new cr(this._client)}create(a={},b){return this._client.post("/threads",{body:a,...b,headers:ch([{"OpenAI-Beta":"assistants=v2"},b?.headers])})}retrieve(a,b){return this._client.get(bY`/threads/${a}`,{...b,headers:ch([{"OpenAI-Beta":"assistants=v2"},b?.headers])})}update(a,b,c){return this._client.post(bY`/threads/${a}`,{body:b,...c,headers:ch([{"OpenAI-Beta":"assistants=v2"},c?.headers])})}delete(a,b){return this._client.delete(bY`/threads/${a}`,{...b,headers:ch([{"OpenAI-Beta":"assistants=v2"},b?.headers])})}createAndRun(a,b){return this._client.post("/threads/runs",{body:a,...b,headers:ch([{"OpenAI-Beta":"assistants=v2"},b?.headers]),stream:a.stream??!1})}async createAndRunPoll(a,b){let c=await this.createAndRun(a,b);return await this.runs.poll(c.id,{thread_id:c.thread_id},b)}createAndRunStream(a,b){return cu.createThreadAssistantStream(a,this._client.beta.threads,b)}}cw.Runs=cv,cw.Messages=cr;class cx extends bV{constructor(){super(...arguments),this.realtime=new cq(this._client),this.assistants=new cn(this._client),this.threads=new cw(this._client)}}cx.Realtime=cq,cx.Assistants=cn,cx.Threads=cw;class cy extends bV{create(a,b){return this._client.post("/completions",{body:a,...b,stream:a.stream??!1})}}class cz extends bV{retrieve(a,b,c){let{container_id:d}=b;return this._client.get(bY`/containers/${d}/files/${a}/content`,{...c,headers:ch([{Accept:"application/binary"},c?.headers]),__binaryResponse:!0})}}class cA extends bV{constructor(){super(...arguments),this.content=new cz(this._client)}create(a,b,c){return this._client.post(bY`/containers/${a}/files`,bM({body:b,...c},this._client))}retrieve(a,b,c){let{container_id:d}=b;return this._client.get(bY`/containers/${d}/files/${a}`,c)}list(a,b={},c){return this._client.getAPIList(bY`/containers/${a}/files`,bH,{query:b,...c})}delete(a,b,c){let{container_id:d}=b;return this._client.delete(bY`/containers/${d}/files/${a}`,{...c,headers:ch([{Accept:"*/*"},c?.headers])})}}cA.Content=cz;class cB extends bV{constructor(){super(...arguments),this.files=new cA(this._client)}create(a,b){return this._client.post("/containers",{body:a,...b})}retrieve(a,b){return this._client.get(bY`/containers/${a}`,b)}list(a={},b){return this._client.getAPIList("/containers",bH,{query:a,...b})}delete(a,b){return this._client.delete(bY`/containers/${a}`,{...b,headers:ch([{Accept:"*/*"},b?.headers])})}}cB.Files=cA;class cC extends bV{create(a,b){let c=!!a.encoding_format,d=c?a.encoding_format:"base64";c&&bv(this._client).debug("embeddings/user defined encoding_format:",a.encoding_format);let e=this._client.post("/embeddings",{body:{...a,encoding_format:d},...b});return c?e:(bv(this._client).debug("embeddings/decoding base64 embeddings from base64"),e._thenUnwrap(a=>(a&&a.data&&a.data.forEach(a=>{let b=a.embedding;a.embedding=(a=>{if("undefined"!=typeof Buffer){let b=Buffer.from(a,"base64");return Array.from(new Float32Array(b.buffer,b.byteOffset,b.length/Float32Array.BYTES_PER_ELEMENT))}{let b=atob(a),c=b.length,d=new Uint8Array(c);for(let a=0;a<c;a++)d[a]=b.charCodeAt(a);return Array.from(new Float32Array(d.buffer))}})(b)}),a)))}}class cD extends bV{retrieve(a,b,c){let{eval_id:d,run_id:e}=b;return this._client.get(bY`/evals/${d}/runs/${e}/output_items/${a}`,c)}list(a,b,c){let{eval_id:d,...e}=b;return this._client.getAPIList(bY`/evals/${d}/runs/${a}/output_items`,bH,{query:e,...c})}}class cE extends bV{constructor(){super(...arguments),this.outputItems=new cD(this._client)}create(a,b,c){return this._client.post(bY`/evals/${a}/runs`,{body:b,...c})}retrieve(a,b,c){let{eval_id:d}=b;return this._client.get(bY`/evals/${d}/runs/${a}`,c)}list(a,b={},c){return this._client.getAPIList(bY`/evals/${a}/runs`,bH,{query:b,...c})}delete(a,b,c){let{eval_id:d}=b;return this._client.delete(bY`/evals/${d}/runs/${a}`,c)}cancel(a,b,c){let{eval_id:d}=b;return this._client.post(bY`/evals/${d}/runs/${a}`,c)}}cE.OutputItems=cD;class cF extends bV{constructor(){super(...arguments),this.runs=new cE(this._client)}create(a,b){return this._client.post("/evals",{body:a,...b})}retrieve(a,b){return this._client.get(bY`/evals/${a}`,b)}update(a,b,c){return this._client.post(bY`/evals/${a}`,{body:b,...c})}list(a={},b){return this._client.getAPIList("/evals",bH,{query:a,...b})}delete(a,b){return this._client.delete(bY`/evals/${a}`,b)}}cF.Runs=cE;class cG extends bV{create(a,b){return this._client.post("/files",bM({body:a,...b},this._client))}retrieve(a,b){return this._client.get(bY`/files/${a}`,b)}list(a={},b){return this._client.getAPIList("/files",bH,{query:a,...b})}delete(a,b){return this._client.delete(bY`/files/${a}`,b)}content(a,b){return this._client.get(bY`/files/${a}/content`,{...b,headers:ch([{Accept:"application/binary"},b?.headers]),__binaryResponse:!0})}async waitForProcessing(a,{pollInterval:b=5e3,maxWait:c=18e5}={}){let d=new Set(["processed","error","deleted"]),e=Date.now(),f=await this.retrieve(a);for(;!f.status||!d.has(f.status);)if(await a3(b),f=await this.retrieve(a),Date.now()-e>c)throw new aP({message:`Giving up on waiting for file ${a} to finish processing after ${c} milliseconds.`});return f}}class cH extends bV{}class cI extends bV{run(a,b){return this._client.post("/fine_tuning/alpha/graders/run",{body:a,...b})}validate(a,b){return this._client.post("/fine_tuning/alpha/graders/validate",{body:a,...b})}}class cJ extends bV{constructor(){super(...arguments),this.graders=new cI(this._client)}}cJ.Graders=cI;class cK extends bV{create(a,b,c){return this._client.getAPIList(bY`/fine_tuning/checkpoints/${a}/permissions`,bG,{body:b,method:"post",...c})}retrieve(a,b={},c){return this._client.get(bY`/fine_tuning/checkpoints/${a}/permissions`,{query:b,...c})}delete(a,b,c){let{fine_tuned_model_checkpoint:d}=b;return this._client.delete(bY`/fine_tuning/checkpoints/${d}/permissions/${a}`,c)}}class cL extends bV{constructor(){super(...arguments),this.permissions=new cK(this._client)}}cL.Permissions=cK;class cM extends bV{list(a,b={},c){return this._client.getAPIList(bY`/fine_tuning/jobs/${a}/checkpoints`,bH,{query:b,...c})}}class cN extends bV{constructor(){super(...arguments),this.checkpoints=new cM(this._client)}create(a,b){return this._client.post("/fine_tuning/jobs",{body:a,...b})}retrieve(a,b){return this._client.get(bY`/fine_tuning/jobs/${a}`,b)}list(a={},b){return this._client.getAPIList("/fine_tuning/jobs",bH,{query:a,...b})}cancel(a,b){return this._client.post(bY`/fine_tuning/jobs/${a}/cancel`,b)}listEvents(a,b={},c){return this._client.getAPIList(bY`/fine_tuning/jobs/${a}/events`,bH,{query:b,...c})}pause(a,b){return this._client.post(bY`/fine_tuning/jobs/${a}/pause`,b)}resume(a,b){return this._client.post(bY`/fine_tuning/jobs/${a}/resume`,b)}}cN.Checkpoints=cM;class cO extends bV{constructor(){super(...arguments),this.methods=new cH(this._client),this.jobs=new cN(this._client),this.checkpoints=new cL(this._client),this.alpha=new cJ(this._client)}}cO.Methods=cH,cO.Jobs=cN,cO.Checkpoints=cL,cO.Alpha=cJ;class cP extends bV{}class cQ extends bV{constructor(){super(...arguments),this.graderModels=new cP(this._client)}}cQ.GraderModels=cP;class cR extends bV{createVariation(a,b){return this._client.post("/images/variations",bM({body:a,...b},this._client))}edit(a,b){return this._client.post("/images/edits",bM({body:a,...b,stream:a.stream??!1},this._client))}generate(a,b){return this._client.post("/images/generations",{body:a,...b,stream:a.stream??!1})}}class cS extends bV{retrieve(a,b){return this._client.get(bY`/models/${a}`,b)}list(a){return this._client.getAPIList("/models",bG,a)}delete(a,b){return this._client.delete(bY`/models/${a}`,b)}}class cT extends bV{create(a,b){return this._client.post("/moderations",{body:a,...b})}}function cU(a,b){let c=a.output.map(a=>{if("function_call"===a.type)return{...a,parsed_arguments:function(a,b){var c,d;let e=(c=a.tools??[],d=b.name,c.find(a=>"function"===a.type&&a.name===d));return{...b,...b,parsed_arguments:e?.$brand==="auto-parseable-tool"?e.$parseRaw(b.arguments):e?.strict?JSON.parse(b.arguments):null}}(b,a)};if("message"===a.type){let c=a.content.map(a=>{var c,d;return"output_text"===a.type?{...a,parsed:(c=b,d=a.text,c.text?.format?.type!=="json_schema"?null:"$parseRaw"in c.text?.format?(c.text?.format).$parseRaw(d):JSON.parse(d))}:a});return{...a,content:c}}return a}),d=Object.assign({},a,{output:c});return Object.getOwnPropertyDescriptor(a,"output_text")||cV(d),Object.defineProperty(d,"output_parsed",{enumerable:!0,get(){for(let a of d.output)if("message"===a.type){for(let b of a.content)if("output_text"===b.type&&null!==b.parsed)return b.parsed}return null}}),d}function cV(a){let b=[];for(let c of a.output)if("message"===c.type)for(let a of c.content)"output_text"===a.type&&b.push(a.text);a.output_text=b.join("")}class cW extends b0{constructor(a){super(),ao.add(this),ap.set(this,void 0),aq.set(this,void 0),ar.set(this,void 0),aG(this,ap,a,"f")}static createResponse(a,b,c){let d=new cW(b);return d._run(()=>d._createOrRetrieveResponse(a,b,{...c,headers:{...c?.headers,"X-Stainless-Helper-Method":"stream"}})),d}async _createOrRetrieveResponse(a,b,c){let d,e=c?.signal;e&&(e.aborted&&this.controller.abort(),e.addEventListener("abort",()=>this.controller.abort())),aH(this,ao,"m",as).call(this);let f=null;for await(let e of("response_id"in b?(d=await a.responses.retrieve(b.response_id,{stream:!0},{...c,signal:this.controller.signal,stream:!0}),f=b.starting_after??null):d=await a.responses.create({...b,stream:!0},{...c,signal:this.controller.signal}),this._connected(),d))aH(this,ao,"m",at).call(this,e,f);if(d.controller.signal?.aborted)throw new aN;return aH(this,ao,"m",au).call(this)}[(ap=new WeakMap,aq=new WeakMap,ar=new WeakMap,ao=new WeakSet,as=function(){this.ended||aG(this,aq,void 0,"f")},at=function(a,b){if(this.ended)return;let c=(a,c)=>{(null==b||c.sequence_number>b)&&this._emit(a,c)},d=aH(this,ao,"m",av).call(this,a);switch(c("event",a),a.type){case"response.output_text.delta":{let b=d.output[a.output_index];if(!b)throw new aL(`missing output at index ${a.output_index}`);if("message"===b.type){let d=b.content[a.content_index];if(!d)throw new aL(`missing content at index ${a.content_index}`);if("output_text"!==d.type)throw new aL(`expected content to be 'output_text', got ${d.type}`);c("response.output_text.delta",{...a,snapshot:d.text})}break}case"response.function_call_arguments.delta":{let b=d.output[a.output_index];if(!b)throw new aL(`missing output at index ${a.output_index}`);"function_call"===b.type&&c("response.function_call_arguments.delta",{...a,snapshot:b.arguments});break}default:c(a.type,a)}},au=function(){if(this.ended)throw new aL("stream has ended, this shouldn't happen");let a=aH(this,aq,"f");if(!a)throw new aL("request ended without sending any events");aG(this,aq,void 0,"f");let b=function(a,b){var c;return b&&(c=b,b1(c.text?.format))?cU(a,b):{...a,output_parsed:null,output:a.output.map(a=>"function_call"===a.type?{...a,parsed_arguments:null}:"message"===a.type?{...a,content:a.content.map(a=>({...a,parsed:null}))}:a)}}(a,aH(this,ap,"f"));return aG(this,ar,b,"f"),b},av=function(a){let b=aH(this,aq,"f");if(!b){if("response.created"!==a.type)throw new aL(`When snapshot hasn't been set yet, expected 'response.created' event, got ${a.type}`);return aG(this,aq,a.response,"f")}switch(a.type){case"response.output_item.added":b.output.push(a.item);break;case"response.content_part.added":{let c=b.output[a.output_index];if(!c)throw new aL(`missing output at index ${a.output_index}`);"message"===c.type&&c.content.push(a.part);break}case"response.output_text.delta":{let c=b.output[a.output_index];if(!c)throw new aL(`missing output at index ${a.output_index}`);if("message"===c.type){let b=c.content[a.content_index];if(!b)throw new aL(`missing content at index ${a.content_index}`);if("output_text"!==b.type)throw new aL(`expected content to be 'output_text', got ${b.type}`);b.text+=a.delta}break}case"response.function_call_arguments.delta":{let c=b.output[a.output_index];if(!c)throw new aL(`missing output at index ${a.output_index}`);"function_call"===c.type&&(c.arguments+=a.delta);break}case"response.completed":aG(this,aq,a.response,"f")}return b},Symbol.asyncIterator)](){let a=[],b=[],c=!1;return this.on("event",c=>{let d=b.shift();d?d.resolve(c):a.push(c)}),this.on("end",()=>{for(let a of(c=!0,b))a.resolve(void 0);b.length=0}),this.on("abort",a=>{for(let d of(c=!0,b))d.reject(a);b.length=0}),this.on("error",a=>{for(let d of(c=!0,b))d.reject(a);b.length=0}),{next:async()=>a.length?{value:a.shift(),done:!1}:c?{value:void 0,done:!0}:new Promise((a,c)=>b.push({resolve:a,reject:c})).then(a=>a?{value:a,done:!1}:{value:void 0,done:!0}),return:async()=>(this.abort(),{value:void 0,done:!0})}}async finalResponse(){await this.done();let a=aH(this,ar,"f");if(!a)throw new aL("stream ended without producing a ChatCompletion");return a}}class cX extends bV{list(a,b={},c){return this._client.getAPIList(bY`/responses/${a}/input_items`,bH,{query:b,...c})}}class cY extends bV{constructor(){super(...arguments),this.inputItems=new cX(this._client)}create(a,b){return this._client.post("/responses",{body:a,...b,stream:a.stream??!1})._thenUnwrap(a=>("object"in a&&"response"===a.object&&cV(a),a))}retrieve(a,b={},c){return this._client.get(bY`/responses/${a}`,{query:b,...c,stream:b?.stream??!1})._thenUnwrap(a=>("object"in a&&"response"===a.object&&cV(a),a))}delete(a,b){return this._client.delete(bY`/responses/${a}`,{...b,headers:ch([{Accept:"*/*"},b?.headers])})}parse(a,b){return this._client.responses.create(a,b)._thenUnwrap(b=>cU(b,a))}stream(a,b){return cW.createResponse(this._client,a,b)}cancel(a,b){return this._client.post(bY`/responses/${a}/cancel`,b)}}cY.InputItems=cX;class cZ extends bV{create(a,b,c){return this._client.post(bY`/uploads/${a}/parts`,bM({body:b,...c},this._client))}}class c$ extends bV{constructor(){super(...arguments),this.parts=new cZ(this._client)}create(a,b){return this._client.post("/uploads",{body:a,...b})}cancel(a,b){return this._client.post(bY`/uploads/${a}/cancel`,b)}complete(a,b,c){return this._client.post(bY`/uploads/${a}/complete`,{body:b,...c})}}c$.Parts=cZ;let c_=async a=>{let b=await Promise.allSettled(a),c=b.filter(a=>"rejected"===a.status);if(c.length){for(let a of c)console.error(a.reason);throw Error(`${c.length} promise(s) failed - see the above errors`)}let d=[];for(let a of b)"fulfilled"===a.status&&d.push(a.value);return d};class c0 extends bV{create(a,b,c){return this._client.post(bY`/vector_stores/${a}/file_batches`,{body:b,...c,headers:ch([{"OpenAI-Beta":"assistants=v2"},c?.headers])})}retrieve(a,b,c){let{vector_store_id:d}=b;return this._client.get(bY`/vector_stores/${d}/file_batches/${a}`,{...c,headers:ch([{"OpenAI-Beta":"assistants=v2"},c?.headers])})}cancel(a,b,c){let{vector_store_id:d}=b;return this._client.post(bY`/vector_stores/${d}/file_batches/${a}/cancel`,{...c,headers:ch([{"OpenAI-Beta":"assistants=v2"},c?.headers])})}async createAndPoll(a,b,c){let d=await this.create(a,b);return await this.poll(a,d.id,c)}listFiles(a,b,c){let{vector_store_id:d,...e}=b;return this._client.getAPIList(bY`/vector_stores/${d}/file_batches/${a}/files`,bH,{query:e,...c,headers:ch([{"OpenAI-Beta":"assistants=v2"},c?.headers])})}async poll(a,b,c){let d=ch([c?.headers,{"X-Stainless-Poll-Helper":"true","X-Stainless-Custom-Poll-Interval":c?.pollIntervalMs?.toString()??void 0}]);for(;;){let{data:e,response:f}=await this.retrieve(b,{vector_store_id:a},{...c,headers:d}).withResponse();switch(e.status){case"in_progress":let g=5e3;if(c?.pollIntervalMs)g=c.pollIntervalMs;else{let a=f.headers.get("openai-poll-after-ms");if(a){let b=parseInt(a);isNaN(b)||(g=b)}}await a3(g);break;case"failed":case"cancelled":case"completed":return e}}}async uploadAndPoll(a,{files:b,fileIds:c=[]},d){if(null==b||0==b.length)throw Error("No `files` provided to process. If you've already uploaded files you should use `.createAndPoll()` instead");let e=Math.min(d?.maxConcurrency??5,b.length),f=this._client,g=b.values(),h=[...c];async function i(a){for(let b of a){let a=await f.files.create({file:b,purpose:"assistants"},d);h.push(a.id)}}let j=Array(e).fill(g).map(i);return await c_(j),await this.createAndPoll(a,{file_ids:h})}}class c1 extends bV{create(a,b,c){return this._client.post(bY`/vector_stores/${a}/files`,{body:b,...c,headers:ch([{"OpenAI-Beta":"assistants=v2"},c?.headers])})}retrieve(a,b,c){let{vector_store_id:d}=b;return this._client.get(bY`/vector_stores/${d}/files/${a}`,{...c,headers:ch([{"OpenAI-Beta":"assistants=v2"},c?.headers])})}update(a,b,c){let{vector_store_id:d,...e}=b;return this._client.post(bY`/vector_stores/${d}/files/${a}`,{body:e,...c,headers:ch([{"OpenAI-Beta":"assistants=v2"},c?.headers])})}list(a,b={},c){return this._client.getAPIList(bY`/vector_stores/${a}/files`,bH,{query:b,...c,headers:ch([{"OpenAI-Beta":"assistants=v2"},c?.headers])})}delete(a,b,c){let{vector_store_id:d}=b;return this._client.delete(bY`/vector_stores/${d}/files/${a}`,{...c,headers:ch([{"OpenAI-Beta":"assistants=v2"},c?.headers])})}async createAndPoll(a,b,c){let d=await this.create(a,b,c);return await this.poll(a,d.id,c)}async poll(a,b,c){let d=ch([c?.headers,{"X-Stainless-Poll-Helper":"true","X-Stainless-Custom-Poll-Interval":c?.pollIntervalMs?.toString()??void 0}]);for(;;){let e=await this.retrieve(b,{vector_store_id:a},{...c,headers:d}).withResponse(),f=e.data;switch(f.status){case"in_progress":let g=5e3;if(c?.pollIntervalMs)g=c.pollIntervalMs;else{let a=e.response.headers.get("openai-poll-after-ms");if(a){let b=parseInt(a);isNaN(b)||(g=b)}}await a3(g);break;case"failed":case"completed":return f}}}async upload(a,b,c){let d=await this._client.files.create({file:b,purpose:"assistants"},c);return this.create(a,{file_id:d.id},c)}async uploadAndPoll(a,b,c){let d=await this.upload(a,b,c);return await this.poll(a,d.id,c)}content(a,b,c){let{vector_store_id:d}=b;return this._client.getAPIList(bY`/vector_stores/${d}/files/${a}/content`,bG,{...c,headers:ch([{"OpenAI-Beta":"assistants=v2"},c?.headers])})}}class c2 extends bV{constructor(){super(...arguments),this.files=new c1(this._client),this.fileBatches=new c0(this._client)}create(a,b){return this._client.post("/vector_stores",{body:a,...b,headers:ch([{"OpenAI-Beta":"assistants=v2"},b?.headers])})}retrieve(a,b){return this._client.get(bY`/vector_stores/${a}`,{...b,headers:ch([{"OpenAI-Beta":"assistants=v2"},b?.headers])})}update(a,b,c){return this._client.post(bY`/vector_stores/${a}`,{body:b,...c,headers:ch([{"OpenAI-Beta":"assistants=v2"},c?.headers])})}list(a={},b){return this._client.getAPIList("/vector_stores",bH,{query:a,...b,headers:ch([{"OpenAI-Beta":"assistants=v2"},b?.headers])})}delete(a,b){return this._client.delete(bY`/vector_stores/${a}`,{...b,headers:ch([{"OpenAI-Beta":"assistants=v2"},b?.headers])})}search(a,b,c){return this._client.getAPIList(bY`/vector_stores/${a}/search`,bG,{body:b,method:"post",...c,headers:ch([{"OpenAI-Beta":"assistants=v2"},c?.headers])})}}c2.Files=c1,c2.FileBatches=c0;class c3 extends bV{constructor(){super(...arguments),aw.add(this)}async unwrap(a,b,c=this._client.webhookSecret,d=300){return await this.verifySignature(a,b,c,d),JSON.parse(a)}async verifySignature(a,b,c=this._client.webhookSecret,d=300){if("undefined"==typeof crypto||"function"!=typeof crypto.subtle.importKey||"function"!=typeof crypto.subtle.verify)throw Error("Webhook signature verification is only supported when the `crypto` global is defined");aH(this,aw,"m",ax).call(this,c);let e=ch([b]).values,f=aH(this,aw,"m",ay).call(this,e,"webhook-signature"),g=aH(this,aw,"m",ay).call(this,e,"webhook-timestamp"),h=aH(this,aw,"m",ay).call(this,e,"webhook-id"),i=parseInt(g,10);if(isNaN(i))throw new a$("Invalid webhook timestamp format");let j=Math.floor(Date.now()/1e3);if(j-i>d)throw new a$("Webhook timestamp is too old");if(i>j+d)throw new a$("Webhook timestamp is too new");let k=f.split(" ").map(a=>a.startsWith("v1,")?a.substring(3):a),l=c.startsWith("whsec_")?Buffer.from(c.replace("whsec_",""),"base64"):Buffer.from(c,"utf-8"),m=h?`${h}.${g}.${a}`:`${g}.${a}`,n=await crypto.subtle.importKey("raw",l,{name:"HMAC",hash:"SHA-256"},!1,["verify"]);for(let a of k)try{let b=Buffer.from(a,"base64");if(await crypto.subtle.verify("HMAC",n,b,new TextEncoder().encode(m)))return}catch{continue}throw new a$("The given webhook signature does not match the expected signature")}}aw=new WeakSet,ax=function(a){if("string"!=typeof a||0===a.length)throw Error("The webhook secret must either be set using the env var, OPENAI_WEBHOOK_SECRET, on the client class, OpenAI({ webhookSecret: '123' }), or passed to this function")},ay=function(a,b){if(!a)throw Error("Headers are required");let c=a.get(b);if(null==c)throw Error(`Missing required header: ${b}`);return c};class c4{constructor({baseURL:a=ct("OPENAI_BASE_URL"),apiKey:b=ct("OPENAI_API_KEY"),organization:c=ct("OPENAI_ORG_ID")??null,project:d=ct("OPENAI_PROJECT_ID")??null,webhookSecret:e=ct("OPENAI_WEBHOOK_SECRET")??null,...f}={}){if(az.add(this),aB.set(this,void 0),this.completions=new cy(this),this.chat=new cf(this),this.embeddings=new cC(this),this.files=new cG(this),this.images=new cR(this),this.audio=new cl(this),this.moderations=new cT(this),this.models=new cS(this),this.fineTuning=new cO(this),this.graders=new cQ(this),this.vectorStores=new c2(this),this.webhooks=new c3(this),this.beta=new cx(this),this.batches=new cm(this),this.uploads=new c$(this),this.responses=new cY(this),this.evals=new cF(this),this.containers=new cB(this),void 0===b)throw new aL("The OPENAI_API_KEY environment variable is missing or empty; either provide it, or instantiate the OpenAI client with an apiKey option, like new OpenAI({ apiKey: 'My API Key' }).");let g={apiKey:b,organization:c,project:d,webhookSecret:e,...f,baseURL:a||"https://api.openai.com/v1"};if(!g.dangerouslyAllowBrowser&&"undefined"!=typeof window&&void 0!==window.document&&"undefined"!=typeof navigator)throw new aL("It looks like you're running in a browser-like environment.\n\nThis is disabled by default, as it risks exposing your secret API credentials to attackers.\nIf you understand the risks and have appropriate mitigations in place,\nyou can set the `dangerouslyAllowBrowser` option to `true`, e.g.,\n\nnew OpenAI({ apiKey, dangerouslyAllowBrowser: true });\n\nhttps://help.openai.com/en/articles/5112595-best-practices-for-api-key-safety\n");this.baseURL=g.baseURL,this.timeout=g.timeout??aA.DEFAULT_TIMEOUT,this.logger=g.logger??console;let h="warn";this.logLevel=h,this.logLevel=bq(g.logLevel,"ClientOptions.logLevel",this)??bq(ct("OPENAI_LOG"),"process.env['OPENAI_LOG']",this)??h,this.fetchOptions=g.fetchOptions,this.maxRetries=g.maxRetries??2,this.fetch=g.fetch??function(){if("undefined"!=typeof fetch)return fetch;throw Error("`fetch` is not defined as a global; Either pass `fetch` to the client, `new OpenAI({ fetch })` or polyfill the global, `globalThis.fetch = fetch`")}(),aG(this,aB,bb,"f"),this._options=g,this.apiKey=b,this.organization=c,this.project=d,this.webhookSecret=e}withOptions(a){return new this.constructor({...this._options,baseURL:this.baseURL,maxRetries:this.maxRetries,timeout:this.timeout,logger:this.logger,logLevel:this.logLevel,fetch:this.fetch,fetchOptions:this.fetchOptions,apiKey:this.apiKey,organization:this.organization,project:this.project,webhookSecret:this.webhookSecret,...a})}defaultQuery(){return this._options.defaultQuery}validateHeaders({values:a,nulls:b}){}async authHeaders(a){return ch([{Authorization:`Bearer ${this.apiKey}`}])}stringifyQuery(a){return function(a,b={}){let c,d=a,e=function(a=bk){let b;if(void 0!==a.allowEmptyArrays&&"boolean"!=typeof a.allowEmptyArrays)throw TypeError("`allowEmptyArrays` option can only be `true` or `false`, when provided");if(void 0!==a.encodeDotInKeys&&"boolean"!=typeof a.encodeDotInKeys)throw TypeError("`encodeDotInKeys` option can only be `true` or `false`, when provided");if(null!==a.encoder&&void 0!==a.encoder&&"function"!=typeof a.encoder)throw TypeError("Encoder has to be a function.");let c=a.charset||bk.charset;if(void 0!==a.charset&&"utf-8"!==a.charset&&"iso-8859-1"!==a.charset)throw TypeError("The charset option must be either utf-8, iso-8859-1, or undefined");let d=bc;if(void 0!==a.format){if(!bf(be,a.format))throw TypeError("Unknown format option provided.");d=a.format}let e=be[d],f=bk.filter;if(("function"==typeof a.filter||a0(a.filter))&&(f=a.filter),b=a.arrayFormat&&a.arrayFormat in bi?a.arrayFormat:"indices"in a?a.indices?"indices":"repeat":bk.arrayFormat,"commaRoundTrip"in a&&"boolean"!=typeof a.commaRoundTrip)throw TypeError("`commaRoundTrip` must be a boolean, or absent");let g=void 0===a.allowDots?!0==!!a.encodeDotInKeys||bk.allowDots:!!a.allowDots;return{addQueryPrefix:"boolean"==typeof a.addQueryPrefix?a.addQueryPrefix:bk.addQueryPrefix,allowDots:g,allowEmptyArrays:"boolean"==typeof a.allowEmptyArrays?!!a.allowEmptyArrays:bk.allowEmptyArrays,arrayFormat:b,charset:c,charsetSentinel:"boolean"==typeof a.charsetSentinel?a.charsetSentinel:bk.charsetSentinel,commaRoundTrip:!!a.commaRoundTrip,delimiter:void 0===a.delimiter?bk.delimiter:a.delimiter,encode:"boolean"==typeof a.encode?a.encode:bk.encode,encodeDotInKeys:"boolean"==typeof a.encodeDotInKeys?a.encodeDotInKeys:bk.encodeDotInKeys,encoder:"function"==typeof a.encoder?a.encoder:bk.encoder,encodeValuesOnly:"boolean"==typeof a.encodeValuesOnly?a.encodeValuesOnly:bk.encodeValuesOnly,filter:f,format:d,formatter:e,serializeDate:"function"==typeof a.serializeDate?a.serializeDate:bk.serializeDate,skipNulls:"boolean"==typeof a.skipNulls?a.skipNulls:bk.skipNulls,sort:"function"==typeof a.sort?a.sort:null,strictNullHandling:"boolean"==typeof a.strictNullHandling?a.strictNullHandling:bk.strictNullHandling}}(b);"function"==typeof e.filter?d=(0,e.filter)("",d):a0(e.filter)&&(c=e.filter);let f=[];if("object"!=typeof d||null===d)return"";let g=bi[e.arrayFormat],h="comma"===g&&e.commaRoundTrip;c||(c=Object.keys(d)),e.sort&&c.sort(e.sort);let i=new WeakMap;for(let a=0;a<c.length;++a){let b=c[a];e.skipNulls&&null===d[b]||bj(f,function a(b,c,d,e,f,g,h,i,j,k,l,m,n,o,p,q,r,s){var t,u;let v,w=b,x=s,y=0,z=!1;for(;void 0!==(x=x.get(bl))&&!z;){let a=x.get(b);if(y+=1,void 0!==a)if(a===y)throw RangeError("Cyclic object value");else z=!0;void 0===x.get(bl)&&(y=0)}if("function"==typeof k?w=k(c,w):w instanceof Date?w=n?.(w):"comma"===d&&a0(w)&&(w=bh(w,function(a){return a instanceof Date?n?.(a):a})),null===w){if(g)return j&&!q?j(c,bk.encoder,r,"key",o):c;w=""}if("string"==typeof(t=w)||"number"==typeof t||"boolean"==typeof t||"symbol"==typeof t||"bigint"==typeof t||(u=w)&&"object"==typeof u&&u.constructor&&u.constructor.isBuffer&&u.constructor.isBuffer(u)){if(j){let a=q?c:j(c,bk.encoder,r,"key",o);return[p?.(a)+"="+p?.(j(w,bk.encoder,r,"value",o))]}return[p?.(c)+"="+p?.(String(w))]}let A=[];if(void 0===w)return A;if("comma"===d&&a0(w))q&&j&&(w=bh(w,j)),v=[{value:w.length>0?w.join(",")||null:void 0}];else if(a0(k))v=k;else{let a=Object.keys(w);v=l?a.sort(l):a}let B=i?String(c).replace(/\./g,"%2E"):String(c),C=e&&a0(w)&&1===w.length?B+"[]":B;if(f&&a0(w)&&0===w.length)return C+"[]";for(let c=0;c<v.length;++c){let t=v[c],u="object"==typeof t&&void 0!==t.value?t.value:w[t];if(h&&null===u)continue;let x=m&&i?t.replace(/\./g,"%2E"):t,z=a0(w)?"function"==typeof d?d(C,x):C:C+(m?"."+x:"["+x+"]");s.set(b,y);let B=new WeakMap;B.set(bl,s),bj(A,a(u,z,d,e,f,g,h,i,"comma"===d&&q&&a0(w)?null:j,k,l,m,n,o,p,q,r,B))}return A}(d[b],b,g,h,e.allowEmptyArrays,e.strictNullHandling,e.skipNulls,e.encodeDotInKeys,e.encode?e.encoder:null,e.filter,e.sort,e.allowDots,e.serializeDate,e.format,e.formatter,e.encodeValuesOnly,e.charset,i))}let j=f.join(e.delimiter),k=!0===e.addQueryPrefix?"?":"";return e.charsetSentinel&&("iso-8859-1"===e.charset?k+="utf8=%26%2310003%3B&":k+="utf8=%E2%9C%93&"),j.length>0?k+j:""}(a,{arrayFormat:"brackets"})}getUserAgent(){return`${this.constructor.name}/JS ${a4}`}defaultIdempotencyKey(){return`stainless-node-retry-${aI()}`}makeStatusError(a,b,c,d){return aM.generate(a,b,c,d)}buildURL(a,b,c){let d=!aH(this,az,"m",aC).call(this)&&c||this.baseURL,e=new URL(a_.test(a)?a:d+(d.endsWith("/")&&a.startsWith("/")?a.slice(1):a)),f=this.defaultQuery();return!function(a){if(!a)return!0;for(let b in a)return!1;return!0}(f)&&(b={...f,...b}),"object"==typeof b&&b&&!Array.isArray(b)&&(e.search=this.stringifyQuery(b)),e.toString()}async prepareOptions(a){}async prepareRequest(a,{url:b,options:c}){}get(a,b){return this.methodRequest("get",a,b)}post(a,b){return this.methodRequest("post",a,b)}patch(a,b){return this.methodRequest("patch",a,b)}put(a,b){return this.methodRequest("put",a,b)}delete(a,b){return this.methodRequest("delete",a,b)}methodRequest(a,b,c){return this.request(Promise.resolve(c).then(c=>({method:a,path:b,...c})))}request(a,b=null){return new bD(this,this.makeRequest(a,b,void 0))}async makeRequest(a,b,c){let d=await a,e=d.maxRetries??this.maxRetries;null==b&&(b=e),await this.prepareOptions(d);let{req:f,url:g,timeout:h}=await this.buildRequest(d,{retryCount:e-b});await this.prepareRequest(f,{url:g,options:d});let i="log_"+(0x1000000*Math.random()|0).toString(16).padStart(6,"0"),j=void 0===c?"":`, retryOf: ${c}`,k=Date.now();if(bv(this).debug(`[${i}] sending request`,bw({retryOfRequestLogID:c,method:d.method,url:g,options:d,headers:f.headers})),d.signal?.aborted)throw new aN;let l=new AbortController,m=await this.fetchWithTimeout(g,f,h,l).catch(aK),n=Date.now();if(m instanceof Error){let a=`retrying, ${b} attempts remaining`;if(d.signal?.aborted)throw new aN;let e=aJ(m)||/timed? ?out/i.test(String(m)+("cause"in m?String(m.cause):""));if(b)return bv(this).info(`[${i}] connection ${e?"timed out":"failed"} - ${a}`),bv(this).debug(`[${i}] connection ${e?"timed out":"failed"} (${a})`,bw({retryOfRequestLogID:c,url:g,durationMs:n-k,message:m.message})),this.retryRequest(d,b,c??i);if(bv(this).info(`[${i}] connection ${e?"timed out":"failed"} - error; no more retries left`),bv(this).debug(`[${i}] connection ${e?"timed out":"failed"} (error; no more retries left)`,bw({retryOfRequestLogID:c,url:g,durationMs:n-k,message:m.message})),e)throw new aP;throw new aO({cause:m})}let o=[...m.headers.entries()].filter(([a])=>"x-request-id"===a).map(([a,b])=>", "+a+": "+JSON.stringify(b)).join(""),p=`[${i}${j}${o}] ${f.method} ${g} ${m.ok?"succeeded":"failed"} with status ${m.status} in ${n-k}ms`;if(!m.ok){let a=await this.shouldRetry(m);if(b&&a){let a=`retrying, ${b} attempts remaining`;return await ba(m.body),bv(this).info(`${p} - ${a}`),bv(this).debug(`[${i}] response error (${a})`,bw({retryOfRequestLogID:c,url:m.url,status:m.status,headers:m.headers,durationMs:n-k})),this.retryRequest(d,b,c??i,m.headers)}let e=a?"error; no more retries left":"error; not retryable";bv(this).info(`${p} - ${e}`);let f=await m.text().catch(a=>aK(a).message),g=(a=>{try{return JSON.parse(a)}catch(a){return}})(f),h=g?void 0:f;throw bv(this).debug(`[${i}] response error (${e})`,bw({retryOfRequestLogID:c,url:m.url,status:m.status,headers:m.headers,message:h,durationMs:Date.now()-k})),this.makeStatusError(m.status,g,h,m.headers)}return bv(this).info(p),bv(this).debug(`[${i}] response start`,bw({retryOfRequestLogID:c,url:m.url,status:m.status,headers:m.headers,durationMs:n-k})),{response:m,options:d,controller:l,requestLogID:i,retryOfRequestLogID:c,startTime:k}}getAPIList(a,b,c){return this.requestAPIList(b,{method:"get",path:a,...c})}requestAPIList(a,b){return new bF(this,this.makeRequest(b,null,void 0),a)}async fetchWithTimeout(a,b,c,d){let{signal:e,method:f,...g}=b||{};e&&e.addEventListener("abort",()=>d.abort());let h=setTimeout(()=>d.abort(),c),i=globalThis.ReadableStream&&g.body instanceof globalThis.ReadableStream||"object"==typeof g.body&&null!==g.body&&Symbol.asyncIterator in g.body,j={signal:d.signal,...i?{duplex:"half"}:{},method:"GET",...g};f&&(j.method=f.toUpperCase());try{return await this.fetch.call(void 0,a,j)}finally{clearTimeout(h)}}async shouldRetry(a){let b=a.headers.get("x-should-retry");return"true"===b||"false"!==b&&(408===a.status||409===a.status||429===a.status||!!(a.status>=500))}async retryRequest(a,b,c,d){let e,f=d?.get("retry-after-ms");if(f){let a=parseFloat(f);Number.isNaN(a)||(e=a)}let g=d?.get("retry-after");if(g&&!e){let a=parseFloat(g);e=Number.isNaN(a)?Date.parse(g)-Date.now():1e3*a}if(!(e&&0<=e&&e<6e4)){let c=a.maxRetries??this.maxRetries;e=this.calculateDefaultRetryTimeoutMillis(b,c)}return await a3(e),this.makeRequest(a,b-1,c)}calculateDefaultRetryTimeoutMillis(a,b){return Math.min(.5*Math.pow(2,b-a),8)*(1-.25*Math.random())*1e3}async buildRequest(a,{retryCount:b=0}={}){let c={...a},{method:d,path:e,query:f,defaultBaseURL:g}=c,h=this.buildURL(e,f,g);"timeout"in c&&((a,b)=>{if("number"!=typeof b||!Number.isInteger(b))throw new aL(`${a} must be an integer`);if(b<0)throw new aL(`${a} must be a positive integer`)})("timeout",c.timeout),c.timeout=c.timeout??this.timeout;let{bodyHeaders:i,body:j}=this.buildBody({options:c}),k=await this.buildHeaders({options:a,method:d,bodyHeaders:i,retryCount:b});return{req:{method:d,headers:k,...c.signal&&{signal:c.signal},...globalThis.ReadableStream&&j instanceof globalThis.ReadableStream&&{duplex:"half"},...j&&{body:j},...this.fetchOptions??{},...c.fetchOptions??{}},url:h,timeout:c.timeout}}async buildHeaders({options:a,method:b,bodyHeaders:c,retryCount:e}){let f={};this.idempotencyHeader&&"get"!==b&&(a.idempotencyKey||(a.idempotencyKey=this.defaultIdempotencyKey()),f[this.idempotencyHeader]=a.idempotencyKey);let g=ch([f,{Accept:"application/json","User-Agent":this.getUserAgent(),"X-Stainless-Retry-Count":String(e),...a.timeout?{"X-Stainless-Timeout":String(Math.trunc(a.timeout/1e3))}:{},...d??(d=(()=>{let a="undefined"!=typeof Deno&&null!=Deno.build?"deno":"undefined"!=typeof EdgeRuntime?"edge":"[object process]"===Object.prototype.toString.call(void 0!==globalThis.process?globalThis.process:0)?"node":"unknown";if("deno"===a)return{"X-Stainless-Lang":"js","X-Stainless-Package-Version":a4,"X-Stainless-OS":a6(Deno.build.os),"X-Stainless-Arch":a5(Deno.build.arch),"X-Stainless-Runtime":"deno","X-Stainless-Runtime-Version":"string"==typeof Deno.version?Deno.version:Deno.version?.deno??"unknown"};if("undefined"!=typeof EdgeRuntime)return{"X-Stainless-Lang":"js","X-Stainless-Package-Version":a4,"X-Stainless-OS":"Unknown","X-Stainless-Arch":`other:${EdgeRuntime}`,"X-Stainless-Runtime":"edge","X-Stainless-Runtime-Version":globalThis.process.version};if("node"===a)return{"X-Stainless-Lang":"js","X-Stainless-Package-Version":a4,"X-Stainless-OS":a6(globalThis.process.platform??"unknown"),"X-Stainless-Arch":a5(globalThis.process.arch??"unknown"),"X-Stainless-Runtime":"node","X-Stainless-Runtime-Version":globalThis.process.version??"unknown"};let b=function(){if("undefined"==typeof navigator||!navigator)return null;for(let{key:a,pattern:b}of[{key:"edge",pattern:/Edge(?:\W+(\d+)\.(\d+)(?:\.(\d+))?)?/},{key:"ie",pattern:/MSIE(?:\W+(\d+)\.(\d+)(?:\.(\d+))?)?/},{key:"ie",pattern:/Trident(?:.*rv\:(\d+)\.(\d+)(?:\.(\d+))?)?/},{key:"chrome",pattern:/Chrome(?:\W+(\d+)\.(\d+)(?:\.(\d+))?)?/},{key:"firefox",pattern:/Firefox(?:\W+(\d+)\.(\d+)(?:\.(\d+))?)?/},{key:"safari",pattern:/(?:Version\W+(\d+)\.(\d+)(?:\.(\d+))?)?(?:\W+Mobile\S*)?\W+Safari/}]){let c=b.exec(navigator.userAgent);if(c){let b=c[1]||0,d=c[2]||0,e=c[3]||0;return{browser:a,version:`${b}.${d}.${e}`}}}return null}();return b?{"X-Stainless-Lang":"js","X-Stainless-Package-Version":a4,"X-Stainless-OS":"Unknown","X-Stainless-Arch":"unknown","X-Stainless-Runtime":`browser:${b.browser}`,"X-Stainless-Runtime-Version":b.version}:{"X-Stainless-Lang":"js","X-Stainless-Package-Version":a4,"X-Stainless-OS":"Unknown","X-Stainless-Arch":"unknown","X-Stainless-Runtime":"unknown","X-Stainless-Runtime-Version":"unknown"}})()),"OpenAI-Organization":this.organization,"OpenAI-Project":this.project},await this.authHeaders(a),this._options.defaultHeaders,c,a.headers]);return this.validateHeaders(g),g.values}buildBody({options:{body:a,headers:b}}){if(!a)return{bodyHeaders:void 0,body:void 0};let c=ch([b]);return ArrayBuffer.isView(a)||a instanceof ArrayBuffer||a instanceof DataView||"string"==typeof a&&c.values.has("content-type")||a instanceof Blob||a instanceof FormData||a instanceof URLSearchParams||globalThis.ReadableStream&&a instanceof globalThis.ReadableStream?{bodyHeaders:void 0,body:a}:"object"==typeof a&&(Symbol.asyncIterator in a||Symbol.iterator in a&&"next"in a&&"function"==typeof a.next)?{bodyHeaders:void 0,body:a8(a)}:aH(this,aB,"f").call(this,{body:a,headers:c})}}aA=c4,aB=new WeakMap,az=new WeakSet,aC=function(){return"https://api.openai.com/v1"!==this.baseURL},c4.OpenAI=aA,c4.DEFAULT_TIMEOUT=6e5,c4.OpenAIError=aL,c4.APIError=aM,c4.APIConnectionError=aO,c4.APIConnectionTimeoutError=aP,c4.APIUserAbortError=aN,c4.NotFoundError=aT,c4.ConflictError=aU,c4.RateLimitError=aW,c4.BadRequestError=aQ,c4.AuthenticationError=aR,c4.InternalServerError=aX,c4.PermissionDeniedError=aS,c4.UnprocessableEntityError=aV,c4.InvalidWebhookSignatureError=a$,c4.toFile=bT,c4.Completions=cy,c4.Chat=cf,c4.Embeddings=cC,c4.Files=cG,c4.Images=cR,c4.Audio=cl,c4.Moderations=cT,c4.Models=cS,c4.FineTuning=cO,c4.Graders=cQ,c4.VectorStores=c2,c4.Webhooks=c3,c4.Beta=cx,c4.Batches=cm,c4.Uploads=c$,c4.Responses=cY,c4.Evals=cF,c4.Containers=cB;class c5{constructor(a){this.conversationHistory=[],this.defaultSystemPrompt="You are a helpful voice assistant. Keep your responses concise and conversational, as they will be spoken aloud. Aim for responses that are 1-2 sentences unless more detail is specifically requested.",this.client=new c4({apiKey:a,dangerouslyAllowBrowser:!0})}async sendMessage(a,b={}){let c=performance.now();try{let d={role:"user",content:a,timestamp:Date.now()};this.conversationHistory.push(d);let e=[],f=b.systemPrompt||this.defaultSystemPrompt;e.push({role:"system",content:f});let g=this.conversationHistory.slice(-10);e.push(...g);let h=await this.client.chat.completions.create({model:b.model||"gpt-3.5-turbo",messages:e.map(a=>({role:a.role,content:a.content})),temperature:b.temperature||.7,max_tokens:b.maxTokens||150,stream:!1}),i=performance.now()-c,j=h.choices[0]?.message?.content||"",k={role:"assistant",content:j,timestamp:Date.now()};this.conversationHistory.push(k);let l={message:j,usage:h.usage?{promptTokens:h.usage.prompt_tokens,completionTokens:h.usage.completion_tokens,totalTokens:h.usage.total_tokens}:void 0,processingTime:i};return console.log(`OpenAI response received in ${i.toFixed(2)}ms`),l}catch(b){let a=performance.now();return console.error("OpenAI API error:",b),{message:"Sorry, I encountered an error processing your request.",processingTime:a-c}}}getConversationHistory(){return[...this.conversationHistory]}clearConversationHistory(){this.conversationHistory=[]}setSystemPrompt(a){this.defaultSystemPrompt=a}getSystemPrompt(){return this.defaultSystemPrompt}trimConversationHistory(a=20){this.conversationHistory.length>a&&(this.conversationHistory=this.conversationHistory.slice(-a))}}class c6{constructor(){this.metrics=[],this.maxMetrics=1e3,this.sessionId=this.generateSessionId()}generateSessionId(){return`session_${Date.now()}_${Math.random().toString(36).substr(2,9)}`}getDeviceInfo(){let a=navigator;return{platform:navigator.platform,memory:a.deviceMemory,cores:a.hardwareConcurrency}}recordMetrics(a){let b={...a,timestamp:Date.now(),sessionId:this.sessionId,userAgent:navigator.userAgent,deviceInfo:this.getDeviceInfo()};this.metrics.push(b),this.metrics.length>this.maxMetrics&&(this.metrics=this.metrics.slice(-this.maxMetrics)),this.logPerformance(b),this.persistMetrics()}logPerformance(a){let{totalLatency:b,sttLatency:c,llmLatency:d,ttsLatency:e,audioLength:f}=a;console.group("\uD83D\uDD0D Performance Metrics"),console.log(`Total Latency: ${b.toFixed(0)}ms ${b<1200?"✅":"⚠️"}`),console.log(`STT: ${c.toFixed(0)}ms`),console.log(`LLM: ${d.toFixed(0)}ms`),console.log(`TTS: ${e.toFixed(0)}ms`),console.log(`Audio Length: ${f.toFixed(1)}s`),console.log(`Efficiency: ${(1e3*f/b).toFixed(2)}x realtime`),b>1200&&(console.warn("⚠️ Total latency exceeds 1.2s target"),this.analyzeBottlenecks(a)),console.groupEnd()}analyzeBottlenecks(a){let{sttLatency:b,llmLatency:c,ttsLatency:d}=a,e=b+c+d;console.group("\uD83D\uDD0D Bottleneck Analysis"),b>.4*e&&(console.warn("STT is the bottleneck (>40% of total time)"),console.log("Recommendations: Use smaller Whisper model, optimize audio preprocessing")),c>.5*e&&(console.warn("LLM is the bottleneck (>50% of total time)"),console.log("Recommendations: Use faster model (gpt-3.5-turbo), reduce max_tokens, optimize prompt")),d>.3*e&&(console.warn("TTS is the bottleneck (>30% of total time)"),console.log("Recommendations: Use smaller TTS model, optimize text preprocessing")),console.groupEnd()}persistMetrics(){try{let a=this.metrics.slice(-100);localStorage.setItem("voiceAssistantMetrics",JSON.stringify(a))}catch(a){console.warn("Failed to persist metrics:",a)}}loadPersistedMetrics(){try{let a=localStorage.getItem("voiceAssistantMetrics");if(a){let b=JSON.parse(a);Array.isArray(b)&&(this.metrics=b)}}catch(a){console.warn("Failed to load persisted metrics:",a)}}generateReport(){if(0===this.metrics.length)return{totalSessions:0,averageLatency:0,medianLatency:0,p95Latency:0,successRate:0,cacheHitRate:0,breakdown:{stt:{avg:0,median:0,p95:0},llm:{avg:0,median:0,p95:0},tts:{avg:0,median:0,p95:0}},recommendations:[]};let a=this.metrics.map(a=>a.totalLatency).sort((a,b)=>a-b),b=this.metrics.map(a=>a.sttLatency).sort((a,b)=>a-b),c=this.metrics.map(a=>a.llmLatency).sort((a,b)=>a-b),d=this.metrics.map(a=>a.ttsLatency).sort((a,b)=>a-b),e=a=>a.reduce((a,b)=>a+b,0)/a.length,f=a=>a[Math.floor(a.length/2)],g=a=>a[Math.floor(.95*a.length)],h=this.metrics.filter(a=>a.cacheHit).length,i=this.metrics.filter(a=>a.totalLatency>0).length,j={totalSessions:this.metrics.length,averageLatency:e(a),medianLatency:f(a),p95Latency:g(a),successRate:i/this.metrics.length,cacheHitRate:h/this.metrics.length,breakdown:{stt:{avg:e(b),median:f(b),p95:g(b)},llm:{avg:e(c),median:f(c),p95:g(c)},tts:{avg:e(d),median:f(d),p95:g(d)}},recommendations:this.generateRecommendations(j)};return j}generateRecommendations(a){let b=[];return a.averageLatency>1200&&b.push("Average latency exceeds 1.2s target. Consider optimizing the pipeline."),a.breakdown.stt.avg>500&&b.push("STT latency is high. Consider using whisper-tiny or optimizing audio preprocessing."),a.breakdown.llm.avg>800&&b.push("LLM latency is high. Consider using gpt-3.5-turbo or reducing max_tokens."),a.breakdown.tts.avg>400&&b.push("TTS latency is high. Consider using a smaller TTS model or optimizing text preprocessing."),a.cacheHitRate<.8&&b.push("Low cache hit rate. Ensure models are properly cached."),a.successRate<.95&&b.push("Low success rate. Check for errors in the pipeline."),b}exportMetrics(){return JSON.stringify(this.metrics,null,2)}clearMetrics(){this.metrics=[],localStorage.removeItem("voiceAssistantMetrics")}getRecentMetrics(a=10){return this.metrics.slice(-a)}init(){this.loadPersistedMetrics()}}let c7=new c6;c7.init();class c8{async register(){return console.warn("Service Worker not supported"),null}async getCacheStatus(){return this.registration&&navigator.serviceWorker.controller?new Promise(a=>{let b=new MessageChannel;b.port1.onmessage=b=>{b.data?.type==="CACHE_STATUS"&&a({...b.data,isReady:b.data.modelsCached===b.data.totalModels})},navigator.serviceWorker.controller.postMessage({type:"GET_CACHE_STATUS"},[b.port2])}):null}onCacheStatusUpdate(a){return this.cacheStatusCallbacks.push(a),()=>{let b=this.cacheStatusCallbacks.indexOf(a);b>-1&&this.cacheStatusCallbacks.splice(b,1)}}async skipWaiting(){this.registration?.waiting&&this.registration.waiting.postMessage({type:"SKIP_WAITING"})}async unregister(){return!!this.registration&&await this.registration.unregister()}constructor(){this.registration=null,this.cacheStatusCallbacks=[]}}let c9=new c8,da=()=>{let[a,b]=(0,aE.useState)(""),[c,d]=(0,aE.useState)(!0),[e,f]=(0,aE.useState)(null),[g,h]=(0,aE.useState)(null),[i,j]=function(a={}){let[b,c]=(0,aE.useState)({isInitialized:!1,isListening:!1,isProcessing:!1,currentStage:"idle",error:null,volume:0,lastTranscription:null,lastResponse:null}),d=(0,aE.useRef)({}),e=(0,aE.useRef)(0),f=(0,aE.useCallback)(b=>{c(c=>{let d={...c,...b};return b.currentStage&&b.currentStage!==c.currentStage&&a.onStageChange?.(b.currentStage),d})},[a]),[,g]=function(a={}){let b=(0,aE.useRef)(null),[c,d]=(0,aE.useState)({isInitialized:!1,isRecording:!1,isSpeaking:!1,volume:0,error:null}),e=(0,aE.useCallback)(a=>{d(b=>({...b,...a}))},[]),f=(0,aE.useCallback)(async()=>{try{e({error:null}),b.current=new aF({onAudioChunk:b=>{a.onAudioChunk?.(b)},onSpeechStart:()=>{e({isSpeaking:!0}),a.onSpeechStart?.()},onSpeechEnd:()=>{e({isSpeaking:!1}),a.onSpeechEnd?.()},onError:b=>{e({error:b.message}),console.error("Audio manager error:",b),a.onError?.(b)},onVolumeChange:b=>{e({volume:b}),a.onVolumeChange?.(b)}}),await b.current.initialize(),e({isInitialized:!0}),a.autoStart&&(b.current.startRecording(),e({isRecording:!0,error:null}))}catch(a){e({error:a instanceof Error?a.message:"Unknown audio error",isInitialized:!1})}},[a,e]),g=(0,aE.useCallback)(()=>{if(!b.current)return void e({error:"Audio manager not initialized"});try{b.current.startRecording(),e({isRecording:!0,error:null})}catch(a){e({error:a instanceof Error?a.message:"Failed to start recording"})}},[e]),h=(0,aE.useCallback)(()=>{b.current&&(b.current.stopRecording(),e({isRecording:!1,isSpeaking:!1}))},[e]),i=(0,aE.useCallback)(async()=>{b.current&&(await b.current.cleanup(),b.current=null),d({isInitialized:!1,isRecording:!1,isSpeaking:!1,volume:0,error:null})},[]),j=(0,aE.useCallback)(a=>{b.current?.setVADThreshold(a)},[]);return[c,{initialize:f,startRecording:g,stopRecording:h,cleanup:i,setVADThreshold:j,getVADThreshold:(0,aE.useCallback)(()=>b.current?.getVADThreshold()||.01,[])}]}({onAudioChunk:a=>{"listening"===b.currentStage&&h.bufferAudio(a)},onSpeechStart:()=>{console.log("Speech started"),e.current=performance.now(),d.current={},f({currentStage:"listening"})},onSpeechEnd:()=>{console.log("Speech ended"),"listening"===b.currentStage&&(f({currentStage:"transcribing"}),h.flushBuffer())},onVolumeChange:a=>{f({volume:a})},onError:b=>{f({error:b.message}),a.onError?.(b.message)}}),[,h]=function(a={}){let b=(0,aE.useRef)(null),[c,d]=(0,aE.useState)({isInitialized:!1,isTranscribing:!1,error:null,lastTranscription:null,initializationProgress:0}),e=(0,aE.useCallback)(a=>{d(b=>({...b,...a}))},[]),f=(0,aE.useCallback)(async()=>{try{e({error:null,initializationProgress:0}),b.current=new Worker("/whisper-worker.js"),b.current.onmessage=b=>{let{type:c,...d}=b.data;switch(c){case"initialized":d.success?(e({isInitialized:!0,initializationProgress:100,error:null}),console.log("Whisper STT initialized successfully")):e({error:d.error||"Failed to initialize Whisper",isInitialized:!1,initializationProgress:0});break;case"transcription":e({isTranscribing:!1,lastTranscription:d.result,error:null}),a.onTranscription?.(d.result);break;case"error":let f=d.error||"Unknown transcription error";e({error:f,isTranscribing:!1}),a.onError?.(f);break;case"progress":e({initializationProgress:d.progress||0});break;default:console.warn("Unknown message type from Whisper worker:",c)}},b.current.onerror=b=>{let c=`Worker error: ${b.message}`;e({error:c,isInitialized:!1,initializationProgress:0}),a.onError?.(c)},e({initializationProgress:10}),b.current.postMessage({type:"initialize"})}catch(c){let b=c instanceof Error?c.message:"Failed to create Whisper worker";e({error:b,isInitialized:!1,initializationProgress:0}),a.onError?.(b)}},[a,e]),g=(0,aE.useCallback)(async(a,d={})=>{if(!b.current||!c.isInitialized)throw Error("Whisper STT not initialized");e({isTranscribing:!0,error:null}),b.current.postMessage({type:"transcribe",data:{audioData:Array.from(a),options:d}})},[c.isInitialized,e]),h=(0,aE.useCallback)(a=>{b.current&&c.isInitialized&&b.current.postMessage({type:"buffer-audio",data:{data:Array.from(a.data),timestamp:a.timestamp,sampleRate:a.sampleRate}})},[c.isInitialized]),i=(0,aE.useCallback)(()=>{b.current&&c.isInitialized&&b.current.postMessage({type:"flush-buffer"})},[c.isInitialized]);return[c,{initialize:f,transcribeAudio:g,bufferAudio:h,flushBuffer:i,reset:(0,aE.useCallback)(()=>{b.current&&(b.current.postMessage({type:"reset"}),e({isTranscribing:!1,lastTranscription:null,error:null}))},[e])}]}({onTranscription:b=>{let c=performance.now();if(d.current.sttLatency=c-e.current,d.current.audioLength=b.audioLength,console.log(`STT completed: "${b.text}" (${d.current.sttLatency.toFixed(2)}ms)`),f({lastTranscription:b.text,currentStage:"thinking"}),a.onTranscription?.(b.text),b.text.trim()){let a=performance.now();i.sendMessage(b.text).then(()=>{let b=performance.now();d.current.llmLatency=b-a})}},onError:b=>{f({error:b,currentStage:"idle"}),a.onError?.(b)}}),[,i]=function(a={}){let b=(0,aE.useRef)(null),[c,d]=(0,aE.useState)({isInitialized:!1,isProcessing:!1,error:null,conversationHistory:[],lastResponse:null}),e=(0,aE.useCallback)(a=>{d(b=>({...b,...a}))},[]),f=(0,aE.useCallback)(c=>{try{if(!c||""===c.trim())throw Error("OpenAI API key is required");b.current=new c5(c),e({isInitialized:!0,error:null,conversationHistory:[]}),console.log("OpenAI client initialized successfully")}catch(c){let b=c instanceof Error?c.message:"Failed to initialize OpenAI client";e({error:b,isInitialized:!1}),a.onError?.(b)}},[a,e]),g=(0,aE.useCallback)(async(d,f={})=>{if(!b.current||!c.isInitialized){let b="OpenAI client not initialized";return e({error:b}),a.onError?.(b),null}if(!d||""===d.trim()){let b="Message cannot be empty";return e({error:b}),a.onError?.(b),null}try{e({isProcessing:!0,error:null});let c=await b.current.sendMessage(d,f),g=b.current.getConversationHistory();return e({isProcessing:!1,lastResponse:c,conversationHistory:g,error:null}),a.autoTrimHistory&&a.maxHistoryLength&&b.current.trimConversationHistory(a.maxHistoryLength),a.onResponse?.(c),c}catch(c){let b=c instanceof Error?c.message:"Failed to send message";return e({error:b,isProcessing:!1}),a.onError?.(b),null}},[c.isInitialized,a,e]),h=(0,aE.useCallback)(()=>{b.current&&(b.current.clearConversationHistory(),e({conversationHistory:[],lastResponse:null,error:null}))},[e]),i=(0,aE.useCallback)(a=>{b.current&&b.current.setSystemPrompt(a)},[]);return[c,{initialize:f,sendMessage:g,clearHistory:h,setSystemPrompt:i,getSystemPrompt:(0,aE.useCallback)(()=>b.current?.getSystemPrompt()||"",[])}]}({onResponse:b=>{console.log(`LLM completed: "${b.message}" (${b.processingTime.toFixed(2)}ms)`),f({lastResponse:b.message,currentStage:"speaking"}),a.onResponse?.(b.message),j.synthesizeText(b.message)},onError:b=>{f({error:b,currentStage:"idle"}),a.onError?.(b)},autoTrimHistory:!0,maxHistoryLength:10}),[,j]=function(a={}){let b=(0,aE.useRef)(null),c=(0,aE.useRef)(null),[d,e]=(0,aE.useState)({isInitialized:!1,isSynthesizing:!1,error:null,lastSynthesis:null,initializationProgress:0}),f=(0,aE.useCallback)(a=>{e(b=>({...b,...a}))},[]),g=(0,aE.useCallback)(async(a,b)=>{if(!c.current)throw Error("Audio context not available");try{"suspended"===c.current.state&&await c.current.resume();let d=c.current.createBuffer(1,a.length,b);d.getChannelData(0).set(a);let e=c.current.createBufferSource();return e.buffer=d,e.connect(c.current.destination),new Promise(a=>{e.onended=()=>a(),e.start()})}catch(a){throw console.error("Audio playback error:",a),a}},[]),h=(0,aE.useCallback)(async()=>{try{f({error:null,initializationProgress:0}),c.current=new AudioContext,b.current=new Worker("/tts-worker.js"),b.current.onmessage=async b=>{let{type:c,...d}=b.data;switch(c){case"initialized":d.success?(f({isInitialized:!0,initializationProgress:100,error:null}),console.log("TTS initialized successfully")):f({error:d.error||"Failed to initialize TTS",isInitialized:!1,initializationProgress:0});break;case"synthesis":let e={audioData:new Float32Array(d.result.audioData),sampleRate:d.result.sampleRate,duration:d.result.duration,processingTime:d.result.processingTime,text:d.result.text};f({isSynthesizing:!1,lastSynthesis:e,error:null}),a.onSynthesis?.(e),a.autoPlay&&await g(e.audioData,e.sampleRate);break;case"error":let h=d.error||"Unknown TTS error";f({error:h,isSynthesizing:!1}),a.onError?.(h);break;case"progress":f({initializationProgress:d.progress||0});break;default:console.warn("Unknown message type from TTS worker:",c)}},b.current.onerror=b=>{let c=`TTS Worker error: ${b.message}`;f({error:c,isInitialized:!1,initializationProgress:0}),a.onError?.(c)},f({initializationProgress:10}),b.current.postMessage({type:"initialize"})}catch(c){let b=c instanceof Error?c.message:"Failed to create TTS worker";f({error:b,isInitialized:!1,initializationProgress:0}),a.onError?.(b)}},[a,f,g]),i=(0,aE.useCallback)(async(a,c={})=>{if(!b.current||!d.isInitialized)throw Error("TTS not initialized");if(!a||""===a.trim())throw Error("Text cannot be empty");f({isSynthesizing:!0,error:null}),b.current.postMessage({type:"synthesize",data:{text:a.trim(),options:c}})},[d.isInitialized,f]);return[d,{initialize:h,synthesizeText:i,playAudio:g,reset:(0,aE.useCallback)(()=>{b.current&&(b.current.postMessage({type:"reset"}),f({isSynthesizing:!1,lastSynthesis:null,error:null}))},[f])}]}({onSynthesis:b=>{let c=performance.now();if(d.current.ttsLatency=b.processingTime,d.current.totalLatency=c-e.current,console.log(`TTS completed (${b.processingTime.toFixed(2)}ms)`),console.log(`Total pipeline latency: ${d.current.totalLatency?.toFixed(2)}ms`),d.current.totalLatency){let b={sttLatency:d.current.sttLatency||0,llmLatency:d.current.llmLatency||0,ttsLatency:d.current.ttsLatency||0,totalLatency:d.current.totalLatency,audioLength:d.current.audioLength||0};c7.recordMetrics({audioLength:b.audioLength,sttLatency:b.sttLatency,llmLatency:b.llmLatency,ttsLatency:b.ttsLatency,totalLatency:b.totalLatency}),a.onMetrics?.(b)}setTimeout(()=>{f({currentStage:"idle"})},500)},onError:b=>{f({error:b,currentStage:"idle"}),a.onError?.(b)},autoPlay:!0}),k=(0,aE.useCallback)(async b=>{try{f({error:null}),await Promise.all([g.initialize(),h.initialize(),j.initialize()]),i.initialize(b),f({isInitialized:!0}),a.autoStartListening&&(g.startRecording(),f({isListening:!0,currentStage:"idle",error:null}))}catch(c){let b=c instanceof Error?c.message:"Initialization failed";f({error:b,isInitialized:!1}),a.onError?.(b)}},[g,h,j,i,a,f]),l=(0,aE.useCallback)(()=>{if(!b.isInitialized)return void f({error:"Voice assistant not initialized"});g.startRecording(),f({isListening:!0,currentStage:"idle",error:null})},[b.isInitialized,g,f]),m=(0,aE.useCallback)(()=>{g.stopRecording(),f({isListening:!1,currentStage:"idle"})},[g,f]),n=(0,aE.useCallback)(async a=>{if(!b.isInitialized)throw Error("Voice assistant not initialized");f({currentStage:"thinking"});try{let b=await i.sendMessage(a);b&&(f({lastTranscription:a,lastResponse:b.message,currentStage:"speaking"}),await j.synthesizeText(b.message))}catch(a){throw f({error:a instanceof Error?a.message:"Failed to send message",currentStage:"idle"}),a}},[b.isInitialized,i,j,f]);return[b,{initialize:k,startListening:l,stopListening:m,sendTextMessage:n,reset:(0,aE.useCallback)(()=>{g.stopRecording(),h.reset(),i.clearHistory(),j.reset(),c({isInitialized:!1,isListening:!1,isProcessing:!1,currentStage:"idle",error:null,volume:0,lastTranscription:null,lastResponse:null})},[g,h,i,j])}]}({onStageChange:a=>{console.log("Stage changed to:",a)},onTranscription:a=>{console.log("Transcription:",a)},onResponse:a=>{console.log("Response:",a)},onMetrics:a=>{f(a),console.log("Pipeline metrics:",a)},onError:a=>{console.error("Voice assistant error:",a)}});(0,aE.useEffect)(()=>{(async()=>{await c9.register(),h(await c9.getCacheStatus())})();let a=setInterval(async()=>{h(await c9.getCacheStatus())},5e3);return()=>clearInterval(a)},[]);let k=async()=>{if(!a.trim())return void alert("Please enter your OpenAI API key");try{await j.initialize(a),d(!1)}catch(a){console.error("Initialization failed:",a)}},l=a=>{switch(a){case"listening":return"text-green-400";case"transcribing":return"text-blue-400";case"thinking":return"text-yellow-400";case"speaking":return"text-purple-400";default:return"text-gray-400"}};return c?(0,aD.jsx)("div",{className:"min-h-screen flex items-center justify-center p-4",children:(0,aD.jsxs)("div",{className:"bg-gray-800 rounded-lg p-8 max-w-md w-full",children:[(0,aD.jsx)("h1",{className:"text-2xl font-bold mb-6 text-center",children:"Voice Assistant"}),(0,aD.jsxs)("div",{className:"mb-6",children:[(0,aD.jsx)("label",{htmlFor:"apiKey",className:"block text-sm font-medium mb-2",children:"OpenAI API Key"}),(0,aD.jsx)("input",{id:"apiKey",type:"password",value:a,onChange:a=>b(a.target.value),placeholder:"sk-...",className:"w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"})]}),g&&(0,aD.jsxs)("div",{className:"mb-6 p-3 bg-gray-700 rounded-md",children:[(0,aD.jsx)("h3",{className:"text-sm font-medium mb-2",children:"Cache Status"}),(0,aD.jsxs)("div",{className:"text-xs text-gray-300",children:[(0,aD.jsxs)("div",{children:["Models: ",g.modelsCached,"/",g.totalModels]}),(0,aD.jsxs)("div",{children:["Ready: ",g.isReady?"✅":"⏳"]})]})]}),(0,aD.jsx)("button",{onClick:k,disabled:!a.trim(),className:"w-full bg-blue-600 hover:bg-blue-700 disabled:bg-gray-600 disabled:cursor-not-allowed px-4 py-2 rounded-md font-medium transition-colors",children:"Initialize Voice Assistant"})]})}):(0,aD.jsx)("div",{className:"min-h-screen flex flex-col items-center justify-center p-4",children:(0,aD.jsxs)("div",{className:"bg-gray-800 rounded-lg p-8 max-w-2xl w-full",children:[(0,aD.jsx)("h1",{className:"text-3xl font-bold mb-8 text-center",children:"Voice Assistant"}),(0,aD.jsxs)("div",{className:"text-center mb-8",children:[(0,aD.jsx)("div",{className:`text-6xl mb-4 ${l(i.currentStage)}`,children:(a=>{switch(a){case"listening":return"\uD83C\uDFA4";case"transcribing":return"\uD83D\uDCDD";case"thinking":return"\uD83E\uDD14";case"speaking":return"\uD83D\uDD0A";default:return"⭕"}})(i.currentStage)}),(0,aD.jsx)("div",{className:`text-xl font-medium ${l(i.currentStage)}`,children:i.currentStage.charAt(0).toUpperCase()+i.currentStage.slice(1)}),i.isListening&&(0,aD.jsxs)("div",{className:"mt-2",children:[(0,aD.jsx)("div",{className:"w-full bg-gray-700 rounded-full h-2",children:(0,aD.jsx)("div",{className:"bg-green-400 h-2 rounded-full transition-all duration-100",style:{width:`${Math.min(100*i.volume,100)}%`}})}),(0,aD.jsxs)("div",{className:"text-xs text-gray-400 mt-1",children:["Volume: ",(100*i.volume).toFixed(0),"%"]})]})]}),(0,aD.jsx)("div",{className:"flex justify-center gap-4 mb-8",children:i.isListening?(0,aD.jsx)("button",{onClick:()=>{j.stopListening()},className:"bg-red-600 hover:bg-red-700 px-6 py-3 rounded-full font-medium transition-colors flex items-center gap-2",children:"⏹️ Stop Listening"}):(0,aD.jsx)("button",{onClick:()=>{j.startListening()},disabled:!i.isInitialized||i.isProcessing,className:"bg-green-600 hover:bg-green-700 disabled:bg-gray-600 disabled:cursor-not-allowed px-6 py-3 rounded-full font-medium transition-colors flex items-center gap-2",children:"\uD83C\uDFA4 Start Listening"})}),(0,aD.jsxs)("div",{className:"space-y-4 mb-6",children:[i.lastTranscription&&(0,aD.jsxs)("div",{className:"bg-blue-900/30 p-4 rounded-lg",children:[(0,aD.jsx)("div",{className:"text-sm text-blue-300 mb-1",children:"You said:"}),(0,aD.jsx)("div",{className:"text-white",children:i.lastTranscription})]}),i.lastResponse&&(0,aD.jsxs)("div",{className:"bg-purple-900/30 p-4 rounded-lg",children:[(0,aD.jsx)("div",{className:"text-sm text-purple-300 mb-1",children:"Assistant:"}),(0,aD.jsx)("div",{className:"text-white",children:i.lastResponse})]})]}),e&&(0,aD.jsxs)("div",{className:"bg-gray-700 p-4 rounded-lg",children:[(0,aD.jsx)("h3",{className:"text-sm font-medium mb-3",children:"Performance Metrics"}),(0,aD.jsxs)("div",{className:"grid grid-cols-2 gap-4 text-xs",children:[(0,aD.jsxs)("div",{children:[(0,aD.jsx)("div",{className:"text-gray-400",children:"STT Latency"}),(0,aD.jsxs)("div",{className:"font-mono",children:[e.sttLatency.toFixed(0),"ms"]})]}),(0,aD.jsxs)("div",{children:[(0,aD.jsx)("div",{className:"text-gray-400",children:"LLM Latency"}),(0,aD.jsxs)("div",{className:"font-mono",children:[e.llmLatency.toFixed(0),"ms"]})]}),(0,aD.jsxs)("div",{children:[(0,aD.jsx)("div",{className:"text-gray-400",children:"TTS Latency"}),(0,aD.jsxs)("div",{className:"font-mono",children:[e.ttsLatency.toFixed(0),"ms"]})]}),(0,aD.jsxs)("div",{children:[(0,aD.jsx)("div",{className:"text-gray-400",children:"Total Latency"}),(0,aD.jsxs)("div",{className:`font-mono ${e.totalLatency<1200?"text-green-400":"text-yellow-400"}`,children:[e.totalLatency.toFixed(0),"ms"]})]})]}),(0,aD.jsxs)("div",{className:"mt-2 text-xs text-gray-400",children:["Audio Length: ",e.audioLength.toFixed(1),"s"]})]}),i.error&&(0,aD.jsx)("div",{className:"bg-red-900/30 border border-red-600 p-4 rounded-lg mt-4",children:(0,aD.jsxs)("div",{className:"text-red-300 text-sm",children:["Error: ",i.error]})}),(0,aD.jsx)("div",{className:"text-center mt-6",children:(0,aD.jsx)("button",{onClick:()=>{j.reset(),d(!0),f(null)},className:"text-gray-400 hover:text-white text-sm underline",children:"Reset & Change API Key"})})]})})};function db(){return(0,aD.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-gray-900 to-black text-white",children:(0,aD.jsx)(da,{})})}},5968:(a,b,c)=>{Promise.resolve().then(c.t.bind(c,5227,23)),Promise.resolve().then(c.t.bind(c,6346,23)),Promise.resolve().then(c.t.bind(c,7924,23)),Promise.resolve().then(c.t.bind(c,99,23)),Promise.resolve().then(c.t.bind(c,8243,23)),Promise.resolve().then(c.t.bind(c,8827,23)),Promise.resolve().then(c.t.bind(c,2763,23)),Promise.resolve().then(c.t.bind(c,7173,23)),Promise.resolve().then(c.bind(c,5587))},6439:a=>{"use strict";a.exports=require("next/dist/shared/lib/no-fallback-error.external")},6713:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/is-bot")},6837:(a,b,c)=>{Promise.resolve().then(c.bind(c,1204))},7739:(a,b,c)=>{"use strict";c.r(b),c.d(b,{GlobalError:()=>C.a,__next_app__:()=>I,handler:()=>K,pages:()=>H,routeModule:()=>J,tree:()=>G});var d=c(5239),e=c(8088),f=c(7220),g=c(1289),h=c(6191),i=c(4823),j=c(1998),k=c(2603),l=c(4649),m=c(2781),n=c(2602),o=c(1268),p=c(4853),q=c(261),r=c(5052),s=c(9977),t=c(6713),u=c(3365),v=c(1454),w=c(7778),x=c(6143),y=c(9105),z=c(8171),A=c(6439),B=c(6133),C=c.n(B),D=c(893),E=c(2836),F={};for(let a in D)0>["default","tree","pages","GlobalError","__next_app__","routeModule","handler"].indexOf(a)&&(F[a]=()=>D[a]);c.d(b,F);let G={children:["",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(c.bind(c,1204)),"C:\\Web_dev\\coding_jr\\voice-assistant\\src\\app\\page.tsx"],metadata:{icon:[async a=>(await Promise.resolve().then(c.bind(c,440))).default(a)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(c.bind(c,4431)),"C:\\Web_dev\\coding_jr\\voice-assistant\\src\\app\\layout.tsx"],"global-error":[()=>Promise.resolve().then(c.t.bind(c,6133,23)),"next/dist/client/components/builtin/global-error.js"],"not-found":[()=>Promise.resolve().then(c.t.bind(c,849,23)),"next/dist/client/components/builtin/not-found.js"],forbidden:[()=>Promise.resolve().then(c.t.bind(c,9868,23)),"next/dist/client/components/builtin/forbidden.js"],unauthorized:[()=>Promise.resolve().then(c.t.bind(c,9615,23)),"next/dist/client/components/builtin/unauthorized.js"],metadata:{icon:[async a=>(await Promise.resolve().then(c.bind(c,440))).default(a)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,H=["C:\\Web_dev\\coding_jr\\voice-assistant\\src\\app\\page.tsx"],I={require:c,loadChunk:()=>Promise.resolve()},J=new d.AppPageRouteModule({definition:{kind:e.RouteKind.APP_PAGE,page:"/page",pathname:"/",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:G},distDir:".next",projectDir:""});async function K(a,b,c){var d;let B="/page";"/index"===B&&(B="/");let F="false",L=(0,h.getRequestMeta)(a,"postponed"),M=(0,h.getRequestMeta)(a,"minimalMode"),N=await J.prepare(a,b,{srcPage:B,multiZoneDraftMode:F});if(!N)return b.statusCode=400,b.end("Bad Request"),null==c.waitUntil||c.waitUntil.call(c,Promise.resolve()),null;let{buildId:O,query:P,params:Q,parsedUrl:R,pageIsDynamic:S,buildManifest:T,nextFontManifest:U,reactLoadableManifest:V,serverActionsManifest:W,clientReferenceManifest:X,subresourceIntegrityManifest:Y,prerenderManifest:Z,isDraftMode:$,resolvedPathname:_,revalidateOnlyGenerated:aa,routerServerContext:ab,nextConfig:ac}=N,ad=R.pathname||"/",ae=(0,q.normalizeAppPath)(B),{isOnDemandRevalidate:af}=N,ag=Z.dynamicRoutes[ae],ah=Z.routes[_],ai=!!(ag||ah||Z.routes[ae]),aj=a.headers["user-agent"]||"",ak=(0,t.getBotType)(aj),al=(0,o.isHtmlBotRequest)(a),am=(0,h.getRequestMeta)(a,"isPrefetchRSCRequest")??!!a.headers[s.NEXT_ROUTER_PREFETCH_HEADER],an=(0,h.getRequestMeta)(a,"isRSCRequest")??!!a.headers[s.RSC_HEADER],ao=(0,r.getIsPossibleServerAction)(a),ap=(0,l.checkIsAppPPREnabled)(ac.experimental.ppr)&&(null==(d=Z.routes[ae]??Z.dynamicRoutes[ae])?void 0:d.renderingMode)==="PARTIALLY_STATIC",aq=!1,ar=!1,as=ap?L:void 0,at=ap&&an&&!am,au=(0,h.getRequestMeta)(a,"segmentPrefetchRSCRequest"),av=!aj||(0,o.shouldServeStreamingMetadata)(aj,ac.htmlLimitedBots);al&&ap&&(ai=!1,av=!1);let aw=!0===J.isDev||!ai||"string"==typeof L||at,ax=al&&ap,ay=null;$||!ai||aw||ao||as||at||(ay=_);let az=ay;!az&&J.isDev&&(az=_);let aA={...D,tree:G,pages:H,GlobalError:C(),handler:K,routeModule:J,__next_app__:I};W&&X&&(0,n.setReferenceManifestsSingleton)({page:B,clientReferenceManifest:X,serverActionsManifest:W,serverModuleMap:(0,p.createServerModuleMap)({serverActionsManifest:W})});let aB=a.method||"GET",aC=(0,g.getTracer)(),aD=aC.getActiveScopeSpan();try{let d=async(c,d)=>{let e=new k.NodeNextRequest(a),f=new k.NodeNextResponse(b);return J.render(e,f,d).finally(()=>{if(!c)return;c.setAttributes({"http.status_code":b.statusCode,"next.rsc":!1});let d=aC.getRootSpanAttributes();if(!d)return;if(d.get("next.span_type")!==i.BaseServerSpan.handleRequest)return void console.warn(`Unexpected root span type '${d.get("next.span_type")}'. Please report this Next.js issue https://github.com/vercel/next.js`);let e=d.get("next.route");if(e){let a=`${aB} ${e}`;c.setAttributes({"next.route":e,"http.route":e,"next.span_name":a}),c.updateName(a)}else c.updateName(`${aB} ${a.url}`)})},f=async({span:e,postponed:f,fallbackRouteParams:g})=>{let i={query:P,params:Q,page:ae,sharedContext:{buildId:O},serverComponentsHmrCache:(0,h.getRequestMeta)(a,"serverComponentsHmrCache"),fallbackRouteParams:g,renderOpts:{App:()=>null,Document:()=>null,pageConfig:{},ComponentMod:aA,Component:(0,j.T)(aA),params:Q,routeModule:J,page:B,postponed:f,shouldWaitOnAllReady:ax,serveStreamingMetadata:av,supportsDynamicResponse:"string"==typeof f||aw,buildManifest:T,nextFontManifest:U,reactLoadableManifest:V,subresourceIntegrityManifest:Y,serverActionsManifest:W,clientReferenceManifest:X,setIsrStatus:null==ab?void 0:ab.setIsrStatus,dir:J.projectDir,isDraftMode:$,isRevalidate:ai&&!f&&!at,botType:ak,isOnDemandRevalidate:af,isPossibleServerAction:ao,assetPrefix:ac.assetPrefix,nextConfigOutput:ac.output,crossOrigin:ac.crossOrigin,trailingSlash:ac.trailingSlash,previewProps:Z.preview,deploymentId:ac.deploymentId,enableTainting:ac.experimental.taint,htmlLimitedBots:ac.htmlLimitedBots,devtoolSegmentExplorer:ac.experimental.devtoolSegmentExplorer,reactMaxHeadersLength:ac.reactMaxHeadersLength,multiZoneDraftMode:F,incrementalCache:(0,h.getRequestMeta)(a,"incrementalCache"),cacheLifeProfiles:ac.experimental.cacheLife,basePath:ac.basePath,serverActions:ac.experimental.serverActions,...aq?{nextExport:!0,supportsDynamicResponse:!1,isStaticGeneration:!0,isRevalidate:!0,isDebugDynamicAccesses:aq}:{},experimental:{isRoutePPREnabled:ap,expireTime:ac.expireTime,staleTimes:ac.experimental.staleTimes,dynamicIO:!!ac.experimental.dynamicIO,clientSegmentCache:!!ac.experimental.clientSegmentCache,dynamicOnHover:!!ac.experimental.dynamicOnHover,inlineCss:!!ac.experimental.inlineCss,authInterrupts:!!ac.experimental.authInterrupts,clientTraceMetadata:ac.experimental.clientTraceMetadata||[]},waitUntil:c.waitUntil,onClose:a=>{b.on("close",a)},onAfterTaskError:()=>{},onInstrumentationRequestError:(b,c,d)=>J.onRequestError(a,b,d,ab),err:(0,h.getRequestMeta)(a,"invokeError"),dev:J.isDev}},k=await d(e,i),{metadata:l}=k,{cacheControl:m,headers:n={},fetchTags:o}=l;if(o&&(n[x.NEXT_CACHE_TAGS_HEADER]=o),a.fetchMetrics=l.fetchMetrics,ai&&(null==m?void 0:m.revalidate)===0&&!J.isDev&&!ap){let a=l.staticBailoutInfo,b=Object.defineProperty(Error(`Page changed from static to dynamic at runtime ${_}${(null==a?void 0:a.description)?`, reason: ${a.description}`:""}
see more here https://nextjs.org/docs/messages/app-static-to-dynamic-error`),"__NEXT_ERROR_CODE",{value:"E132",enumerable:!1,configurable:!0});if(null==a?void 0:a.stack){let c=a.stack;b.stack=b.message+c.substring(c.indexOf("\n"))}throw b}return{value:{kind:u.CachedRouteKind.APP_PAGE,html:k,headers:n,rscData:l.flightData,postponed:l.postponed,status:l.statusCode,segmentData:l.segmentData},cacheControl:m}},l=async({hasResolved:d,previousCacheEntry:g,isRevalidating:i,span:j})=>{let k,l=!1===J.isDev,n=d||b.writableEnded;if(af&&aa&&!g&&!M)return(null==ab?void 0:ab.render404)?await ab.render404(a,b):(b.statusCode=404,b.end("This page could not be found")),null;if(ag&&(k=(0,v.parseFallbackField)(ag.fallback)),k===v.FallbackMode.PRERENDER&&(0,t.isBot)(aj)&&(k=v.FallbackMode.BLOCKING_STATIC_RENDER),(null==g?void 0:g.isStale)===-1&&(af=!0),af&&(k!==v.FallbackMode.NOT_FOUND||g)&&(k=v.FallbackMode.BLOCKING_STATIC_RENDER),!M&&k!==v.FallbackMode.BLOCKING_STATIC_RENDER&&az&&!n&&!$&&S&&(l||!ah)){let b;if((l||ag)&&k===v.FallbackMode.NOT_FOUND)throw new A.NoFallbackError;if(ap&&!an){if(b=await J.handleResponse({cacheKey:l?ae:null,req:a,nextConfig:ac,routeKind:e.RouteKind.APP_PAGE,isFallback:!0,prerenderManifest:Z,isRoutePPREnabled:ap,responseGenerator:async()=>f({span:j,postponed:void 0,fallbackRouteParams:l||ar?(0,m.u)(ae):null}),waitUntil:c.waitUntil}),null===b)return null;if(b)return delete b.cacheControl,b}}let o=af||i||!as?void 0:as;if(aq&&void 0!==o)return{cacheControl:{revalidate:1,expire:void 0},value:{kind:u.CachedRouteKind.PAGES,html:w.default.fromStatic(""),pageData:{},headers:void 0,status:void 0}};let p=S&&ap&&((0,h.getRequestMeta)(a,"renderFallbackShell")||ar)?(0,m.u)(ad):null;return f({span:j,postponed:o,fallbackRouteParams:p})},n=async d=>{var g,i,j,k,m;let n,o=await J.handleResponse({cacheKey:ay,responseGenerator:a=>l({span:d,...a}),routeKind:e.RouteKind.APP_PAGE,isOnDemandRevalidate:af,isRoutePPREnabled:ap,req:a,nextConfig:ac,prerenderManifest:Z,waitUntil:c.waitUntil});if($&&b.setHeader("Cache-Control","private, no-cache, no-store, max-age=0, must-revalidate"),J.isDev&&b.setHeader("Cache-Control","no-store, must-revalidate"),!o){if(ay)throw Object.defineProperty(Error("invariant: cache entry required but not generated"),"__NEXT_ERROR_CODE",{value:"E62",enumerable:!1,configurable:!0});return null}if((null==(g=o.value)?void 0:g.kind)!==u.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant app-page handler received invalid cache entry ${null==(j=o.value)?void 0:j.kind}`),"__NEXT_ERROR_CODE",{value:"E707",enumerable:!1,configurable:!0});let p="string"==typeof o.value.postponed;ai&&!at&&(!p||am)&&(M||b.setHeader("x-nextjs-cache",af?"REVALIDATED":o.isMiss?"MISS":o.isStale?"STALE":"HIT"),b.setHeader(s.NEXT_IS_PRERENDER_HEADER,"1"));let{value:q}=o;if(as)n={revalidate:0,expire:void 0};else if(M&&an&&!am&&ap)n={revalidate:0,expire:void 0};else if(!J.isDev)if($)n={revalidate:0,expire:void 0};else if(ai){if(o.cacheControl)if("number"==typeof o.cacheControl.revalidate){if(o.cacheControl.revalidate<1)throw Object.defineProperty(Error(`Invalid revalidate configuration provided: ${o.cacheControl.revalidate} < 1`),"__NEXT_ERROR_CODE",{value:"E22",enumerable:!1,configurable:!0});n={revalidate:o.cacheControl.revalidate,expire:(null==(k=o.cacheControl)?void 0:k.expire)??ac.expireTime}}else n={revalidate:x.CACHE_ONE_YEAR,expire:void 0}}else b.getHeader("Cache-Control")||(n={revalidate:0,expire:void 0});if(o.cacheControl=n,"string"==typeof au&&(null==q?void 0:q.kind)===u.CachedRouteKind.APP_PAGE&&q.segmentData){b.setHeader(s.NEXT_DID_POSTPONE_HEADER,"2");let c=null==(m=q.headers)?void 0:m[x.NEXT_CACHE_TAGS_HEADER];M&&ai&&c&&"string"==typeof c&&b.setHeader(x.NEXT_CACHE_TAGS_HEADER,c);let d=q.segmentData.get(au);return void 0!==d?(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(d),cacheControl:o.cacheControl}):(b.statusCode=204,(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(""),cacheControl:o.cacheControl}))}let r=(0,h.getRequestMeta)(a,"onCacheEntry");if(r&&await r({...o,value:{...o.value,kind:"PAGE"}},{url:(0,h.getRequestMeta)(a,"initURL")}))return null;if(p&&as)throw Object.defineProperty(Error("Invariant: postponed state should not be present on a resume request"),"__NEXT_ERROR_CODE",{value:"E396",enumerable:!1,configurable:!0});if(q.headers){let a={...q.headers};for(let[c,d]of(M&&ai||delete a[x.NEXT_CACHE_TAGS_HEADER],Object.entries(a)))if(void 0!==d)if(Array.isArray(d))for(let a of d)b.appendHeader(c,a);else"number"==typeof d&&(d=d.toString()),b.appendHeader(c,d)}let t=null==(i=q.headers)?void 0:i[x.NEXT_CACHE_TAGS_HEADER];if(M&&ai&&t&&"string"==typeof t&&b.setHeader(x.NEXT_CACHE_TAGS_HEADER,t),!q.status||an&&ap||(b.statusCode=q.status),!M&&q.status&&E.RedirectStatusCode[q.status]&&an&&(b.statusCode=200),p&&b.setHeader(s.NEXT_DID_POSTPONE_HEADER,"1"),an&&!$){if(void 0===q.rscData){if(q.postponed)throw Object.defineProperty(Error("Invariant: Expected postponed to be undefined"),"__NEXT_ERROR_CODE",{value:"E372",enumerable:!1,configurable:!0});return(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:q.html,cacheControl:at?{revalidate:0,expire:void 0}:o.cacheControl})}return(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(q.rscData),cacheControl:o.cacheControl})}let v=q.html;if(!p||M)return(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:o.cacheControl});if(aq)return v.chain(new ReadableStream({start(a){a.enqueue(y.ENCODED_TAGS.CLOSED.BODY_AND_HTML),a.close()}})),(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:{revalidate:0,expire:void 0}});let A=new TransformStream;return v.chain(A.readable),f({span:d,postponed:q.postponed,fallbackRouteParams:null}).then(async a=>{var b,c;if(!a)throw Object.defineProperty(Error("Invariant: expected a result to be returned"),"__NEXT_ERROR_CODE",{value:"E463",enumerable:!1,configurable:!0});if((null==(b=a.value)?void 0:b.kind)!==u.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant: expected a page response, got ${null==(c=a.value)?void 0:c.kind}`),"__NEXT_ERROR_CODE",{value:"E305",enumerable:!1,configurable:!0});await a.value.html.pipeTo(A.writable)}).catch(a=>{A.writable.abort(a).catch(a=>{console.error("couldn't abort transformer",a)})}),(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:{revalidate:0,expire:void 0}})};if(!aD)return await aC.withPropagatedContext(a.headers,()=>aC.trace(i.BaseServerSpan.handleRequest,{spanName:`${aB} ${a.url}`,kind:g.SpanKind.SERVER,attributes:{"http.method":aB,"http.target":a.url}},n));await n(aD)}catch(b){throw aD||b instanceof A.NoFallbackError||await J.onRequestError(a,b,{routerKind:"App Router",routePath:B,routeType:"render",revalidateReason:(0,f.c)({isRevalidate:ai,isOnDemandRevalidate:af})},ab),b}}},7868:()=>{},8354:a=>{"use strict";a.exports=require("util")},9120:(a,b,c)=>{Promise.resolve().then(c.t.bind(c,6133,23)),Promise.resolve().then(c.t.bind(c,6444,23)),Promise.resolve().then(c.t.bind(c,6042,23)),Promise.resolve().then(c.t.bind(c,9477,23)),Promise.resolve().then(c.t.bind(c,9345,23)),Promise.resolve().then(c.t.bind(c,2089,23)),Promise.resolve().then(c.t.bind(c,6577,23)),Promise.resolve().then(c.t.bind(c,1307,23)),Promise.resolve().then(c.t.bind(c,4817,23))},9121:a=>{"use strict";a.exports=require("next/dist/server/app-render/action-async-storage.external.js")},9294:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-async-storage.external.js")}};var b=require("../webpack-runtime.js");b.C(a);var c=b.X(0,[985,400],()=>b(b.s=7739));module.exports=c})();