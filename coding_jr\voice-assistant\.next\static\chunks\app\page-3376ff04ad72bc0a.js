(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[974],{2963:(e,t,r)=>{Promise.resolve().then(r.bind(r,4360))},4360:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>g});var s=r(5155),i=r(2115);class n{async initialize(){try{this.mediaStream=await navigator.mediaDevices.getUserMedia({audio:{sampleRate:16e3,channelCount:1,echoCancellation:!0,noiseSuppression:!0,autoGainControl:!0}}),this.audioContext=new AudioContext({sampleRate:16e3}),this.analyser=this.audioContext.createAnalyser(),this.analyser.fftSize=2048,this.analyser.smoothingTimeConstant=.8;let e=this.audioContext.createMediaStreamSource(this.mediaStream);e.connect(this.analyser);try{await this.audioContext.audioWorklet.addModule("/audio-processor.js"),this.workletNode=new AudioWorkletNode(this.audioContext,"audio-processor"),this.workletNode.port.onmessage=e=>{let{audioData:t,timestamp:r}=e.data;this.handleAudioData(t,r)},e.connect(this.workletNode)}catch(t){console.warn("AudioWorklet not available, falling back to ScriptProcessor"),this.processor=this.audioContext.createScriptProcessor(this.bufferSize,1,1),this.processor.onaudioprocess=e=>{let t=e.inputBuffer.getChannelData(0);this.handleAudioData(new Float32Array(t),this.audioContext.currentTime)},e.connect(this.processor),this.processor.connect(this.audioContext.destination)}console.log("Audio manager initialized successfully")}catch(s){var e,t;let r=Error("Failed to initialize audio: ".concat(s));throw null==(e=(t=this.events).onError)||e.call(t,r),r}}handleAudioData(e,t){var r,s,i,n,a;if(!this.isRecording)return;let o=this.calculateRMS(e);null==(r=(s=this.events).onVolumeChange)||r.call(s,o),this.performVAD(o),this.audioBuffer.push(new Float32Array(e));let l={data:new Float32Array(e),timestamp:t,sampleRate:(null==(i=this.audioContext)?void 0:i.sampleRate)||16e3};null==(n=(a=this.events).onAudioChunk)||n.call(a,l),this.audioBuffer.length>=10&&this.sendBufferedAudio()}calculateRMS(e){let t=0;for(let r=0;r<e.length;r++)t+=e[r]*e[r];return Math.sqrt(t/e.length)}performVAD(e){var t,r,s,i;e>this.vadThreshold?(this.vadSpeechFrames++,this.vadSilenceFrames=0,!this.isSpeaking&&this.vadSpeechFrames>=this.vadSpeechThreshold&&(this.isSpeaking=!0,null==(t=(r=this.events).onSpeechStart)||t.call(r))):(this.vadSilenceFrames++,this.vadSpeechFrames=0,this.isSpeaking&&this.vadSilenceFrames>=this.vadSilenceThreshold&&(this.isSpeaking=!1,null==(s=(i=this.events).onSpeechEnd)||s.call(i),this.sendBufferedAudio()))}sendBufferedAudio(){var e,t,r,s;if(0===this.audioBuffer.length)return;let i=new Float32Array(this.audioBuffer.reduce((e,t)=>e+t.length,0)),n=0;for(let e of this.audioBuffer)i.set(e,n),n+=e.length;this.audioBuffer=[];let a={data:i,timestamp:(null==(e=this.audioContext)?void 0:e.currentTime)||Date.now(),sampleRate:(null==(t=this.audioContext)?void 0:t.sampleRate)||16e3};null==(r=(s=this.events).onAudioChunk)||r.call(s,a)}startRecording(){if(!this.audioContext)throw Error("Audio manager not initialized");"suspended"===this.audioContext.state&&this.audioContext.resume(),this.isRecording=!0,this.audioBuffer=[],console.log("Recording started")}stopRecording(){this.isRecording=!1,this.sendBufferedAudio(),console.log("Recording stopped")}setVADThreshold(e){this.vadThreshold=e}getVADThreshold(){return this.vadThreshold}isCurrentlyRecording(){return this.isRecording}isCurrentlySpeaking(){return this.isSpeaking}async cleanup(){this.stopRecording(),this.processor&&(this.processor.disconnect(),this.processor=null),this.workletNode&&(this.workletNode.disconnect(),this.workletNode=null),this.analyser&&(this.analyser.disconnect(),this.analyser=null),this.audioContext&&(await this.audioContext.close(),this.audioContext=null),this.mediaStream&&(this.mediaStream.getTracks().forEach(e=>e.stop()),this.mediaStream=null),console.log("Audio manager cleaned up")}constructor(e={}){this.mediaStream=null,this.audioContext=null,this.analyser=null,this.processor=null,this.workletNode=null,this.isRecording=!1,this.events={},this.vadThreshold=.01,this.vadSilenceFrames=0,this.vadSilenceThreshold=30,this.vadSpeechFrames=0,this.vadSpeechThreshold=5,this.isSpeaking=!1,this.audioBuffer=[],this.bufferSize=4096,this.events=e}}var a=r(9617);class o{async sendMessage(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},r=performance.now();try{var s,i;let n={role:"user",content:e,timestamp:Date.now()};this.conversationHistory.push(n);let a=[],o=t.systemPrompt||this.defaultSystemPrompt;a.push({role:"system",content:o});let l=this.conversationHistory.slice(-10);a.push(...l);let c=await this.client.chat.completions.create({model:t.model||"gpt-3.5-turbo",messages:a.map(e=>({role:e.role,content:e.content})),temperature:t.temperature||.7,max_tokens:t.maxTokens||150,stream:!1}),u=performance.now()-r,d=(null==(i=c.choices[0])||null==(s=i.message)?void 0:s.content)||"",h={role:"assistant",content:d,timestamp:Date.now()};this.conversationHistory.push(h);let g={message:d,usage:c.usage?{promptTokens:c.usage.prompt_tokens,completionTokens:c.usage.completion_tokens,totalTokens:c.usage.total_tokens}:void 0,processingTime:u};return console.log("OpenAI response received in ".concat(u.toFixed(2),"ms")),g}catch(t){let e=performance.now();return console.error("OpenAI API error:",t),{message:"Sorry, I encountered an error processing your request.",processingTime:e-r}}}getConversationHistory(){return[...this.conversationHistory]}clearConversationHistory(){this.conversationHistory=[]}setSystemPrompt(e){this.defaultSystemPrompt=e}getSystemPrompt(){return this.defaultSystemPrompt}trimConversationHistory(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:20;this.conversationHistory.length>e&&(this.conversationHistory=this.conversationHistory.slice(-e))}constructor(e){this.conversationHistory=[],this.defaultSystemPrompt="You are a helpful voice assistant. Keep your responses concise and conversational, as they will be spoken aloud. Aim for responses that are 1-2 sentences unless more detail is specifically requested.",this.client=new a.Ay({apiKey:e,dangerouslyAllowBrowser:!0})}}class l{generateSessionId(){return"session_".concat(Date.now(),"_").concat(Math.random().toString(36).substr(2,9))}getDeviceInfo(){let e=navigator;return{platform:navigator.platform,memory:e.deviceMemory,cores:e.hardwareConcurrency}}recordMetrics(e){let t={...e,timestamp:Date.now(),sessionId:this.sessionId,userAgent:navigator.userAgent,deviceInfo:this.getDeviceInfo()};this.metrics.push(t),this.metrics.length>this.maxMetrics&&(this.metrics=this.metrics.slice(-this.maxMetrics)),this.logPerformance(t),this.persistMetrics()}logPerformance(e){let{totalLatency:t,sttLatency:r,llmLatency:s,ttsLatency:i,audioLength:n}=e;console.group("\uD83D\uDD0D Performance Metrics"),console.log("Total Latency: ".concat(t.toFixed(0),"ms ").concat(t<1200?"✅":"⚠️")),console.log("STT: ".concat(r.toFixed(0),"ms")),console.log("LLM: ".concat(s.toFixed(0),"ms")),console.log("TTS: ".concat(i.toFixed(0),"ms")),console.log("Audio Length: ".concat(n.toFixed(1),"s")),console.log("Efficiency: ".concat((1e3*n/t).toFixed(2),"x realtime")),t>1200&&(console.warn("⚠️ Total latency exceeds 1.2s target"),this.analyzeBottlenecks(e)),console.groupEnd()}analyzeBottlenecks(e){let{sttLatency:t,llmLatency:r,ttsLatency:s}=e,i=t+r+s;console.group("\uD83D\uDD0D Bottleneck Analysis"),t>.4*i&&(console.warn("STT is the bottleneck (>40% of total time)"),console.log("Recommendations: Use smaller Whisper model, optimize audio preprocessing")),r>.5*i&&(console.warn("LLM is the bottleneck (>50% of total time)"),console.log("Recommendations: Use faster model (gpt-3.5-turbo), reduce max_tokens, optimize prompt")),s>.3*i&&(console.warn("TTS is the bottleneck (>30% of total time)"),console.log("Recommendations: Use smaller TTS model, optimize text preprocessing")),console.groupEnd()}persistMetrics(){try{let e=this.metrics.slice(-100);localStorage.setItem("voiceAssistantMetrics",JSON.stringify(e))}catch(e){console.warn("Failed to persist metrics:",e)}}loadPersistedMetrics(){try{let e=localStorage.getItem("voiceAssistantMetrics");if(e){let t=JSON.parse(e);Array.isArray(t)&&(this.metrics=t)}}catch(e){console.warn("Failed to load persisted metrics:",e)}}generateReport(){if(0===this.metrics.length)return{totalSessions:0,averageLatency:0,medianLatency:0,p95Latency:0,successRate:0,cacheHitRate:0,breakdown:{stt:{avg:0,median:0,p95:0},llm:{avg:0,median:0,p95:0},tts:{avg:0,median:0,p95:0}},recommendations:[]};let e=this.metrics.map(e=>e.totalLatency).sort((e,t)=>e-t),t=this.metrics.map(e=>e.sttLatency).sort((e,t)=>e-t),r=this.metrics.map(e=>e.llmLatency).sort((e,t)=>e-t),s=this.metrics.map(e=>e.ttsLatency).sort((e,t)=>e-t),i=e=>e.reduce((e,t)=>e+t,0)/e.length,n=e=>e[Math.floor(e.length/2)],a=e=>e[Math.floor(.95*e.length)],o=this.metrics.filter(e=>e.cacheHit).length,l=this.metrics.filter(e=>e.totalLatency>0).length,c={totalSessions:this.metrics.length,averageLatency:i(e),medianLatency:n(e),p95Latency:a(e),successRate:l/this.metrics.length,cacheHitRate:o/this.metrics.length,breakdown:{stt:{avg:i(t),median:n(t),p95:a(t)},llm:{avg:i(r),median:n(r),p95:a(r)},tts:{avg:i(s),median:n(s),p95:a(s)}},recommendations:this.generateRecommendations(c)};return c}generateRecommendations(e){let t=[];return e.averageLatency>1200&&t.push("Average latency exceeds 1.2s target. Consider optimizing the pipeline."),e.breakdown.stt.avg>500&&t.push("STT latency is high. Consider using whisper-tiny or optimizing audio preprocessing."),e.breakdown.llm.avg>800&&t.push("LLM latency is high. Consider using gpt-3.5-turbo or reducing max_tokens."),e.breakdown.tts.avg>400&&t.push("TTS latency is high. Consider using a smaller TTS model or optimizing text preprocessing."),e.cacheHitRate<.8&&t.push("Low cache hit rate. Ensure models are properly cached."),e.successRate<.95&&t.push("Low success rate. Check for errors in the pipeline."),t}exportMetrics(){return JSON.stringify(this.metrics,null,2)}clearMetrics(){this.metrics=[],localStorage.removeItem("voiceAssistantMetrics")}getRecentMetrics(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:10;return this.metrics.slice(-e)}init(){this.loadPersistedMetrics()}constructor(){this.metrics=[],this.maxMetrics=1e3,this.sessionId=this.generateSessionId()}}let c=new l;c.init();class u{async register(){if(!("serviceWorker"in navigator))return console.warn("Service Worker not supported"),null;try{return this.registration=await navigator.serviceWorker.register("/sw.js"),console.log("Service Worker registered successfully"),this.registration.addEventListener("updatefound",()=>{var e;let t=null==(e=this.registration)?void 0:e.installing;t&&t.addEventListener("statechange",()=>{"installed"===t.state&&navigator.serviceWorker.controller&&console.log("New Service Worker available")})}),navigator.serviceWorker.addEventListener("message",e=>{var t;if((null==(t=e.data)?void 0:t.type)==="CACHE_STATUS"){let t={...e.data,isReady:e.data.modelsCached===e.data.totalModels};this.cacheStatusCallbacks.forEach(e=>e(t))}}),this.registration}catch(e){return console.error("Service Worker registration failed:",e),null}}async getCacheStatus(){return this.registration&&navigator.serviceWorker.controller?new Promise(e=>{let t=new MessageChannel;t.port1.onmessage=t=>{var r;(null==(r=t.data)?void 0:r.type)==="CACHE_STATUS"&&e({...t.data,isReady:t.data.modelsCached===t.data.totalModels})},navigator.serviceWorker.controller.postMessage({type:"GET_CACHE_STATUS"},[t.port2])}):null}onCacheStatusUpdate(e){return this.cacheStatusCallbacks.push(e),()=>{let t=this.cacheStatusCallbacks.indexOf(e);t>-1&&this.cacheStatusCallbacks.splice(t,1)}}async skipWaiting(){var e;(null==(e=this.registration)?void 0:e.waiting)&&this.registration.waiting.postMessage({type:"SKIP_WAITING"})}async unregister(){return!!this.registration&&await this.registration.unregister()}constructor(){this.registration=null,this.cacheStatusCallbacks=[]}}let d=new u,h=()=>{let[e,t]=(0,i.useState)(""),[r,a]=(0,i.useState)(!0),[l,u]=(0,i.useState)(null),[h,g]=(0,i.useState)(null),[m,p]=function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},[t,r]=(0,i.useState)({isInitialized:!1,isListening:!1,isProcessing:!1,currentStage:"idle",error:null,volume:0,lastTranscription:null,lastResponse:null}),s=(0,i.useRef)({}),a=(0,i.useRef)(0),l=(0,i.useCallback)(t=>{r(r=>{let s={...r,...t};if(t.currentStage&&t.currentStage!==r.currentStage){var i;null==(i=e.onStageChange)||i.call(e,t.currentStage)}return s})},[e]),[,u]=function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=(0,i.useRef)(null),[r,s]=(0,i.useState)({isInitialized:!1,isRecording:!1,isSpeaking:!1,volume:0,error:null}),a=(0,i.useCallback)(e=>{s(t=>({...t,...e}))},[]),o=(0,i.useCallback)(async()=>{try{a({error:null}),t.current=new n({onAudioChunk:t=>{var r;null==(r=e.onAudioChunk)||r.call(e,t)},onSpeechStart:()=>{var t;a({isSpeaking:!0}),null==(t=e.onSpeechStart)||t.call(e)},onSpeechEnd:()=>{var t;a({isSpeaking:!1}),null==(t=e.onSpeechEnd)||t.call(e)},onError:t=>{var r;a({error:t.message}),console.error("Audio manager error:",t),null==(r=e.onError)||r.call(e,t)},onVolumeChange:t=>{var r;a({volume:t}),null==(r=e.onVolumeChange)||r.call(e,t)}}),await t.current.initialize(),a({isInitialized:!0}),e.autoStart&&(t.current.startRecording(),a({isRecording:!0,error:null}))}catch(e){a({error:e instanceof Error?e.message:"Unknown audio error",isInitialized:!1})}},[e,a]),l=(0,i.useCallback)(()=>{if(!t.current)return void a({error:"Audio manager not initialized"});try{t.current.startRecording(),a({isRecording:!0,error:null})}catch(e){a({error:e instanceof Error?e.message:"Failed to start recording"})}},[a]),c=(0,i.useCallback)(()=>{t.current&&(t.current.stopRecording(),a({isRecording:!1,isSpeaking:!1}))},[a]),u=(0,i.useCallback)(async()=>{t.current&&(await t.current.cleanup(),t.current=null),s({isInitialized:!1,isRecording:!1,isSpeaking:!1,volume:0,error:null})},[]),d=(0,i.useCallback)(e=>{var r;null==(r=t.current)||r.setVADThreshold(e)},[]),h=(0,i.useCallback)(()=>{var e;return(null==(e=t.current)?void 0:e.getVADThreshold())||.01},[]);return(0,i.useEffect)(()=>()=>{u()},[u]),(0,i.useEffect)(()=>{let e=setInterval(()=>{if(t.current){let e=t.current.isCurrentlyRecording(),r=t.current.isCurrentlySpeaking();s(t=>t.isRecording!==e||t.isSpeaking!==r?{...t,isRecording:e,isSpeaking:r}:t)}},100);return()=>clearInterval(e)},[]),[r,{initialize:o,startRecording:l,stopRecording:c,cleanup:u,setVADThreshold:d,getVADThreshold:h}]}({onAudioChunk:e=>{"listening"===t.currentStage&&d.bufferAudio(e)},onSpeechStart:()=>{console.log("Speech started"),a.current=performance.now(),s.current={},l({currentStage:"listening"})},onSpeechEnd:()=>{console.log("Speech ended"),"listening"===t.currentStage&&(l({currentStage:"transcribing"}),d.flushBuffer())},onVolumeChange:e=>{l({volume:e})},onError:t=>{var r;l({error:t.message}),null==(r=e.onError)||r.call(e,t.message)}}),[,d]=function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=(0,i.useRef)(null),[r,s]=(0,i.useState)({isInitialized:!1,isTranscribing:!1,error:null,lastTranscription:null,initializationProgress:0}),n=(0,i.useCallback)(e=>{s(t=>({...t,...e}))},[]),a=(0,i.useCallback)(async()=>{try{n({error:null,initializationProgress:0}),t.current=new Worker("/whisper-worker.js"),t.current.onmessage=t=>{var r,s;let{type:i,...a}=t.data;switch(i){case"initialized":a.success?(n({isInitialized:!0,initializationProgress:100,error:null}),console.log("Whisper STT initialized successfully")):n({error:a.error||"Failed to initialize Whisper",isInitialized:!1,initializationProgress:0});break;case"transcription":n({isTranscribing:!1,lastTranscription:a.result,error:null}),null==(r=e.onTranscription)||r.call(e,a.result);break;case"error":let o=a.error||"Unknown transcription error";n({error:o,isTranscribing:!1}),null==(s=e.onError)||s.call(e,o);break;case"progress":n({initializationProgress:a.progress||0});break;default:console.warn("Unknown message type from Whisper worker:",i)}},t.current.onerror=t=>{var r;let s="Worker error: ".concat(t.message);n({error:s,isInitialized:!1,initializationProgress:0}),null==(r=e.onError)||r.call(e,s)},n({initializationProgress:10}),t.current.postMessage({type:"initialize"})}catch(s){var r;let t=s instanceof Error?s.message:"Failed to create Whisper worker";n({error:t,isInitialized:!1,initializationProgress:0}),null==(r=e.onError)||r.call(e,t)}},[e,n]),o=(0,i.useCallback)(async function(e){let s=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(!t.current||!r.isInitialized)throw Error("Whisper STT not initialized");n({isTranscribing:!0,error:null}),t.current.postMessage({type:"transcribe",data:{audioData:Array.from(e),options:s}})},[r.isInitialized,n]),l=(0,i.useCallback)(e=>{t.current&&r.isInitialized&&t.current.postMessage({type:"buffer-audio",data:{data:Array.from(e.data),timestamp:e.timestamp,sampleRate:e.sampleRate}})},[r.isInitialized]),c=(0,i.useCallback)(()=>{t.current&&r.isInitialized&&t.current.postMessage({type:"flush-buffer"})},[r.isInitialized]),u=(0,i.useCallback)(()=>{t.current&&(t.current.postMessage({type:"reset"}),n({isTranscribing:!1,lastTranscription:null,error:null}))},[n]);return(0,i.useEffect)(()=>{e.autoInitialize&&a()},[e.autoInitialize,a]),(0,i.useEffect)(()=>()=>{t.current&&(t.current.terminate(),t.current=null)},[]),[r,{initialize:a,transcribeAudio:o,bufferAudio:l,flushBuffer:c,reset:u}]}({onTranscription:t=>{var r;let i=performance.now();if(s.current.sttLatency=i-a.current,s.current.audioLength=t.audioLength,console.log('STT completed: "'.concat(t.text,'" (').concat(s.current.sttLatency.toFixed(2),"ms)")),l({lastTranscription:t.text,currentStage:"thinking"}),null==(r=e.onTranscription)||r.call(e,t.text),t.text.trim()){let e=performance.now();h.sendMessage(t.text).then(()=>{let t=performance.now();s.current.llmLatency=t-e})}},onError:t=>{var r;l({error:t,currentStage:"idle"}),null==(r=e.onError)||r.call(e,t)}}),[,h]=function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=(0,i.useRef)(null),[r,s]=(0,i.useState)({isInitialized:!1,isProcessing:!1,error:null,conversationHistory:[],lastResponse:null}),n=(0,i.useCallback)(e=>{s(t=>({...t,...e}))},[]),a=(0,i.useCallback)(r=>{try{if(!r||""===r.trim())throw Error("OpenAI API key is required");t.current=new o(r),n({isInitialized:!0,error:null,conversationHistory:[]}),console.log("OpenAI client initialized successfully")}catch(r){var s;let t=r instanceof Error?r.message:"Failed to initialize OpenAI client";n({error:t,isInitialized:!1}),null==(s=e.onError)||s.call(e,t)}},[e,n]),l=(0,i.useCallback)(async function(s){var i,a,o,l;let c=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(!t.current||!r.isInitialized){let t="OpenAI client not initialized";return n({error:t}),null==(i=e.onError)||i.call(e,t),null}if(!s||""===s.trim()){let t="Message cannot be empty";return n({error:t}),null==(a=e.onError)||a.call(e,t),null}try{n({isProcessing:!0,error:null});let r=await t.current.sendMessage(s,c),i=t.current.getConversationHistory();return n({isProcessing:!1,lastResponse:r,conversationHistory:i,error:null}),e.autoTrimHistory&&e.maxHistoryLength&&t.current.trimConversationHistory(e.maxHistoryLength),null==(o=e.onResponse)||o.call(e,r),r}catch(r){let t=r instanceof Error?r.message:"Failed to send message";return n({error:t,isProcessing:!1}),null==(l=e.onError)||l.call(e,t),null}},[r.isInitialized,e,n]),c=(0,i.useCallback)(()=>{t.current&&(t.current.clearConversationHistory(),n({conversationHistory:[],lastResponse:null,error:null}))},[n]),u=(0,i.useCallback)(e=>{t.current&&t.current.setSystemPrompt(e)},[]);return[r,{initialize:a,sendMessage:l,clearHistory:c,setSystemPrompt:u,getSystemPrompt:(0,i.useCallback)(()=>{var e;return(null==(e=t.current)?void 0:e.getSystemPrompt())||""},[])}]}({onResponse:t=>{var r;console.log('LLM completed: "'.concat(t.message,'" (').concat(t.processingTime.toFixed(2),"ms)")),l({lastResponse:t.message,currentStage:"speaking"}),null==(r=e.onResponse)||r.call(e,t.message),g.synthesizeText(t.message)},onError:t=>{var r;l({error:t,currentStage:"idle"}),null==(r=e.onError)||r.call(e,t)},autoTrimHistory:!0,maxHistoryLength:10}),[,g]=function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=(0,i.useRef)(null),r=(0,i.useRef)(null),[s,n]=(0,i.useState)({isInitialized:!1,isSynthesizing:!1,error:null,lastSynthesis:null,initializationProgress:0}),a=(0,i.useCallback)(e=>{n(t=>({...t,...e}))},[]),o=(0,i.useCallback)(async(e,t)=>{if(!r.current)throw Error("Audio context not available");try{"suspended"===r.current.state&&await r.current.resume();let s=r.current.createBuffer(1,e.length,t);s.getChannelData(0).set(e);let i=r.current.createBufferSource();return i.buffer=s,i.connect(r.current.destination),new Promise(e=>{i.onended=()=>e(),i.start()})}catch(e){throw console.error("Audio playback error:",e),e}},[]),l=(0,i.useCallback)(async()=>{try{a({error:null,initializationProgress:0}),r.current=new AudioContext,t.current=new Worker("/tts-worker.js"),t.current.onmessage=async t=>{var r,s;let{type:i,...n}=t.data;switch(i){case"initialized":n.success?(a({isInitialized:!0,initializationProgress:100,error:null}),console.log("TTS initialized successfully")):a({error:n.error||"Failed to initialize TTS",isInitialized:!1,initializationProgress:0});break;case"synthesis":let l={audioData:new Float32Array(n.result.audioData),sampleRate:n.result.sampleRate,duration:n.result.duration,processingTime:n.result.processingTime,text:n.result.text};a({isSynthesizing:!1,lastSynthesis:l,error:null}),null==(r=e.onSynthesis)||r.call(e,l),e.autoPlay&&await o(l.audioData,l.sampleRate);break;case"error":let c=n.error||"Unknown TTS error";a({error:c,isSynthesizing:!1}),null==(s=e.onError)||s.call(e,c);break;case"progress":a({initializationProgress:n.progress||0});break;default:console.warn("Unknown message type from TTS worker:",i)}},t.current.onerror=t=>{var r;let s="TTS Worker error: ".concat(t.message);a({error:s,isInitialized:!1,initializationProgress:0}),null==(r=e.onError)||r.call(e,s)},a({initializationProgress:10}),t.current.postMessage({type:"initialize"})}catch(r){var s;let t=r instanceof Error?r.message:"Failed to create TTS worker";a({error:t,isInitialized:!1,initializationProgress:0}),null==(s=e.onError)||s.call(e,t)}},[e,a,o]),c=(0,i.useCallback)(async function(e){let r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(!t.current||!s.isInitialized)throw Error("TTS not initialized");if(!e||""===e.trim())throw Error("Text cannot be empty");a({isSynthesizing:!0,error:null}),t.current.postMessage({type:"synthesize",data:{text:e.trim(),options:r}})},[s.isInitialized,a]),u=(0,i.useCallback)(()=>{t.current&&(t.current.postMessage({type:"reset"}),a({isSynthesizing:!1,lastSynthesis:null,error:null}))},[a]);return(0,i.useEffect)(()=>{e.autoInitialize&&l()},[e.autoInitialize,l]),(0,i.useEffect)(()=>()=>{t.current&&(t.current.terminate(),t.current=null),r.current&&(r.current.close(),r.current=null)},[]),[s,{initialize:l,synthesizeText:c,playAudio:o,reset:u}]}({onSynthesis:t=>{var r,i;let n=performance.now();if(s.current.ttsLatency=t.processingTime,s.current.totalLatency=n-a.current,console.log("TTS completed (".concat(t.processingTime.toFixed(2),"ms)")),console.log("Total pipeline latency: ".concat(null==(r=s.current.totalLatency)?void 0:r.toFixed(2),"ms")),s.current.totalLatency){let t={sttLatency:s.current.sttLatency||0,llmLatency:s.current.llmLatency||0,ttsLatency:s.current.ttsLatency||0,totalLatency:s.current.totalLatency,audioLength:s.current.audioLength||0};c.recordMetrics({audioLength:t.audioLength,sttLatency:t.sttLatency,llmLatency:t.llmLatency,ttsLatency:t.ttsLatency,totalLatency:t.totalLatency}),null==(i=e.onMetrics)||i.call(e,t)}setTimeout(()=>{l({currentStage:"idle"})},500)},onError:t=>{var r;l({error:t,currentStage:"idle"}),null==(r=e.onError)||r.call(e,t)},autoPlay:!0}),m=(0,i.useCallback)(async t=>{try{l({error:null}),await Promise.all([u.initialize(),d.initialize(),g.initialize()]),h.initialize(t),l({isInitialized:!0}),e.autoStartListening&&(u.startRecording(),l({isListening:!0,currentStage:"idle",error:null}))}catch(s){var r;let t=s instanceof Error?s.message:"Initialization failed";l({error:t,isInitialized:!1}),null==(r=e.onError)||r.call(e,t)}},[u,d,g,h,e,l]),p=(0,i.useCallback)(()=>{if(!t.isInitialized)return void l({error:"Voice assistant not initialized"});u.startRecording(),l({isListening:!0,currentStage:"idle",error:null})},[t.isInitialized,u,l]),y=(0,i.useCallback)(()=>{u.stopRecording(),l({isListening:!1,currentStage:"idle"})},[u,l]),v=(0,i.useCallback)(async e=>{if(!t.isInitialized)throw Error("Voice assistant not initialized");l({currentStage:"thinking"});try{let t=await h.sendMessage(e);t&&(l({lastTranscription:e,lastResponse:t.message,currentStage:"speaking"}),await g.synthesizeText(t.message))}catch(e){throw l({error:e instanceof Error?e.message:"Failed to send message",currentStage:"idle"}),e}},[t.isInitialized,h,g,l]),f=(0,i.useCallback)(()=>{u.stopRecording(),d.reset(),h.clearHistory(),g.reset(),r({isInitialized:!1,isListening:!1,isProcessing:!1,currentStage:"idle",error:null,volume:0,lastTranscription:null,lastResponse:null})},[u,d,h,g]);return(0,i.useEffect)(()=>{let e=["transcribing","thinking","speaking"].includes(t.currentStage);t.isProcessing!==e&&l({isProcessing:e})},[t.currentStage,t.isProcessing,l]),[t,{initialize:m,startListening:p,stopListening:y,sendTextMessage:v,reset:f}]}({onStageChange:e=>{console.log("Stage changed to:",e)},onTranscription:e=>{console.log("Transcription:",e)},onResponse:e=>{console.log("Response:",e)},onMetrics:e=>{u(e),console.log("Pipeline metrics:",e)},onError:e=>{console.error("Voice assistant error:",e)}});(0,i.useEffect)(()=>{(async()=>{await d.register(),g(await d.getCacheStatus())})();let e=setInterval(async()=>{g(await d.getCacheStatus())},5e3);return()=>clearInterval(e)},[]);let y=async()=>{if(!e.trim())return void alert("Please enter your OpenAI API key");try{await p.initialize(e),a(!1)}catch(e){console.error("Initialization failed:",e)}},v=e=>{switch(e){case"listening":return"text-green-400";case"transcribing":return"text-blue-400";case"thinking":return"text-yellow-400";case"speaking":return"text-purple-400";default:return"text-gray-400"}};return r?(0,s.jsx)("div",{className:"min-h-screen flex items-center justify-center p-4",children:(0,s.jsxs)("div",{className:"bg-gray-800 rounded-lg p-8 max-w-md w-full",children:[(0,s.jsx)("h1",{className:"text-2xl font-bold mb-6 text-center",children:"Voice Assistant"}),(0,s.jsxs)("div",{className:"mb-6",children:[(0,s.jsx)("label",{htmlFor:"apiKey",className:"block text-sm font-medium mb-2",children:"OpenAI API Key"}),(0,s.jsx)("input",{id:"apiKey",type:"password",value:e,onChange:e=>t(e.target.value),placeholder:"sk-...",className:"w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"})]}),h&&(0,s.jsxs)("div",{className:"mb-6 p-3 bg-gray-700 rounded-md",children:[(0,s.jsx)("h3",{className:"text-sm font-medium mb-2",children:"Cache Status"}),(0,s.jsxs)("div",{className:"text-xs text-gray-300",children:[(0,s.jsxs)("div",{children:["Models: ",h.modelsCached,"/",h.totalModels]}),(0,s.jsxs)("div",{children:["Ready: ",h.isReady?"✅":"⏳"]})]})]}),(0,s.jsx)("button",{onClick:y,disabled:!e.trim(),className:"w-full bg-blue-600 hover:bg-blue-700 disabled:bg-gray-600 disabled:cursor-not-allowed px-4 py-2 rounded-md font-medium transition-colors",children:"Initialize Voice Assistant"})]})}):(0,s.jsx)("div",{className:"min-h-screen flex flex-col items-center justify-center p-4",children:(0,s.jsxs)("div",{className:"bg-gray-800 rounded-lg p-8 max-w-2xl w-full",children:[(0,s.jsx)("h1",{className:"text-3xl font-bold mb-8 text-center",children:"Voice Assistant"}),(0,s.jsxs)("div",{className:"text-center mb-8",children:[(0,s.jsx)("div",{className:"text-6xl mb-4 ".concat(v(m.currentStage)),children:(e=>{switch(e){case"listening":return"\uD83C\uDFA4";case"transcribing":return"\uD83D\uDCDD";case"thinking":return"\uD83E\uDD14";case"speaking":return"\uD83D\uDD0A";default:return"⭕"}})(m.currentStage)}),(0,s.jsx)("div",{className:"text-xl font-medium ".concat(v(m.currentStage)),children:m.currentStage.charAt(0).toUpperCase()+m.currentStage.slice(1)}),m.isListening&&(0,s.jsxs)("div",{className:"mt-2",children:[(0,s.jsx)("div",{className:"w-full bg-gray-700 rounded-full h-2",children:(0,s.jsx)("div",{className:"bg-green-400 h-2 rounded-full transition-all duration-100",style:{width:"".concat(Math.min(100*m.volume,100),"%")}})}),(0,s.jsxs)("div",{className:"text-xs text-gray-400 mt-1",children:["Volume: ",(100*m.volume).toFixed(0),"%"]})]})]}),(0,s.jsx)("div",{className:"flex justify-center gap-4 mb-8",children:m.isListening?(0,s.jsx)("button",{onClick:()=>{p.stopListening()},className:"bg-red-600 hover:bg-red-700 px-6 py-3 rounded-full font-medium transition-colors flex items-center gap-2",children:"⏹️ Stop Listening"}):(0,s.jsx)("button",{onClick:()=>{p.startListening()},disabled:!m.isInitialized||m.isProcessing,className:"bg-green-600 hover:bg-green-700 disabled:bg-gray-600 disabled:cursor-not-allowed px-6 py-3 rounded-full font-medium transition-colors flex items-center gap-2",children:"\uD83C\uDFA4 Start Listening"})}),(0,s.jsxs)("div",{className:"space-y-4 mb-6",children:[m.lastTranscription&&(0,s.jsxs)("div",{className:"bg-blue-900/30 p-4 rounded-lg",children:[(0,s.jsx)("div",{className:"text-sm text-blue-300 mb-1",children:"You said:"}),(0,s.jsx)("div",{className:"text-white",children:m.lastTranscription})]}),m.lastResponse&&(0,s.jsxs)("div",{className:"bg-purple-900/30 p-4 rounded-lg",children:[(0,s.jsx)("div",{className:"text-sm text-purple-300 mb-1",children:"Assistant:"}),(0,s.jsx)("div",{className:"text-white",children:m.lastResponse})]})]}),l&&(0,s.jsxs)("div",{className:"bg-gray-700 p-4 rounded-lg",children:[(0,s.jsx)("h3",{className:"text-sm font-medium mb-3",children:"Performance Metrics"}),(0,s.jsxs)("div",{className:"grid grid-cols-2 gap-4 text-xs",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("div",{className:"text-gray-400",children:"STT Latency"}),(0,s.jsxs)("div",{className:"font-mono",children:[l.sttLatency.toFixed(0),"ms"]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("div",{className:"text-gray-400",children:"LLM Latency"}),(0,s.jsxs)("div",{className:"font-mono",children:[l.llmLatency.toFixed(0),"ms"]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("div",{className:"text-gray-400",children:"TTS Latency"}),(0,s.jsxs)("div",{className:"font-mono",children:[l.ttsLatency.toFixed(0),"ms"]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("div",{className:"text-gray-400",children:"Total Latency"}),(0,s.jsxs)("div",{className:"font-mono ".concat(l.totalLatency<1200?"text-green-400":"text-yellow-400"),children:[l.totalLatency.toFixed(0),"ms"]})]})]}),(0,s.jsxs)("div",{className:"mt-2 text-xs text-gray-400",children:["Audio Length: ",l.audioLength.toFixed(1),"s"]})]}),m.error&&(0,s.jsx)("div",{className:"bg-red-900/30 border border-red-600 p-4 rounded-lg mt-4",children:(0,s.jsxs)("div",{className:"text-red-300 text-sm",children:["Error: ",m.error]})}),(0,s.jsx)("div",{className:"text-center mt-6",children:(0,s.jsx)("button",{onClick:()=>{p.reset(),a(!0),u(null)},className:"text-gray-400 hover:text-white text-sm underline",children:"Reset & Change API Key"})})]})})};function g(){return(0,s.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-gray-900 to-black text-white",children:(0,s.jsx)(h,{})})}}},e=>{e.O(0,[617,441,964,358],()=>e(e.s=2963)),_N_E=e.O()}]);