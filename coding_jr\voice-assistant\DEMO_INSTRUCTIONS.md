# Voice Assistant Demo Instructions

## Prerequisites

1. **OpenAI API Key**: You'll need a valid OpenAI API key
2. **Modern Browser**: Chrome, Edge, or Firefox (latest versions)
3. **Microphone**: Working microphone with permissions granted
4. **Network**: Internet connection for initial setup and OpenAI API calls

## Demo Setup

### 1. Start the Application

```bash
cd voice-assistant
npm run dev
```

The application will be available at `http://localhost:3000`

### 2. Initial Setup

1. **Open the application** in your browser
2. **Enter your OpenAI API key** when prompted
3. **Grant microphone permissions** when requested
4. **Wait for initialization** - models will be downloaded and cached (this may take 1-2 minutes on first load)

### 3. Cache Status Verification

- Check the "Cache Status" indicator
- Wait until it shows "Ready: ✅" 
- This ensures offline capabilities are properly set up

## Demo Flow

### Phase 1: Basic Voice Interaction

1. **Click "Initialize Voice Assistant"**
   - Wait for all components to initialize
   - You should see "Initialized successfully" messages in console

2. **Click "Start Listening"**
   - The microphone icon should turn green
   - Volume meter should show audio input levels

3. **Say a simple greeting**
   - Example: "Hello, how are you today?"
   - Watch the pipeline stages: Listening → Transcribing → Thinking → Speaking

4. **Observe the response**
   - Text should appear in the conversation area
   - Audio response should play automatically
   - Performance metrics should be displayed

### Phase 2: Performance Testing

1. **Test various query types:**
   - Short questions: "What time is it?"
   - Complex queries: "Explain quantum computing in simple terms"
   - Conversational: "Tell me a joke"

2. **Monitor performance metrics:**
   - STT Latency (target: <500ms)
   - LLM Latency (target: <800ms)  
   - TTS Latency (target: <400ms)
   - Total Latency (target: <1200ms)

3. **Check for bottlenecks:**
   - Green total latency = under 1.2s target ✅
   - Yellow/Red = over target, check individual components

### Phase 3: Offline Capabilities Demo

1. **Ensure models are cached** (Cache Status shows "Ready: ✅")

2. **Disconnect internet** (disable WiFi or unplug ethernet)

3. **Test voice interaction:**
   - STT should work (local Whisper)
   - LLM will fail gracefully (requires internet)
   - TTS should work (local SpeechT5)

4. **Reconnect internet** and verify full functionality returns

## Expected Demo Results

### Successful Demo Indicators

✅ **Initialization**: All components load without errors  
✅ **Voice Recognition**: Speech is accurately transcribed  
✅ **AI Responses**: Relevant and coherent responses from OpenAI  
✅ **Audio Synthesis**: Clear, natural-sounding speech output  
✅ **Performance**: Total latency under 1.2 seconds  
✅ **Offline Mode**: STT and TTS work without internet  
✅ **PWA Features**: App can be installed and works offline  

### Performance Benchmarks

| Component | Target | Good | Acceptable | Poor |
|-----------|--------|------|------------|------|
| STT | <400ms | <500ms | <800ms | >800ms |
| LLM | <600ms | <800ms | <1200ms | >1200ms |
| TTS | <300ms | <400ms | <600ms | >600ms |
| **Total** | **<1200ms** | **<1500ms** | **<2000ms** | **>2000ms** |

## Troubleshooting Common Issues

### Issue: Models Not Loading
**Symptoms**: Initialization fails, cache status stuck  
**Solutions**: 
- Check network connection
- Clear browser cache
- Try incognito/private mode

### Issue: Microphone Not Working
**Symptoms**: No volume meter activity, STT fails  
**Solutions**:
- Check browser permissions
- Try different browser
- Ensure HTTPS in production

### Issue: High Latency
**Symptoms**: Total response time >2 seconds  
**Solutions**:
- Check network speed
- Monitor individual component latencies
- Consider using smaller models

### Issue: OpenAI API Errors
**Symptoms**: LLM stage fails, error messages  
**Solutions**:
- Verify API key validity
- Check API quota/billing
- Monitor rate limits

## Demo Script Example

```
Presenter: "Let me demonstrate our offline-capable voice assistant."

1. "First, I'll initialize the system with my OpenAI API key..."
   [Enter API key, wait for initialization]

2. "Notice the cache status - models are being downloaded for offline use..."
   [Point to cache status indicator]

3. "Now I'll start a voice conversation..."
   [Click Start Listening]

4. "Hello, can you explain what a Progressive Web App is?"
   [Speak clearly into microphone]

5. "Watch the pipeline stages and performance metrics..."
   [Point to stage indicators and latency numbers]

6. "The response is synthesized locally and played back..."
   [Listen to audio response]

7. "Now let me demonstrate offline capabilities..."
   [Disconnect internet, try voice interaction]

8. "Notice that speech recognition and synthesis still work offline!"
   [Show STT and TTS working without internet]
```

## Video Recording Tips

1. **Screen Recording**: Capture the full browser window
2. **Audio**: Ensure both input speech and output audio are recorded
3. **Metrics**: Make sure performance metrics are visible
4. **Offline Demo**: Clearly show network disconnection
5. **Duration**: Keep demo under 3-5 minutes for optimal impact

## Success Criteria

The demo is successful if:
- ✅ Complete pipeline works end-to-end
- ✅ Performance targets are met (most of the time)
- ✅ Offline capabilities are demonstrated
- ✅ No critical errors or crashes
- ✅ Audio quality is clear and natural
- ✅ User interface is responsive and intuitive

---

*Demo prepared for: Coding Jr Internship Application*  
*Date: January 31, 2025*  
*Contact: [Your Name] - [Your Email]*
