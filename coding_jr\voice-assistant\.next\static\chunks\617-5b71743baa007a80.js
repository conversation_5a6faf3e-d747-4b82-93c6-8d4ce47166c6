(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[617],{4134:(e,t,r)=>{"use strict";var s=r(7719),n=r(7610),i="function"==typeof Symbol&&"function"==typeof Symbol.for?Symbol.for("nodejs.util.inspect.custom"):null;function a(e){if(e>0x7fffffff)throw RangeError('The value "'+e+'" is invalid for option "size"');var t=new Uint8Array(e);return Object.setPrototypeOf(t,o.prototype),t}function o(e,t,r){if("number"==typeof e){if("string"==typeof t)throw TypeError('The "string" argument must be of type string. Received type number');return u(e)}return l(e,t,r)}function l(e,t,r){if("string"==typeof e){var s=e,n=t;if(("string"!=typeof n||""===n)&&(n="utf8"),!o.isEncoding(n))throw TypeError("Unknown encoding: "+n);var i=0|p(s,n),l=a(i),c=l.write(s,n);return c!==i&&(l=l.slice(0,c)),l}if(ArrayBuffer.isView(e)){var u=e;if(R(u,Uint8Array)){var m=new Uint8Array(u);return f(m.buffer,m.byteOffset,m.byteLength)}return h(u)}if(null==e)throw TypeError("The first argument must be one of type string, Buffer, ArrayBuffer, Array, or Array-like Object. Received type "+typeof e);if(R(e,ArrayBuffer)||e&&R(e.buffer,ArrayBuffer)||"undefined"!=typeof SharedArrayBuffer&&(R(e,SharedArrayBuffer)||e&&R(e.buffer,SharedArrayBuffer)))return f(e,t,r);if("number"==typeof e)throw TypeError('The "value" argument must not be of type number. Received type number');var g=e.valueOf&&e.valueOf();if(null!=g&&g!==e)return o.from(g,t,r);var y=function(e){if(o.isBuffer(e)){var t=0|d(e.length),r=a(t);return 0===r.length||e.copy(r,0,0,t),r}return void 0!==e.length?"number"!=typeof e.length||function(e){return e!=e}(e.length)?a(0):h(e):"Buffer"===e.type&&Array.isArray(e.data)?h(e.data):void 0}(e);if(y)return y;if("undefined"!=typeof Symbol&&null!=Symbol.toPrimitive&&"function"==typeof e[Symbol.toPrimitive])return o.from(e[Symbol.toPrimitive]("string"),t,r);throw TypeError("The first argument must be one of type string, Buffer, ArrayBuffer, Array, or Array-like Object. Received type "+typeof e)}function c(e){if("number"!=typeof e)throw TypeError('"size" argument must be of type number');if(e<0)throw RangeError('The value "'+e+'" is invalid for option "size"')}function u(e){return c(e),a(e<0?0:0|d(e))}function h(e){for(var t=e.length<0?0:0|d(e.length),r=a(t),s=0;s<t;s+=1)r[s]=255&e[s];return r}function f(e,t,r){var s;if(t<0||e.byteLength<t)throw RangeError('"offset" is outside of buffer bounds');if(e.byteLength<t+(r||0))throw RangeError('"length" is outside of buffer bounds');return Object.setPrototypeOf(s=void 0===t&&void 0===r?new Uint8Array(e):void 0===r?new Uint8Array(e,t):new Uint8Array(e,t,r),o.prototype),s}function d(e){if(e>=0x7fffffff)throw RangeError("Attempt to allocate Buffer larger than maximum size: 0x7fffffff bytes");return 0|e}function p(e,t){if(o.isBuffer(e))return e.length;if(ArrayBuffer.isView(e)||R(e,ArrayBuffer))return e.byteLength;if("string"!=typeof e)throw TypeError('The "string" argument must be one of type string, Buffer, or ArrayBuffer. Received type '+typeof e);var r=e.length,s=arguments.length>2&&!0===arguments[2];if(!s&&0===r)return 0;for(var n=!1;;)switch(t){case"ascii":case"latin1":case"binary":return r;case"utf8":case"utf-8":return I(e).length;case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return 2*r;case"hex":return r>>>1;case"base64":return O(e).length;default:if(n)return s?-1:I(e).length;t=(""+t).toLowerCase(),n=!0}}function m(e,t,r){var n,i,a,o=!1;if((void 0===t||t<0)&&(t=0),t>this.length||((void 0===r||r>this.length)&&(r=this.length),r<=0||(r>>>=0)<=(t>>>=0)))return"";for(e||(e="utf8");;)switch(e){case"hex":return function(e,t,r){var s=e.length;(!t||t<0)&&(t=0),(!r||r<0||r>s)&&(r=s);for(var n="",i=t;i<r;++i)n+=k[e[i]];return n}(this,t,r);case"utf8":case"utf-8":return b(this,t,r);case"ascii":return function(e,t,r){var s="";r=Math.min(e.length,r);for(var n=t;n<r;++n)s+=String.fromCharCode(127&e[n]);return s}(this,t,r);case"latin1":case"binary":return function(e,t,r){var s="";r=Math.min(e.length,r);for(var n=t;n<r;++n)s+=String.fromCharCode(e[n]);return s}(this,t,r);case"base64":return n=this,i=t,a=r,0===i&&a===n.length?s.fromByteArray(n):s.fromByteArray(n.slice(i,a));case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return function(e,t,r){for(var s=e.slice(t,r),n="",i=0;i<s.length-1;i+=2)n+=String.fromCharCode(s[i]+256*s[i+1]);return n}(this,t,r);default:if(o)throw TypeError("Unknown encoding: "+e);e=(e+"").toLowerCase(),o=!0}}function g(e,t,r){var s=e[t];e[t]=e[r],e[r]=s}function y(e,t,r,s,n){var i;if(0===e.length)return -1;if("string"==typeof r?(s=r,r=0):r>0x7fffffff?r=0x7fffffff:r<-0x80000000&&(r=-0x80000000),(i=r*=1)!=i&&(r=n?0:e.length-1),r<0&&(r=e.length+r),r>=e.length)if(n)return -1;else r=e.length-1;else if(r<0)if(!n)return -1;else r=0;if("string"==typeof t&&(t=o.from(t,s)),o.isBuffer(t))return 0===t.length?-1:w(e,t,r,s,n);if("number"==typeof t){if(t&=255,"function"==typeof Uint8Array.prototype.indexOf)if(n)return Uint8Array.prototype.indexOf.call(e,t,r);else return Uint8Array.prototype.lastIndexOf.call(e,t,r);return w(e,[t],r,s,n)}throw TypeError("val must be string, number or Buffer")}function w(e,t,r,s,n){var i,a=1,o=e.length,l=t.length;if(void 0!==s&&("ucs2"===(s=String(s).toLowerCase())||"ucs-2"===s||"utf16le"===s||"utf-16le"===s)){if(e.length<2||t.length<2)return -1;a=2,o/=2,l/=2,r/=2}function c(e,t){return 1===a?e[t]:e.readUInt16BE(t*a)}if(n){var u=-1;for(i=r;i<o;i++)if(c(e,i)===c(t,-1===u?0:i-u)){if(-1===u&&(u=i),i-u+1===l)return u*a}else -1!==u&&(i-=i-u),u=-1}else for(r+l>o&&(r=o-l),i=r;i>=0;i--){for(var h=!0,f=0;f<l;f++)if(c(e,i+f)!==c(t,f)){h=!1;break}if(h)return i}return -1}function b(e,t,r){r=Math.min(e.length,r);for(var s=[],n=t;n<r;){var i,a,o,l,c=e[n],u=null,h=c>239?4:c>223?3:c>191?2:1;if(n+h<=r)switch(h){case 1:c<128&&(u=c);break;case 2:(192&(i=e[n+1]))==128&&(l=(31&c)<<6|63&i)>127&&(u=l);break;case 3:i=e[n+1],a=e[n+2],(192&i)==128&&(192&a)==128&&(l=(15&c)<<12|(63&i)<<6|63&a)>2047&&(l<55296||l>57343)&&(u=l);break;case 4:i=e[n+1],a=e[n+2],o=e[n+3],(192&i)==128&&(192&a)==128&&(192&o)==128&&(l=(15&c)<<18|(63&i)<<12|(63&a)<<6|63&o)>65535&&l<1114112&&(u=l)}null===u?(u=65533,h=1):u>65535&&(u-=65536,s.push(u>>>10&1023|55296),u=56320|1023&u),s.push(u),n+=h}var f=s,d=f.length;if(d<=4096)return String.fromCharCode.apply(String,f);for(var p="",m=0;m<d;)p+=String.fromCharCode.apply(String,f.slice(m,m+=4096));return p}function _(e,t,r){if(e%1!=0||e<0)throw RangeError("offset is not uint");if(e+t>r)throw RangeError("Trying to access beyond buffer length")}function v(e,t,r,s,n,i){if(!o.isBuffer(e))throw TypeError('"buffer" argument must be a Buffer instance');if(t>n||t<i)throw RangeError('"value" argument is out of bounds');if(r+s>e.length)throw RangeError("Index out of range")}function x(e,t,r,s,n,i){if(r+s>e.length||r<0)throw RangeError("Index out of range")}function A(e,t,r,s,i){return t*=1,r>>>=0,i||x(e,t,r,4,34028234663852886e22,-34028234663852886e22),n.write(e,t,r,s,23,4),r+4}function S(e,t,r,s,i){return t*=1,r>>>=0,i||x(e,t,r,8,17976931348623157e292,-17976931348623157e292),n.write(e,t,r,s,52,8),r+8}t.hp=o,t.IS=50,o.TYPED_ARRAY_SUPPORT=function(){try{var e=new Uint8Array(1),t={foo:function(){return 42}};return Object.setPrototypeOf(t,Uint8Array.prototype),Object.setPrototypeOf(e,t),42===e.foo()}catch(e){return!1}}(),o.TYPED_ARRAY_SUPPORT||"undefined"==typeof console||"function"!=typeof console.error||console.error("This browser lacks typed array (Uint8Array) support which is required by `buffer` v5.x. Use `buffer` v4.x if you require old browser support."),Object.defineProperty(o.prototype,"parent",{enumerable:!0,get:function(){if(o.isBuffer(this))return this.buffer}}),Object.defineProperty(o.prototype,"offset",{enumerable:!0,get:function(){if(o.isBuffer(this))return this.byteOffset}}),o.poolSize=8192,o.from=function(e,t,r){return l(e,t,r)},Object.setPrototypeOf(o.prototype,Uint8Array.prototype),Object.setPrototypeOf(o,Uint8Array),o.alloc=function(e,t,r){return(c(e),e<=0)?a(e):void 0!==t?"string"==typeof r?a(e).fill(t,r):a(e).fill(t):a(e)},o.allocUnsafe=function(e){return u(e)},o.allocUnsafeSlow=function(e){return u(e)},o.isBuffer=function(e){return null!=e&&!0===e._isBuffer&&e!==o.prototype},o.compare=function(e,t){if(R(e,Uint8Array)&&(e=o.from(e,e.offset,e.byteLength)),R(t,Uint8Array)&&(t=o.from(t,t.offset,t.byteLength)),!o.isBuffer(e)||!o.isBuffer(t))throw TypeError('The "buf1", "buf2" arguments must be one of type Buffer or Uint8Array');if(e===t)return 0;for(var r=e.length,s=t.length,n=0,i=Math.min(r,s);n<i;++n)if(e[n]!==t[n]){r=e[n],s=t[n];break}return r<s?-1:+(s<r)},o.isEncoding=function(e){switch(String(e).toLowerCase()){case"hex":case"utf8":case"utf-8":case"ascii":case"latin1":case"binary":case"base64":case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return!0;default:return!1}},o.concat=function(e,t){if(!Array.isArray(e))throw TypeError('"list" argument must be an Array of Buffers');if(0===e.length)return o.alloc(0);if(void 0===t)for(r=0,t=0;r<e.length;++r)t+=e[r].length;var r,s=o.allocUnsafe(t),n=0;for(r=0;r<e.length;++r){var i=e[r];if(R(i,Uint8Array))n+i.length>s.length?o.from(i).copy(s,n):Uint8Array.prototype.set.call(s,i,n);else if(o.isBuffer(i))i.copy(s,n);else throw TypeError('"list" argument must be an Array of Buffers');n+=i.length}return s},o.byteLength=p,o.prototype._isBuffer=!0,o.prototype.swap16=function(){var e=this.length;if(e%2!=0)throw RangeError("Buffer size must be a multiple of 16-bits");for(var t=0;t<e;t+=2)g(this,t,t+1);return this},o.prototype.swap32=function(){var e=this.length;if(e%4!=0)throw RangeError("Buffer size must be a multiple of 32-bits");for(var t=0;t<e;t+=4)g(this,t,t+3),g(this,t+1,t+2);return this},o.prototype.swap64=function(){var e=this.length;if(e%8!=0)throw RangeError("Buffer size must be a multiple of 64-bits");for(var t=0;t<e;t+=8)g(this,t,t+7),g(this,t+1,t+6),g(this,t+2,t+5),g(this,t+3,t+4);return this},o.prototype.toString=function(){var e=this.length;return 0===e?"":0==arguments.length?b(this,0,e):m.apply(this,arguments)},o.prototype.toLocaleString=o.prototype.toString,o.prototype.equals=function(e){if(!o.isBuffer(e))throw TypeError("Argument must be a Buffer");return this===e||0===o.compare(this,e)},o.prototype.inspect=function(){var e="",r=t.IS;return e=this.toString("hex",0,r).replace(/(.{2})/g,"$1 ").trim(),this.length>r&&(e+=" ... "),"<Buffer "+e+">"},i&&(o.prototype[i]=o.prototype.inspect),o.prototype.compare=function(e,t,r,s,n){if(R(e,Uint8Array)&&(e=o.from(e,e.offset,e.byteLength)),!o.isBuffer(e))throw TypeError('The "target" argument must be one of type Buffer or Uint8Array. Received type '+typeof e);if(void 0===t&&(t=0),void 0===r&&(r=e?e.length:0),void 0===s&&(s=0),void 0===n&&(n=this.length),t<0||r>e.length||s<0||n>this.length)throw RangeError("out of range index");if(s>=n&&t>=r)return 0;if(s>=n)return -1;if(t>=r)return 1;if(t>>>=0,r>>>=0,s>>>=0,n>>>=0,this===e)return 0;for(var i=n-s,a=r-t,l=Math.min(i,a),c=this.slice(s,n),u=e.slice(t,r),h=0;h<l;++h)if(c[h]!==u[h]){i=c[h],a=u[h];break}return i<a?-1:+(a<i)},o.prototype.includes=function(e,t,r){return -1!==this.indexOf(e,t,r)},o.prototype.indexOf=function(e,t,r){return y(this,e,t,r,!0)},o.prototype.lastIndexOf=function(e,t,r){return y(this,e,t,r,!1)},o.prototype.write=function(e,t,r,s){if(void 0===t)s="utf8",r=this.length,t=0;else if(void 0===r&&"string"==typeof t)s=t,r=this.length,t=0;else if(isFinite(t))t>>>=0,isFinite(r)?(r>>>=0,void 0===s&&(s="utf8")):(s=r,r=void 0);else throw Error("Buffer.write(string, encoding, offset[, length]) is no longer supported");var n,i,a,o,l,c,u,h,f=this.length-t;if((void 0===r||r>f)&&(r=f),e.length>0&&(r<0||t<0)||t>this.length)throw RangeError("Attempt to write outside buffer bounds");s||(s="utf8");for(var d=!1;;)switch(s){case"hex":return function(e,t,r,s){r=Number(r)||0;var n=e.length-r;s?(s=Number(s))>n&&(s=n):s=n;var i=t.length;s>i/2&&(s=i/2);for(var a=0;a<s;++a){var o,l=parseInt(t.substr(2*a,2),16);if((o=l)!=o)break;e[r+a]=l}return a}(this,e,t,r);case"utf8":case"utf-8":return n=t,i=r,$(I(e,this.length-n),this,n,i);case"ascii":case"latin1":case"binary":return a=t,o=r,$(function(e){for(var t=[],r=0;r<e.length;++r)t.push(255&e.charCodeAt(r));return t}(e),this,a,o);case"base64":return l=t,c=r,$(O(e),this,l,c);case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return u=t,h=r,$(function(e,t){for(var r,s,n=[],i=0;i<e.length&&!((t-=2)<0);++i)s=(r=e.charCodeAt(i))>>8,n.push(r%256),n.push(s);return n}(e,this.length-u),this,u,h);default:if(d)throw TypeError("Unknown encoding: "+s);s=(""+s).toLowerCase(),d=!0}},o.prototype.toJSON=function(){return{type:"Buffer",data:Array.prototype.slice.call(this._arr||this,0)}},o.prototype.slice=function(e,t){var r=this.length;e=~~e,t=void 0===t?r:~~t,e<0?(e+=r)<0&&(e=0):e>r&&(e=r),t<0?(t+=r)<0&&(t=0):t>r&&(t=r),t<e&&(t=e);var s=this.subarray(e,t);return Object.setPrototypeOf(s,o.prototype),s},o.prototype.readUintLE=o.prototype.readUIntLE=function(e,t,r){e>>>=0,t>>>=0,r||_(e,t,this.length);for(var s=this[e],n=1,i=0;++i<t&&(n*=256);)s+=this[e+i]*n;return s},o.prototype.readUintBE=o.prototype.readUIntBE=function(e,t,r){e>>>=0,t>>>=0,r||_(e,t,this.length);for(var s=this[e+--t],n=1;t>0&&(n*=256);)s+=this[e+--t]*n;return s},o.prototype.readUint8=o.prototype.readUInt8=function(e,t){return e>>>=0,t||_(e,1,this.length),this[e]},o.prototype.readUint16LE=o.prototype.readUInt16LE=function(e,t){return e>>>=0,t||_(e,2,this.length),this[e]|this[e+1]<<8},o.prototype.readUint16BE=o.prototype.readUInt16BE=function(e,t){return e>>>=0,t||_(e,2,this.length),this[e]<<8|this[e+1]},o.prototype.readUint32LE=o.prototype.readUInt32LE=function(e,t){return e>>>=0,t||_(e,4,this.length),(this[e]|this[e+1]<<8|this[e+2]<<16)+0x1000000*this[e+3]},o.prototype.readUint32BE=o.prototype.readUInt32BE=function(e,t){return e>>>=0,t||_(e,4,this.length),0x1000000*this[e]+(this[e+1]<<16|this[e+2]<<8|this[e+3])},o.prototype.readIntLE=function(e,t,r){e>>>=0,t>>>=0,r||_(e,t,this.length);for(var s=this[e],n=1,i=0;++i<t&&(n*=256);)s+=this[e+i]*n;return s>=(n*=128)&&(s-=Math.pow(2,8*t)),s},o.prototype.readIntBE=function(e,t,r){e>>>=0,t>>>=0,r||_(e,t,this.length);for(var s=t,n=1,i=this[e+--s];s>0&&(n*=256);)i+=this[e+--s]*n;return i>=(n*=128)&&(i-=Math.pow(2,8*t)),i},o.prototype.readInt8=function(e,t){return(e>>>=0,t||_(e,1,this.length),128&this[e])?-((255-this[e]+1)*1):this[e]},o.prototype.readInt16LE=function(e,t){e>>>=0,t||_(e,2,this.length);var r=this[e]|this[e+1]<<8;return 32768&r?0xffff0000|r:r},o.prototype.readInt16BE=function(e,t){e>>>=0,t||_(e,2,this.length);var r=this[e+1]|this[e]<<8;return 32768&r?0xffff0000|r:r},o.prototype.readInt32LE=function(e,t){return e>>>=0,t||_(e,4,this.length),this[e]|this[e+1]<<8|this[e+2]<<16|this[e+3]<<24},o.prototype.readInt32BE=function(e,t){return e>>>=0,t||_(e,4,this.length),this[e]<<24|this[e+1]<<16|this[e+2]<<8|this[e+3]},o.prototype.readFloatLE=function(e,t){return e>>>=0,t||_(e,4,this.length),n.read(this,e,!0,23,4)},o.prototype.readFloatBE=function(e,t){return e>>>=0,t||_(e,4,this.length),n.read(this,e,!1,23,4)},o.prototype.readDoubleLE=function(e,t){return e>>>=0,t||_(e,8,this.length),n.read(this,e,!0,52,8)},o.prototype.readDoubleBE=function(e,t){return e>>>=0,t||_(e,8,this.length),n.read(this,e,!1,52,8)},o.prototype.writeUintLE=o.prototype.writeUIntLE=function(e,t,r,s){if(e*=1,t>>>=0,r>>>=0,!s){var n=Math.pow(2,8*r)-1;v(this,e,t,r,n,0)}var i=1,a=0;for(this[t]=255&e;++a<r&&(i*=256);)this[t+a]=e/i&255;return t+r},o.prototype.writeUintBE=o.prototype.writeUIntBE=function(e,t,r,s){if(e*=1,t>>>=0,r>>>=0,!s){var n=Math.pow(2,8*r)-1;v(this,e,t,r,n,0)}var i=r-1,a=1;for(this[t+i]=255&e;--i>=0&&(a*=256);)this[t+i]=e/a&255;return t+r},o.prototype.writeUint8=o.prototype.writeUInt8=function(e,t,r){return e*=1,t>>>=0,r||v(this,e,t,1,255,0),this[t]=255&e,t+1},o.prototype.writeUint16LE=o.prototype.writeUInt16LE=function(e,t,r){return e*=1,t>>>=0,r||v(this,e,t,2,65535,0),this[t]=255&e,this[t+1]=e>>>8,t+2},o.prototype.writeUint16BE=o.prototype.writeUInt16BE=function(e,t,r){return e*=1,t>>>=0,r||v(this,e,t,2,65535,0),this[t]=e>>>8,this[t+1]=255&e,t+2},o.prototype.writeUint32LE=o.prototype.writeUInt32LE=function(e,t,r){return e*=1,t>>>=0,r||v(this,e,t,4,0xffffffff,0),this[t+3]=e>>>24,this[t+2]=e>>>16,this[t+1]=e>>>8,this[t]=255&e,t+4},o.prototype.writeUint32BE=o.prototype.writeUInt32BE=function(e,t,r){return e*=1,t>>>=0,r||v(this,e,t,4,0xffffffff,0),this[t]=e>>>24,this[t+1]=e>>>16,this[t+2]=e>>>8,this[t+3]=255&e,t+4},o.prototype.writeIntLE=function(e,t,r,s){if(e*=1,t>>>=0,!s){var n=Math.pow(2,8*r-1);v(this,e,t,r,n-1,-n)}var i=0,a=1,o=0;for(this[t]=255&e;++i<r&&(a*=256);)e<0&&0===o&&0!==this[t+i-1]&&(o=1),this[t+i]=(e/a|0)-o&255;return t+r},o.prototype.writeIntBE=function(e,t,r,s){if(e*=1,t>>>=0,!s){var n=Math.pow(2,8*r-1);v(this,e,t,r,n-1,-n)}var i=r-1,a=1,o=0;for(this[t+i]=255&e;--i>=0&&(a*=256);)e<0&&0===o&&0!==this[t+i+1]&&(o=1),this[t+i]=(e/a|0)-o&255;return t+r},o.prototype.writeInt8=function(e,t,r){return e*=1,t>>>=0,r||v(this,e,t,1,127,-128),e<0&&(e=255+e+1),this[t]=255&e,t+1},o.prototype.writeInt16LE=function(e,t,r){return e*=1,t>>>=0,r||v(this,e,t,2,32767,-32768),this[t]=255&e,this[t+1]=e>>>8,t+2},o.prototype.writeInt16BE=function(e,t,r){return e*=1,t>>>=0,r||v(this,e,t,2,32767,-32768),this[t]=e>>>8,this[t+1]=255&e,t+2},o.prototype.writeInt32LE=function(e,t,r){return e*=1,t>>>=0,r||v(this,e,t,4,0x7fffffff,-0x80000000),this[t]=255&e,this[t+1]=e>>>8,this[t+2]=e>>>16,this[t+3]=e>>>24,t+4},o.prototype.writeInt32BE=function(e,t,r){return e*=1,t>>>=0,r||v(this,e,t,4,0x7fffffff,-0x80000000),e<0&&(e=0xffffffff+e+1),this[t]=e>>>24,this[t+1]=e>>>16,this[t+2]=e>>>8,this[t+3]=255&e,t+4},o.prototype.writeFloatLE=function(e,t,r){return A(this,e,t,!0,r)},o.prototype.writeFloatBE=function(e,t,r){return A(this,e,t,!1,r)},o.prototype.writeDoubleLE=function(e,t,r){return S(this,e,t,!0,r)},o.prototype.writeDoubleBE=function(e,t,r){return S(this,e,t,!1,r)},o.prototype.copy=function(e,t,r,s){if(!o.isBuffer(e))throw TypeError("argument should be a Buffer");if(r||(r=0),s||0===s||(s=this.length),t>=e.length&&(t=e.length),t||(t=0),s>0&&s<r&&(s=r),s===r||0===e.length||0===this.length)return 0;if(t<0)throw RangeError("targetStart out of bounds");if(r<0||r>=this.length)throw RangeError("Index out of range");if(s<0)throw RangeError("sourceEnd out of bounds");s>this.length&&(s=this.length),e.length-t<s-r&&(s=e.length-t+r);var n=s-r;return this===e&&"function"==typeof Uint8Array.prototype.copyWithin?this.copyWithin(t,r,s):Uint8Array.prototype.set.call(e,this.subarray(r,s),t),n},o.prototype.fill=function(e,t,r,s){if("string"==typeof e){if("string"==typeof t?(s=t,t=0,r=this.length):"string"==typeof r&&(s=r,r=this.length),void 0!==s&&"string"!=typeof s)throw TypeError("encoding must be a string");if("string"==typeof s&&!o.isEncoding(s))throw TypeError("Unknown encoding: "+s);if(1===e.length){var n,i=e.charCodeAt(0);("utf8"===s&&i<128||"latin1"===s)&&(e=i)}}else"number"==typeof e?e&=255:"boolean"==typeof e&&(e=Number(e));if(t<0||this.length<t||this.length<r)throw RangeError("Out of range index");if(r<=t)return this;if(t>>>=0,r=void 0===r?this.length:r>>>0,e||(e=0),"number"==typeof e)for(n=t;n<r;++n)this[n]=e;else{var a=o.isBuffer(e)?e:o.from(e,s),l=a.length;if(0===l)throw TypeError('The value "'+e+'" is invalid for argument "value"');for(n=0;n<r-t;++n)this[n+t]=a[n%l]}return this};var E=/[^+/0-9A-Za-z-_]/g;function I(e,t){t=t||1/0;for(var r,s=e.length,n=null,i=[],a=0;a<s;++a){if((r=e.charCodeAt(a))>55295&&r<57344){if(!n){if(r>56319||a+1===s){(t-=3)>-1&&i.push(239,191,189);continue}n=r;continue}if(r<56320){(t-=3)>-1&&i.push(239,191,189),n=r;continue}r=(n-55296<<10|r-56320)+65536}else n&&(t-=3)>-1&&i.push(239,191,189);if(n=null,r<128){if((t-=1)<0)break;i.push(r)}else if(r<2048){if((t-=2)<0)break;i.push(r>>6|192,63&r|128)}else if(r<65536){if((t-=3)<0)break;i.push(r>>12|224,r>>6&63|128,63&r|128)}else if(r<1114112){if((t-=4)<0)break;i.push(r>>18|240,r>>12&63|128,r>>6&63|128,63&r|128)}else throw Error("Invalid code point")}return i}function O(e){return s.toByteArray(function(e){if((e=(e=e.split("=")[0]).trim().replace(E,"")).length<2)return"";for(;e.length%4!=0;)e+="=";return e}(e))}function $(e,t,r,s){for(var n=0;n<s&&!(n+r>=t.length)&&!(n>=e.length);++n)t[n+r]=e[n];return n}function R(e,t){return e instanceof t||null!=e&&null!=e.constructor&&null!=e.constructor.name&&e.constructor.name===t.name}var k=function(){for(var e="0123456789abcdef",t=Array(256),r=0;r<16;++r)for(var s=16*r,n=0;n<16;++n)t[s+n]=e[r]+e[n];return t}()},7610:(e,t)=>{t.read=function(e,t,r,s,n){var i,a,o=8*n-s-1,l=(1<<o)-1,c=l>>1,u=-7,h=r?n-1:0,f=r?-1:1,d=e[t+h];for(h+=f,i=d&(1<<-u)-1,d>>=-u,u+=o;u>0;i=256*i+e[t+h],h+=f,u-=8);for(a=i&(1<<-u)-1,i>>=-u,u+=s;u>0;a=256*a+e[t+h],h+=f,u-=8);if(0===i)i=1-c;else{if(i===l)return a?NaN:1/0*(d?-1:1);a+=Math.pow(2,s),i-=c}return(d?-1:1)*a*Math.pow(2,i-s)},t.write=function(e,t,r,s,n,i){var a,o,l,c=8*i-n-1,u=(1<<c)-1,h=u>>1,f=5960464477539062e-23*(23===n),d=s?0:i-1,p=s?1:-1,m=+(t<0||0===t&&1/t<0);for(isNaN(t=Math.abs(t))||t===1/0?(o=+!!isNaN(t),a=u):(a=Math.floor(Math.log(t)/Math.LN2),t*(l=Math.pow(2,-a))<1&&(a--,l*=2),a+h>=1?t+=f/l:t+=f*Math.pow(2,1-h),t*l>=2&&(a++,l/=2),a+h>=u?(o=0,a=u):a+h>=1?(o=(t*l-1)*Math.pow(2,n),a+=h):(o=t*Math.pow(2,h-1)*Math.pow(2,n),a=0));n>=8;e[r+d]=255&o,d+=p,o/=256,n-=8);for(a=a<<n|o,c+=n;c>0;e[r+d]=255&a,d+=p,a/=256,c-=8);e[r+d-p]|=128*m}},7719:(e,t)=>{"use strict";t.byteLength=function(e){var t=l(e),r=t[0],s=t[1];return(r+s)*3/4-s},t.toByteArray=function(e){var t,r,i=l(e),a=i[0],o=i[1],c=new n((a+o)*3/4-o),u=0,h=o>0?a-4:a;for(r=0;r<h;r+=4)t=s[e.charCodeAt(r)]<<18|s[e.charCodeAt(r+1)]<<12|s[e.charCodeAt(r+2)]<<6|s[e.charCodeAt(r+3)],c[u++]=t>>16&255,c[u++]=t>>8&255,c[u++]=255&t;return 2===o&&(t=s[e.charCodeAt(r)]<<2|s[e.charCodeAt(r+1)]>>4,c[u++]=255&t),1===o&&(t=s[e.charCodeAt(r)]<<10|s[e.charCodeAt(r+1)]<<4|s[e.charCodeAt(r+2)]>>2,c[u++]=t>>8&255,c[u++]=255&t),c},t.fromByteArray=function(e){for(var t,s=e.length,n=s%3,i=[],a=0,o=s-n;a<o;a+=16383)i.push(function(e,t,s){for(var n,i=[],a=t;a<s;a+=3)n=(e[a]<<16&0xff0000)+(e[a+1]<<8&65280)+(255&e[a+2]),i.push(r[n>>18&63]+r[n>>12&63]+r[n>>6&63]+r[63&n]);return i.join("")}(e,a,a+16383>o?o:a+16383));return 1===n?i.push(r[(t=e[s-1])>>2]+r[t<<4&63]+"=="):2===n&&i.push(r[(t=(e[s-2]<<8)+e[s-1])>>10]+r[t>>4&63]+r[t<<2&63]+"="),i.join("")};for(var r=[],s=[],n="undefined"!=typeof Uint8Array?Uint8Array:Array,i="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",a=0,o=i.length;a<o;++a)r[a]=i[a],s[i.charCodeAt(a)]=a;function l(e){var t=e.length;if(t%4>0)throw Error("Invalid string. Length must be a multiple of 4");var r=e.indexOf("=");-1===r&&(r=t);var s=r===t?0:4-r%4;return[r,s]}s[45]=62,s[95]=63},9617:(e,t,r)=>{"use strict";let s,n,i,a;function o(e,t,r,s,n){if("m"===s)throw TypeError("Private method is not writable");if("a"===s&&!n)throw TypeError("Private accessor was defined without a setter");if("function"==typeof t?e!==t||!n:!t.has(e))throw TypeError("Cannot write private member to an object whose class did not declare it");return"a"===s?n.call(e,r):n?n.value=r:t.set(e,r),r}function l(e,t,r,s){if("a"===r&&!s)throw TypeError("Private accessor was defined without a getter");if("function"==typeof t?e!==t||!s:!t.has(e))throw TypeError("Cannot read private member from an object whose class did not declare it");return"m"===r?s:"a"===r?s.call(e):s?s.value:t.get(e)}r.d(t,{Ay:()=>r8});let c=function(){let{crypto:e}=globalThis;if(e?.randomUUID)return c=e.randomUUID.bind(e),e.randomUUID();let t=new Uint8Array(1),r=e?()=>e.getRandomValues(t)[0]:()=>255*Math.random()&255;return"10000000-1000-4000-8000-100000000000".replace(/[018]/g,e=>(e^r()&15>>e/4).toString(16))};function u(e){return"object"==typeof e&&null!==e&&("name"in e&&"AbortError"===e.name||"message"in e&&String(e.message).includes("FetchRequestCanceledException"))}let h=e=>{if(e instanceof Error)return e;if("object"==typeof e&&null!==e){try{if("[object Error]"===Object.prototype.toString.call(e)){let t=Error(e.message,e.cause?{cause:e.cause}:{});return e.stack&&(t.stack=e.stack),e.cause&&!t.cause&&(t.cause=e.cause),e.name&&(t.name=e.name),t}}catch{}try{return Error(JSON.stringify(e))}catch{}}return Error(e)};class f extends Error{}class d extends f{constructor(e,t,r,s){super(`${d.makeMessage(e,t,r)}`),this.status=e,this.headers=s,this.requestID=s?.get("x-request-id"),this.error=t,this.code=t?.code,this.param=t?.param,this.type=t?.type}static makeMessage(e,t,r){let s=t?.message?"string"==typeof t.message?t.message:JSON.stringify(t.message):t?JSON.stringify(t):r;return e&&s?`${e} ${s}`:e?`${e} status code (no body)`:s||"(no status code or body)"}static generate(e,t,r,s){if(!e||!s)return new m({message:r,cause:h(t)});let n=t?.error;return 400===e?new y(e,n,r,s):401===e?new w(e,n,r,s):403===e?new b(e,n,r,s):404===e?new _(e,n,r,s):409===e?new v(e,n,r,s):422===e?new x(e,n,r,s):429===e?new A(e,n,r,s):e>=500?new S(e,n,r,s):new d(e,n,r,s)}}class p extends d{constructor({message:e}={}){super(void 0,void 0,e||"Request was aborted.",void 0)}}class m extends d{constructor({message:e,cause:t}){super(void 0,void 0,e||"Connection error.",void 0),t&&(this.cause=t)}}class g extends m{constructor({message:e}={}){super({message:e??"Request timed out."})}}class y extends d{}class w extends d{}class b extends d{}class _ extends d{}class v extends d{}class x extends d{}class A extends d{}class S extends d{}class E extends f{constructor(){super("Could not parse response content as the length limit was reached")}}class I extends f{constructor(){super("Could not parse response content as the request was rejected by the content filter")}}class O extends Error{constructor(e){super(e)}}let $=/^[a-z][a-z0-9+.-]*:/i,R=e=>(R=Array.isArray)(e),k=R;function T(e){return null!=e&&"object"==typeof e&&!Array.isArray(e)}let C=e=>new Promise(t=>setTimeout(t,e)),P="5.11.0",B=e=>"x32"===e?"x32":"x86_64"===e||"x64"===e?"x64":"arm"===e?"arm":"aarch64"===e||"arm64"===e?"arm64":e?`other:${e}`:"unknown",N=e=>(e=e.toLowerCase()).includes("ios")?"iOS":"android"===e?"Android":"darwin"===e?"MacOS":"win32"===e?"Windows":"freebsd"===e?"FreeBSD":"openbsd"===e?"OpenBSD":"linux"===e?"Linux":e?`Other:${e}`:"Unknown";function L(...e){let t=globalThis.ReadableStream;if(void 0===t)throw Error("`ReadableStream` is not defined as a global; You will need to polyfill it, `globalThis.ReadableStream = ReadableStream`");return new t(...e)}function M(e){let t=Symbol.asyncIterator in e?e[Symbol.asyncIterator]():e[Symbol.iterator]();return L({start(){},async pull(e){let{done:r,value:s}=await t.next();r?e.close():e.enqueue(s)},async cancel(){await t.return?.()}})}function U(e){if(e[Symbol.asyncIterator])return e;let t=e.getReader();return{async next(){try{let e=await t.read();return e?.done&&t.releaseLock(),e}catch(e){throw t.releaseLock(),e}},async return(){let e=t.cancel();return t.releaseLock(),await e,{done:!0,value:void 0}},[Symbol.asyncIterator](){return this}}}async function j(e){if(null===e||"object"!=typeof e)return;if(e[Symbol.asyncIterator])return void await e[Symbol.asyncIterator]().return?.();let t=e.getReader(),r=t.cancel();t.releaseLock(),await r}let D=({headers:e,body:t})=>({bodyHeaders:{"content-type":"application/json"},body:JSON.stringify(t)}),W="RFC3986",F=e=>String(e),q={RFC1738:e=>String(e).replace(/%20/g,"+"),RFC3986:F},X=(e,t)=>(X=Object.hasOwn??Function.prototype.call.bind(Object.prototype.hasOwnProperty))(e,t),J=(()=>{let e=[];for(let t=0;t<256;++t)e.push("%"+((t<16?"0":"")+t.toString(16)).toUpperCase());return e})();function H(e,t){if(R(e)){let r=[];for(let s=0;s<e.length;s+=1)r.push(t(e[s]));return r}return t(e)}let K={brackets:e=>String(e)+"[]",comma:"comma",indices:(e,t)=>String(e)+"["+t+"]",repeat:e=>String(e)},V=function(e,t){Array.prototype.push.apply(e,R(t)?t:[t])},z={addQueryPrefix:!1,allowDots:!1,allowEmptyArrays:!1,arrayFormat:"indices",charset:"utf-8",charsetSentinel:!1,delimiter:"&",encode:!0,encodeDotInKeys:!1,encoder:(e,t,r,s,n)=>{if(0===e.length)return e;let i=e;if("symbol"==typeof e?i=Symbol.prototype.toString.call(e):"string"!=typeof e&&(i=String(e)),"iso-8859-1"===r)return escape(i).replace(/%u[0-9a-f]{4}/gi,function(e){return"%26%23"+parseInt(e.slice(2),16)+"%3B"});let a="";for(let e=0;e<i.length;e+=1024){let t=i.length>=1024?i.slice(e,e+1024):i,r=[];for(let e=0;e<t.length;++e){let s=t.charCodeAt(e);if(45===s||46===s||95===s||126===s||s>=48&&s<=57||s>=65&&s<=90||s>=97&&s<=122||"RFC1738"===n&&(40===s||41===s)){r[r.length]=t.charAt(e);continue}if(s<128){r[r.length]=J[s];continue}if(s<2048){r[r.length]=J[192|s>>6]+J[128|63&s];continue}if(s<55296||s>=57344){r[r.length]=J[224|s>>12]+J[128|s>>6&63]+J[128|63&s];continue}e+=1,s=65536+((1023&s)<<10|1023&t.charCodeAt(e)),r[r.length]=J[240|s>>18]+J[128|s>>12&63]+J[128|s>>6&63]+J[128|63&s]}a+=r.join("")}return a},encodeValuesOnly:!1,format:W,formatter:F,indices:!1,serializeDate:e=>(n??(n=Function.prototype.call.bind(Date.prototype.toISOString)))(e),skipNulls:!1,strictNullHandling:!1},Y={};function Q(e){let t;return(i??(i=(t=new globalThis.TextEncoder).encode.bind(t)))(e)}function G(e){let t;return(a??(a=(t=new globalThis.TextDecoder).decode.bind(t)))(e)}class Z{constructor(){tn.set(this,void 0),ti.set(this,void 0),o(this,tn,new Uint8Array,"f"),o(this,ti,null,"f")}decode(e){let t;if(null==e)return[];let r=e instanceof ArrayBuffer?new Uint8Array(e):"string"==typeof e?Q(e):e;o(this,tn,function(e){let t=0;for(let r of e)t+=r.length;let r=new Uint8Array(t),s=0;for(let t of e)r.set(t,s),s+=t.length;return r}([l(this,tn,"f"),r]),"f");let s=[];for(;null!=(t=function(e,t){for(let r=t??0;r<e.length;r++){if(10===e[r])return{preceding:r,index:r+1,carriage:!1};if(13===e[r])return{preceding:r,index:r+1,carriage:!0}}return null}(l(this,tn,"f"),l(this,ti,"f")));){if(t.carriage&&null==l(this,ti,"f")){o(this,ti,t.index,"f");continue}if(null!=l(this,ti,"f")&&(t.index!==l(this,ti,"f")+1||t.carriage)){s.push(G(l(this,tn,"f").subarray(0,l(this,ti,"f")-1))),o(this,tn,l(this,tn,"f").subarray(l(this,ti,"f")),"f"),o(this,ti,null,"f");continue}let e=null!==l(this,ti,"f")?t.preceding-1:t.preceding,r=G(l(this,tn,"f").subarray(0,e));s.push(r),o(this,tn,l(this,tn,"f").subarray(t.index),"f"),o(this,ti,null,"f")}return s}flush(){return l(this,tn,"f").length?this.decode("\n"):[]}}tn=new WeakMap,ti=new WeakMap,Z.NEWLINE_CHARS=new Set(["\n","\r"]),Z.NEWLINE_REGEXP=/\r\n|[\n\r]/g;let ee={off:0,error:200,warn:300,info:400,debug:500},et=(e,t,r)=>{if(e){if(Object.prototype.hasOwnProperty.call(ee,e))return e;ea(r).warn(`${t} was set to ${JSON.stringify(e)}, expected one of ${JSON.stringify(Object.keys(ee))}`)}};function er(){}function es(e,t,r){return!t||ee[e]>ee[r]?er:t[e].bind(t)}let en={error:er,warn:er,info:er,debug:er},ei=new WeakMap;function ea(e){let t=e.logger,r=e.logLevel??"off";if(!t)return en;let s=ei.get(t);if(s&&s[0]===r)return s[1];let n={error:es("error",t,r),warn:es("warn",t,r),info:es("info",t,r),debug:es("debug",t,r)};return ei.set(t,[r,n]),n}let eo=e=>(e.options&&(e.options={...e.options},delete e.options.headers),e.headers&&(e.headers=Object.fromEntries((e.headers instanceof Headers?[...e.headers]:Object.entries(e.headers)).map(([e,t])=>[e,"authorization"===e.toLowerCase()||"cookie"===e.toLowerCase()||"set-cookie"===e.toLowerCase()?"***":t]))),"retryOfRequestLogID"in e&&(e.retryOfRequestLogID&&(e.retryOf=e.retryOfRequestLogID),delete e.retryOfRequestLogID),e);class el{constructor(e,t,r){this.iterator=e,ta.set(this,void 0),this.controller=t,o(this,ta,r,"f")}static fromSSEResponse(e,t,r){let s=!1,n=r?ea(r):console;async function*i(){if(s)throw new f("Cannot iterate over a consumed stream, use `.tee()` to split the stream.");s=!0;let r=!1;try{for await(let s of ec(e,t))if(!r){if(s.data.startsWith("[DONE]")){r=!0;continue}if(null!==s.event&&s.event.startsWith("thread.")){let e;try{e=JSON.parse(s.data)}catch(e){throw console.error("Could not parse message into JSON:",s.data),console.error("From chunk:",s.raw),e}if("error"==s.event)throw new d(void 0,e.error,e.message,void 0);yield{event:s.event,data:e}}else{let t;try{t=JSON.parse(s.data)}catch(e){throw n.error("Could not parse message into JSON:",s.data),n.error("From chunk:",s.raw),e}if(t&&t.error)throw new d(void 0,t.error,void 0,e.headers);yield t}}r=!0}catch(e){if(u(e))return;throw e}finally{r||t.abort()}}return new el(i,t,r)}static fromReadableStream(e,t,r){let s=!1;async function*n(){let t=new Z;for await(let r of U(e))for(let e of t.decode(r))yield e;for(let e of t.flush())yield e}return new el(async function*(){if(s)throw new f("Cannot iterate over a consumed stream, use `.tee()` to split the stream.");s=!0;let e=!1;try{for await(let t of n())!e&&t&&(yield JSON.parse(t));e=!0}catch(e){if(u(e))return;throw e}finally{e||t.abort()}},t,r)}[(ta=new WeakMap,Symbol.asyncIterator)](){return this.iterator()}tee(){let e=[],t=[],r=this.iterator(),s=s=>({next:()=>{if(0===s.length){let s=r.next();e.push(s),t.push(s)}return s.shift()}});return[new el(()=>s(e),this.controller,l(this,ta,"f")),new el(()=>s(t),this.controller,l(this,ta,"f"))]}toReadableStream(){let e,t=this;return L({async start(){e=t[Symbol.asyncIterator]()},async pull(t){try{let{value:r,done:s}=await e.next();if(s)return t.close();let n=Q(JSON.stringify(r)+"\n");t.enqueue(n)}catch(e){t.error(e)}},async cancel(){await e.return?.()}})}}async function*ec(e,t){if(!e.body){if(t.abort(),void 0!==globalThis.navigator&&"ReactNative"===globalThis.navigator.product)throw new f("The default react-native fetch implementation does not support streaming. Please use expo/fetch: https://docs.expo.dev/versions/latest/sdk/expo/#expofetch-api");throw new f("Attempted to iterate over a response with no body")}let r=new eh,s=new Z;for await(let t of eu(U(e.body)))for(let e of s.decode(t)){let t=r.decode(e);t&&(yield t)}for(let e of s.flush()){let t=r.decode(e);t&&(yield t)}}async function*eu(e){let t=new Uint8Array;for await(let r of e){let e;if(null==r)continue;let s=r instanceof ArrayBuffer?new Uint8Array(r):"string"==typeof r?Q(r):r,n=new Uint8Array(t.length+s.length);for(n.set(t),n.set(s,t.length),t=n;-1!==(e=function(e){for(let t=0;t<e.length-1;t++){if(10===e[t]&&10===e[t+1]||13===e[t]&&13===e[t+1])return t+2;if(13===e[t]&&10===e[t+1]&&t+3<e.length&&13===e[t+2]&&10===e[t+3])return t+4}return -1}(t));)yield t.slice(0,e),t=t.slice(e)}t.length>0&&(yield t)}class eh{constructor(){this.event=null,this.data=[],this.chunks=[]}decode(e){if(e.endsWith("\r")&&(e=e.substring(0,e.length-1)),!e){if(!this.event&&!this.data.length)return null;let e={event:this.event,data:this.data.join("\n"),raw:this.chunks};return this.event=null,this.data=[],this.chunks=[],e}if(this.chunks.push(e),e.startsWith(":"))return null;let[t,r,s]=function(e,t){let r=e.indexOf(":");return -1!==r?[e.substring(0,r),t,e.substring(r+t.length)]:[e,"",""]}(e,":");return s.startsWith(" ")&&(s=s.substring(1)),"event"===t?this.event=s:"data"===t&&this.data.push(s),null}}async function ef(e,t){let{response:r,requestLogID:s,retryOfRequestLogID:n,startTime:i}=t,a=await (async()=>{if(t.options.stream)return(ea(e).debug("response",r.status,r.url,r.headers,r.body),t.options.__streamClass)?t.options.__streamClass.fromSSEResponse(r,t.controller,e):el.fromSSEResponse(r,t.controller,e);if(204===r.status)return null;if(t.options.__binaryResponse)return r;let s=r.headers.get("content-type"),n=s?.split(";")[0]?.trim();return n?.includes("application/json")||n?.endsWith("+json")?ed(await r.json(),r):await r.text()})();return ea(e).debug(`[${s}] response parsed`,eo({retryOfRequestLogID:n,url:r.url,status:r.status,body:a,durationMs:Date.now()-i})),a}function ed(e,t){return!e||"object"!=typeof e||Array.isArray(e)?e:Object.defineProperty(e,"_request_id",{value:t.headers.get("x-request-id"),enumerable:!1})}class ep extends Promise{constructor(e,t,r=ef){super(e=>{e(null)}),this.responsePromise=t,this.parseResponse=r,to.set(this,void 0),o(this,to,e,"f")}_thenUnwrap(e){return new ep(l(this,to,"f"),this.responsePromise,async(t,r)=>ed(e(await this.parseResponse(t,r),r),r.response))}asResponse(){return this.responsePromise.then(e=>e.response)}async withResponse(){let[e,t]=await Promise.all([this.parse(),this.asResponse()]);return{data:e,response:t,request_id:t.headers.get("x-request-id")}}parse(){return this.parsedPromise||(this.parsedPromise=this.responsePromise.then(e=>this.parseResponse(l(this,to,"f"),e))),this.parsedPromise}then(e,t){return this.parse().then(e,t)}catch(e){return this.parse().catch(e)}finally(e){return this.parse().finally(e)}}to=new WeakMap;class em{constructor(e,t,r,s){tl.set(this,void 0),o(this,tl,e,"f"),this.options=s,this.response=t,this.body=r}hasNextPage(){return!!this.getPaginatedItems().length&&null!=this.nextPageRequestOptions()}async getNextPage(){let e=this.nextPageRequestOptions();if(!e)throw new f("No next page expected; please check `.hasNextPage()` before calling `.getNextPage()`.");return await l(this,tl,"f").requestAPIList(this.constructor,e)}async *iterPages(){let e=this;for(yield e;e.hasNextPage();)e=await e.getNextPage(),yield e}async *[(tl=new WeakMap,Symbol.asyncIterator)](){for await(let e of this.iterPages())for(let t of e.getPaginatedItems())yield t}}class eg extends ep{constructor(e,t,r){super(e,t,async(e,t)=>new r(e,t.response,await ef(e,t),t.options))}async *[Symbol.asyncIterator](){for await(let e of(await this))yield e}}class ey extends em{constructor(e,t,r,s){super(e,t,r,s),this.data=r.data||[],this.object=r.object}getPaginatedItems(){return this.data??[]}nextPageRequestOptions(){return null}}class ew extends em{constructor(e,t,r,s){super(e,t,r,s),this.data=r.data||[],this.has_more=r.has_more||!1}getPaginatedItems(){return this.data??[]}hasNextPage(){return!1!==this.has_more&&super.hasNextPage()}nextPageRequestOptions(){var e;let t=this.getPaginatedItems(),r=t[t.length-1]?.id;return r?{...this.options,query:{..."object"!=typeof(e=this.options.query)?{}:e??{},after:r}}:null}}let eb=()=>{if("undefined"==typeof File){let{process:e}=globalThis;throw Error("`File` is not defined as a global, which is required for file uploads."+("string"==typeof e?.versions?.node&&20>parseInt(e.versions.node.split("."))?" Update to Node 20 LTS or newer, or set `globalThis.File` to `import('node:buffer').File`.":""))}};function e_(e,t,r){return eb(),new File(e,t??"unknown_file",r)}function ev(e){return("object"==typeof e&&null!==e&&("name"in e&&e.name&&String(e.name)||"url"in e&&e.url&&String(e.url)||"filename"in e&&e.filename&&String(e.filename)||"path"in e&&e.path&&String(e.path))||"").split(/[\\/]/).pop()||void 0}let ex=e=>null!=e&&"object"==typeof e&&"function"==typeof e[Symbol.asyncIterator],eA=async(e,t)=>({...e,body:await eE(e.body,t)}),eS=new WeakMap,eE=async(e,t)=>{if(!await function(e){let t="function"==typeof e?e:e.fetch,r=eS.get(t);if(r)return r;let s=(async()=>{try{let e="Response"in t?t.Response:(await t("data:,")).constructor,r=new FormData;if(r.toString()===await new e(r).text())return!1;return!0}catch{return!0}})();return eS.set(t,s),s}(t))throw TypeError("The provided fetch function does not support file uploads with the current global FormData class.");let r=new FormData;return await Promise.all(Object.entries(e||{}).map(([e,t])=>e$(r,e,t))),r},eI=e=>e instanceof Blob&&"name"in e,eO=e=>{if((e=>"object"==typeof e&&null!==e&&(e instanceof Response||ex(e)||eI(e)))(e))return!0;if(Array.isArray(e))return e.some(eO);if(e&&"object"==typeof e){for(let t in e)if(eO(e[t]))return!0}return!1},e$=async(e,t,r)=>{if(void 0!==r){if(null==r)throw TypeError(`Received null for "${t}"; to pass null in FormData, you must use the string 'null'`);if("string"==typeof r||"number"==typeof r||"boolean"==typeof r)e.append(t,String(r));else if(r instanceof Response)e.append(t,e_([await r.blob()],ev(r)));else if(ex(r))e.append(t,e_([await new Response(M(r)).blob()],ev(r)));else if(eI(r))e.append(t,r,ev(r));else if(Array.isArray(r))await Promise.all(r.map(r=>e$(e,t+"[]",r)));else if("object"==typeof r)await Promise.all(Object.entries(r).map(([r,s])=>e$(e,`${t}[${r}]`,s)));else throw TypeError(`Invalid value given to form, expected a string, number, boolean, object, Array, File or Blob but got ${r} instead`)}},eR=e=>null!=e&&"object"==typeof e&&"number"==typeof e.size&&"string"==typeof e.type&&"function"==typeof e.text&&"function"==typeof e.slice&&"function"==typeof e.arrayBuffer;async function ek(e,t,r){let s,n;if(eb(),null!=(s=e=await e)&&"object"==typeof s&&"string"==typeof s.name&&"number"==typeof s.lastModified&&eR(s))return e instanceof File?e:e_([await e.arrayBuffer()],e.name);if(null!=(n=e)&&"object"==typeof n&&"string"==typeof n.url&&"function"==typeof n.blob){let s=await e.blob();return t||(t=new URL(e.url).pathname.split(/[\\/]/).pop()),e_(await eT(s),t,r)}let i=await eT(e);if(t||(t=ev(e)),!r?.type){let e=i.find(e=>"object"==typeof e&&"type"in e&&e.type);"string"==typeof e&&(r={...r,type:e})}return e_(i,t,r)}async function eT(e){let t=[];if("string"==typeof e||ArrayBuffer.isView(e)||e instanceof ArrayBuffer)t.push(e);else if(eR(e))t.push(e instanceof Blob?e:await e.arrayBuffer());else if(ex(e))for await(let r of e)t.push(...await eT(r));else{let t=e?.constructor?.name;throw Error(`Unexpected data type: ${typeof e}${t?`; constructor: ${t}`:""}${function(e){if("object"!=typeof e||null===e)return"";let t=Object.getOwnPropertyNames(e);return`; props: [${t.map(e=>`"${e}"`).join(", ")}]`}(e)}`)}return t}class eC{constructor(e){this._client=e}}function eP(e){return e.replace(/[^A-Za-z0-9\-._~!$&'()*+,;=:@]+/g,encodeURIComponent)}let eB=Object.freeze(Object.create(null)),eN=((e=eP)=>function(t,...r){let s;if(1===t.length)return t[0];let n=!1,i=[],a=t.reduce((t,s,a)=>{/[?#]/.test(s)&&(n=!0);let o=r[a],l=(n?encodeURIComponent:e)(""+o);return a!==r.length&&(null==o||"object"==typeof o&&o.toString===Object.getPrototypeOf(Object.getPrototypeOf(o.hasOwnProperty??eB)??eB)?.toString)&&(l=o+"",i.push({start:t.length+s.length,length:l.length,error:`Value of type ${Object.prototype.toString.call(o).slice(8,-1)} is not a valid path parameter`})),t+s+(a===r.length?"":l)},""),o=a.split(/[?#]/,1)[0],l=/(?<=^|\/)(?:\.|%2e){1,2}(?=\/|$)/gi;for(;null!==(s=l.exec(o));)i.push({start:s.index,length:s[0].length,error:`Value "${s[0]}" can't be safely passed as a path parameter`});if(i.sort((e,t)=>e.start-t.start),i.length>0){let e=0,t=i.reduce((t,r)=>{let s=" ".repeat(r.start-e),n="^".repeat(r.length);return e=r.start+r.length,t+s+n},"");throw new f(`Path parameters result in path with invalid segments:
${i.map(e=>e.error).join("\n")}
${a}
${t}`)}return a})(eP);class eL extends eC{list(e,t={},r){return this._client.getAPIList(eN`/chat/completions/${e}/messages`,ew,{query:t,...r})}}let eM=e=>e?.role==="assistant",eU=e=>e?.role==="tool";class ej{constructor(){tc.add(this),this.controller=new AbortController,tu.set(this,void 0),th.set(this,()=>{}),tf.set(this,()=>{}),td.set(this,void 0),tp.set(this,()=>{}),tm.set(this,()=>{}),tg.set(this,{}),ty.set(this,!1),tw.set(this,!1),tb.set(this,!1),t_.set(this,!1),o(this,tu,new Promise((e,t)=>{o(this,th,e,"f"),o(this,tf,t,"f")}),"f"),o(this,td,new Promise((e,t)=>{o(this,tp,e,"f"),o(this,tm,t,"f")}),"f"),l(this,tu,"f").catch(()=>{}),l(this,td,"f").catch(()=>{})}_run(e){setTimeout(()=>{e().then(()=>{this._emitFinal(),this._emit("end")},l(this,tc,"m",tv).bind(this))},0)}_connected(){this.ended||(l(this,th,"f").call(this),this._emit("connect"))}get ended(){return l(this,ty,"f")}get errored(){return l(this,tw,"f")}get aborted(){return l(this,tb,"f")}abort(){this.controller.abort()}on(e,t){return(l(this,tg,"f")[e]||(l(this,tg,"f")[e]=[])).push({listener:t}),this}off(e,t){let r=l(this,tg,"f")[e];if(!r)return this;let s=r.findIndex(e=>e.listener===t);return s>=0&&r.splice(s,1),this}once(e,t){return(l(this,tg,"f")[e]||(l(this,tg,"f")[e]=[])).push({listener:t,once:!0}),this}emitted(e){return new Promise((t,r)=>{o(this,t_,!0,"f"),"error"!==e&&this.once("error",r),this.once(e,t)})}async done(){o(this,t_,!0,"f"),await l(this,td,"f")}_emit(e,...t){if(l(this,ty,"f"))return;"end"===e&&(o(this,ty,!0,"f"),l(this,tp,"f").call(this));let r=l(this,tg,"f")[e];if(r&&(l(this,tg,"f")[e]=r.filter(e=>!e.once),r.forEach(({listener:e})=>e(...t))),"abort"===e){let e=t[0];l(this,t_,"f")||r?.length||Promise.reject(e),l(this,tf,"f").call(this,e),l(this,tm,"f").call(this,e),this._emit("end");return}if("error"===e){let e=t[0];l(this,t_,"f")||r?.length||Promise.reject(e),l(this,tf,"f").call(this,e),l(this,tm,"f").call(this,e),this._emit("end")}}_emitFinal(){}}function eD(e){return e?.$brand==="auto-parseable-response-format"}function eW(e){return e?.$brand==="auto-parseable-tool"}function eF(e,t){let r=e.choices.map(e=>{var r,s;if("length"===e.finish_reason)throw new E;if("content_filter"===e.finish_reason)throw new I;return{...e,message:{...e.message,...e.message.tool_calls?{tool_calls:e.message.tool_calls?.map(e=>(function(e,t){let r=e.tools?.find(e=>e.function?.name===t.function.name);return{...t,function:{...t.function,parsed_arguments:eW(r)?r.$parseRaw(t.function.arguments):r?.function.strict?JSON.parse(t.function.arguments):null}}})(t,e))??void 0}:void 0,parsed:e.message.content&&!e.message.refusal?(r=t,s=e.message.content,r.response_format?.type!=="json_schema"?null:r.response_format?.type==="json_schema"?"$parseRaw"in r.response_format?r.response_format.$parseRaw(s):JSON.parse(s):null):null}}});return{...e,choices:r}}function eq(e){return!!eD(e.response_format)||(e.tools?.some(e=>eW(e)||"function"===e.type&&!0===e.function.strict)??!1)}tu=new WeakMap,th=new WeakMap,tf=new WeakMap,td=new WeakMap,tp=new WeakMap,tm=new WeakMap,tg=new WeakMap,ty=new WeakMap,tw=new WeakMap,tb=new WeakMap,t_=new WeakMap,tc=new WeakSet,tv=function(e){if(o(this,tw,!0,"f"),e instanceof Error&&"AbortError"===e.name&&(e=new p),e instanceof p)return o(this,tb,!0,"f"),this._emit("abort",e);if(e instanceof f)return this._emit("error",e);if(e instanceof Error){let t=new f(e.message);return t.cause=e,this._emit("error",t)}return this._emit("error",new f(String(e)))};class eX extends ej{constructor(){super(...arguments),tx.add(this),this._chatCompletions=[],this.messages=[]}_addChatCompletion(e){this._chatCompletions.push(e),this._emit("chatCompletion",e);let t=e.choices[0]?.message;return t&&this._addMessage(t),e}_addMessage(e,t=!0){if("content"in e||(e.content=null),this.messages.push(e),t){if(this._emit("message",e),eU(e)&&e.content)this._emit("functionToolCallResult",e.content);else if(eM(e)&&e.tool_calls)for(let t of e.tool_calls)"function"===t.type&&this._emit("functionToolCall",t.function)}}async finalChatCompletion(){await this.done();let e=this._chatCompletions[this._chatCompletions.length-1];if(!e)throw new f("stream ended without producing a ChatCompletion");return e}async finalContent(){return await this.done(),l(this,tx,"m",tA).call(this)}async finalMessage(){return await this.done(),l(this,tx,"m",tS).call(this)}async finalFunctionToolCall(){return await this.done(),l(this,tx,"m",tE).call(this)}async finalFunctionToolCallResult(){return await this.done(),l(this,tx,"m",tI).call(this)}async totalUsage(){return await this.done(),l(this,tx,"m",tO).call(this)}allChatCompletions(){return[...this._chatCompletions]}_emitFinal(){let e=this._chatCompletions[this._chatCompletions.length-1];e&&this._emit("finalChatCompletion",e);let t=l(this,tx,"m",tS).call(this);t&&this._emit("finalMessage",t);let r=l(this,tx,"m",tA).call(this);r&&this._emit("finalContent",r);let s=l(this,tx,"m",tE).call(this);s&&this._emit("finalFunctionToolCall",s);let n=l(this,tx,"m",tI).call(this);null!=n&&this._emit("finalFunctionToolCallResult",n),this._chatCompletions.some(e=>e.usage)&&this._emit("totalUsage",l(this,tx,"m",tO).call(this))}async _createChatCompletion(e,t,r){let s=r?.signal;s&&(s.aborted&&this.controller.abort(),s.addEventListener("abort",()=>this.controller.abort())),l(this,tx,"m",t$).call(this,t);let n=await e.chat.completions.create({...t,stream:!1},{...r,signal:this.controller.signal});return this._connected(),this._addChatCompletion(eF(n,t))}async _runChatCompletion(e,t,r){for(let e of t.messages)this._addMessage(e,!1);return await this._createChatCompletion(e,t,r)}async _runTools(e,t,r){let s="tool",{tool_choice:n="auto",stream:i,...a}=t,o="string"!=typeof n&&n?.function?.name,{maxChatCompletions:c=10}=r||{},u=t.tools.map(e=>{if(eW(e)){if(!e.$callback)throw new f("Tool given to `.runTools()` that does not have an associated function");return{type:"function",function:{function:e.$callback,name:e.function.name,description:e.function.description||"",parameters:e.function.parameters,parse:e.$parseRaw,strict:!0}}}return e}),h={};for(let e of u)"function"===e.type&&(h[e.function.name||e.function.function.name]=e.function);let d="tools"in t?u.map(e=>"function"===e.type?{type:"function",function:{name:e.function.name||e.function.function.name,parameters:e.function.parameters,description:e.function.description,strict:e.function.strict}}:e):void 0;for(let e of t.messages)this._addMessage(e,!1);for(let t=0;t<c;++t){let t=await this._createChatCompletion(e,{...a,tool_choice:n,tools:d,messages:[...this.messages]},r),i=t.choices[0]?.message;if(!i)throw new f("missing message in ChatCompletion response");if(!i.tool_calls?.length)break;for(let e of i.tool_calls){let t;if("function"!==e.type)continue;let r=e.id,{name:n,arguments:i}=e.function,a=h[n];if(a){if(o&&o!==n){let e=`Invalid tool_call: ${JSON.stringify(n)}. ${JSON.stringify(o)} requested. Please try again`;this._addMessage({role:s,tool_call_id:r,content:e});continue}}else{let e=`Invalid tool_call: ${JSON.stringify(n)}. Available options are: ${Object.keys(h).map(e=>JSON.stringify(e)).join(", ")}. Please try again`;this._addMessage({role:s,tool_call_id:r,content:e});continue}try{t="function"==typeof a.parse?await a.parse(i):i}catch(t){let e=t instanceof Error?t.message:String(t);this._addMessage({role:s,tool_call_id:r,content:e});continue}let c=await a.function(t,this),u=l(this,tx,"m",tR).call(this,c);if(this._addMessage({role:s,tool_call_id:r,content:u}),o)return}}}}tx=new WeakSet,tA=function(){return l(this,tx,"m",tS).call(this).content??null},tS=function(){let e=this.messages.length;for(;e-- >0;){let t=this.messages[e];if(eM(t))return{...t,content:t.content??null,refusal:t.refusal??null}}throw new f("stream ended without producing a ChatCompletionMessage with role=assistant")},tE=function(){for(let e=this.messages.length-1;e>=0;e--){let t=this.messages[e];if(eM(t)&&t?.tool_calls?.length)return t.tool_calls.at(-1)?.function}},tI=function(){for(let e=this.messages.length-1;e>=0;e--){let t=this.messages[e];if(eU(t)&&null!=t.content&&"string"==typeof t.content&&this.messages.some(e=>"assistant"===e.role&&e.tool_calls?.some(e=>"function"===e.type&&e.id===t.tool_call_id)))return t.content}},tO=function(){let e={completion_tokens:0,prompt_tokens:0,total_tokens:0};for(let{usage:t}of this._chatCompletions)t&&(e.completion_tokens+=t.completion_tokens,e.prompt_tokens+=t.prompt_tokens,e.total_tokens+=t.total_tokens);return e},t$=function(e){if(null!=e.n&&e.n>1)throw new f("ChatCompletion convenience helpers only support n=1 at this time. To use n>1, please use chat.completions.create() directly.")},tR=function(e){return"string"==typeof e?e:void 0===e?"undefined":JSON.stringify(e)};class eJ extends eX{static runTools(e,t,r){let s=new eJ,n={...r,headers:{...r?.headers,"X-Stainless-Helper-Method":"runTools"}};return s._run(()=>s._runTools(e,t,n)),s}_addMessage(e,t=!0){super._addMessage(e,t),eM(e)&&e.content&&this._emit("content",e.content)}}let eH={STR:1,NUM:2,ARR:4,OBJ:8,NULL:16,BOOL:32,NAN:64,INFINITY:128,MINUS_INFINITY:256,INF:384,ALL:511};class eK extends Error{}class eV extends Error{}let ez=e=>(function(e,t=eH.ALL){if("string"!=typeof e)throw TypeError(`expecting str, got ${typeof e}`);if(!e.trim())throw Error(`${e} is empty`);return((e,t)=>{let r=e.length,s=0,n=e=>{throw new eK(`${e} at position ${s}`)},i=e=>{throw new eV(`${e} at position ${s}`)},a=()=>(h(),s>=r&&n("Unexpected end of input"),'"'===e[s])?o():"{"===e[s]?l():"["===e[s]?c():"null"===e.substring(s,s+4)||eH.NULL&t&&r-s<4&&"null".startsWith(e.substring(s))?(s+=4,null):"true"===e.substring(s,s+4)||eH.BOOL&t&&r-s<4&&"true".startsWith(e.substring(s))?(s+=4,!0):"false"===e.substring(s,s+5)||eH.BOOL&t&&r-s<5&&"false".startsWith(e.substring(s))?(s+=5,!1):"Infinity"===e.substring(s,s+8)||eH.INFINITY&t&&r-s<8&&"Infinity".startsWith(e.substring(s))?(s+=8,1/0):"-Infinity"===e.substring(s,s+9)||eH.MINUS_INFINITY&t&&1<r-s&&r-s<9&&"-Infinity".startsWith(e.substring(s))?(s+=9,-1/0):"NaN"===e.substring(s,s+3)||eH.NAN&t&&r-s<3&&"NaN".startsWith(e.substring(s))?(s+=3,NaN):u(),o=()=>{let a=s,o=!1;for(s++;s<r&&('"'!==e[s]||o&&"\\"===e[s-1]);)o="\\"===e[s]&&!o,s++;if('"'==e.charAt(s))try{return JSON.parse(e.substring(a,++s-Number(o)))}catch(e){i(String(e))}else if(eH.STR&t)try{return JSON.parse(e.substring(a,s-Number(o))+'"')}catch(t){return JSON.parse(e.substring(a,e.lastIndexOf("\\"))+'"')}n("Unterminated string literal")},l=()=>{s++,h();let i={};try{for(;"}"!==e[s];){if(h(),s>=r&&eH.OBJ&t)return i;let n=o();h(),s++;try{let e=a();Object.defineProperty(i,n,{value:e,writable:!0,enumerable:!0,configurable:!0})}catch(e){if(eH.OBJ&t)return i;throw e}h(),","===e[s]&&s++}}catch(e){if(eH.OBJ&t)return i;n("Expected '}' at end of object")}return s++,i},c=()=>{s++;let r=[];try{for(;"]"!==e[s];)r.push(a()),h(),","===e[s]&&s++}catch(e){if(eH.ARR&t)return r;n("Expected ']' at end of array")}return s++,r},u=()=>{if(0===s){"-"===e&&eH.NUM&t&&n("Not sure what '-' is");try{return JSON.parse(e)}catch(r){if(eH.NUM&t)try{if("."===e[e.length-1])return JSON.parse(e.substring(0,e.lastIndexOf(".")));return JSON.parse(e.substring(0,e.lastIndexOf("e")))}catch(e){}i(String(r))}}let a=s;for("-"===e[s]&&s++;e[s]&&!",]}".includes(e[s]);)s++;s!=r||eH.NUM&t||n("Unterminated number literal");try{return JSON.parse(e.substring(a,s))}catch(r){"-"===e.substring(a,s)&&eH.NUM&t&&n("Not sure what '-' is");try{return JSON.parse(e.substring(a,e.lastIndexOf("e")))}catch(e){i(String(e))}}},h=()=>{for(;s<r&&" \n\r	".includes(e[s]);)s++};return a()})(e.trim(),t)})(e,eH.ALL^eH.NUM);class eY extends eX{constructor(e){super(),tk.add(this),tT.set(this,void 0),tC.set(this,void 0),tP.set(this,void 0),o(this,tT,e,"f"),o(this,tC,[],"f")}get currentChatCompletionSnapshot(){return l(this,tP,"f")}static fromReadableStream(e){let t=new eY(null);return t._run(()=>t._fromReadableStream(e)),t}static createChatCompletion(e,t,r){let s=new eY(t);return s._run(()=>s._runChatCompletion(e,{...t,stream:!0},{...r,headers:{...r?.headers,"X-Stainless-Helper-Method":"stream"}})),s}async _createChatCompletion(e,t,r){super._createChatCompletion;let s=r?.signal;s&&(s.aborted&&this.controller.abort(),s.addEventListener("abort",()=>this.controller.abort())),l(this,tk,"m",tB).call(this);let n=await e.chat.completions.create({...t,stream:!0},{...r,signal:this.controller.signal});for await(let e of(this._connected(),n))l(this,tk,"m",tL).call(this,e);if(n.controller.signal?.aborted)throw new p;return this._addChatCompletion(l(this,tk,"m",tj).call(this))}async _fromReadableStream(e,t){let r,s=t?.signal;s&&(s.aborted&&this.controller.abort(),s.addEventListener("abort",()=>this.controller.abort())),l(this,tk,"m",tB).call(this),this._connected();let n=el.fromReadableStream(e,this.controller);for await(let e of n)r&&r!==e.id&&this._addChatCompletion(l(this,tk,"m",tj).call(this)),l(this,tk,"m",tL).call(this,e),r=e.id;if(n.controller.signal?.aborted)throw new p;return this._addChatCompletion(l(this,tk,"m",tj).call(this))}[(tT=new WeakMap,tC=new WeakMap,tP=new WeakMap,tk=new WeakSet,tB=function(){this.ended||o(this,tP,void 0,"f")},tN=function(e){let t=l(this,tC,"f")[e.index];return t||(t={content_done:!1,refusal_done:!1,logprobs_content_done:!1,logprobs_refusal_done:!1,done_tool_calls:new Set,current_tool_call_index:null},l(this,tC,"f")[e.index]=t),t},tL=function(e){if(this.ended)return;let t=l(this,tk,"m",tW).call(this,e);for(let r of(this._emit("chunk",e,t),e.choices)){let e=t.choices[r.index];null!=r.delta.content&&e.message?.role==="assistant"&&e.message?.content&&(this._emit("content",r.delta.content,e.message.content),this._emit("content.delta",{delta:r.delta.content,snapshot:e.message.content,parsed:e.message.parsed})),null!=r.delta.refusal&&e.message?.role==="assistant"&&e.message?.refusal&&this._emit("refusal.delta",{delta:r.delta.refusal,snapshot:e.message.refusal}),r.logprobs?.content!=null&&e.message?.role==="assistant"&&this._emit("logprobs.content.delta",{content:r.logprobs?.content,snapshot:e.logprobs?.content??[]}),r.logprobs?.refusal!=null&&e.message?.role==="assistant"&&this._emit("logprobs.refusal.delta",{refusal:r.logprobs?.refusal,snapshot:e.logprobs?.refusal??[]});let s=l(this,tk,"m",tN).call(this,e);for(let t of(e.finish_reason&&(l(this,tk,"m",tU).call(this,e),null!=s.current_tool_call_index&&l(this,tk,"m",tM).call(this,e,s.current_tool_call_index)),r.delta.tool_calls??[]))s.current_tool_call_index!==t.index&&(l(this,tk,"m",tU).call(this,e),null!=s.current_tool_call_index&&l(this,tk,"m",tM).call(this,e,s.current_tool_call_index)),s.current_tool_call_index=t.index;for(let t of r.delta.tool_calls??[]){let r=e.message.tool_calls?.[t.index];r?.type&&(r?.type==="function"?this._emit("tool_calls.function.arguments.delta",{name:r.function?.name,index:t.index,arguments:r.function.arguments,parsed_arguments:r.function.parsed_arguments,arguments_delta:t.function?.arguments??""}):r?.type)}}},tM=function(e,t){if(l(this,tk,"m",tN).call(this,e).done_tool_calls.has(t))return;let r=e.message.tool_calls?.[t];if(!r)throw Error("no tool call snapshot");if(!r.type)throw Error("tool call snapshot missing `type`");if("function"===r.type){let e=l(this,tT,"f")?.tools?.find(e=>"function"===e.type&&e.function.name===r.function.name);this._emit("tool_calls.function.arguments.done",{name:r.function.name,index:t,arguments:r.function.arguments,parsed_arguments:eW(e)?e.$parseRaw(r.function.arguments):e?.function.strict?JSON.parse(r.function.arguments):null})}else r.type},tU=function(e){let t=l(this,tk,"m",tN).call(this,e);if(e.message.content&&!t.content_done){t.content_done=!0;let r=l(this,tk,"m",tD).call(this);this._emit("content.done",{content:e.message.content,parsed:r?r.$parseRaw(e.message.content):null})}e.message.refusal&&!t.refusal_done&&(t.refusal_done=!0,this._emit("refusal.done",{refusal:e.message.refusal})),e.logprobs?.content&&!t.logprobs_content_done&&(t.logprobs_content_done=!0,this._emit("logprobs.content.done",{content:e.logprobs.content})),e.logprobs?.refusal&&!t.logprobs_refusal_done&&(t.logprobs_refusal_done=!0,this._emit("logprobs.refusal.done",{refusal:e.logprobs.refusal}))},tj=function(){if(this.ended)throw new f("stream has ended, this shouldn't happen");let e=l(this,tP,"f");if(!e)throw new f("request ended without sending any chunks");return o(this,tP,void 0,"f"),o(this,tC,[],"f"),function(e,t){var r;let{id:s,choices:n,created:i,model:a,system_fingerprint:o,...l}=e;return r={...l,id:s,choices:n.map(({message:t,finish_reason:r,index:s,logprobs:n,...i})=>{if(!r)throw new f(`missing finish_reason for choice ${s}`);let{content:a=null,function_call:o,tool_calls:l,...c}=t,u=t.role;if(!u)throw new f(`missing role for choice ${s}`);if(o){let{arguments:e,name:l}=o;if(null==e)throw new f(`missing function_call.arguments for choice ${s}`);if(!l)throw new f(`missing function_call.name for choice ${s}`);return{...i,message:{content:a,function_call:{arguments:e,name:l},role:u,refusal:t.refusal??null},finish_reason:r,index:s,logprobs:n}}return l?{...i,index:s,finish_reason:r,logprobs:n,message:{...c,role:u,content:a,refusal:t.refusal??null,tool_calls:l.map((t,r)=>{let{function:n,type:i,id:a,...o}=t,{arguments:l,name:c,...u}=n||{};if(null==a)throw new f(`missing choices[${s}].tool_calls[${r}].id
${eQ(e)}`);if(null==i)throw new f(`missing choices[${s}].tool_calls[${r}].type
${eQ(e)}`);if(null==c)throw new f(`missing choices[${s}].tool_calls[${r}].function.name
${eQ(e)}`);if(null==l)throw new f(`missing choices[${s}].tool_calls[${r}].function.arguments
${eQ(e)}`);return{...o,id:a,type:i,function:{...u,name:c,arguments:l}}})}}:{...i,message:{...c,content:a,role:u,refusal:t.refusal??null},finish_reason:r,index:s,logprobs:n}}),created:i,model:a,object:"chat.completion",...o?{system_fingerprint:o}:{}},t&&eq(t)?eF(r,t):{...r,choices:r.choices.map(e=>({...e,message:{...e.message,parsed:null,...e.message.tool_calls?{tool_calls:e.message.tool_calls}:void 0}}))}}(e,l(this,tT,"f"))},tD=function(){let e=l(this,tT,"f")?.response_format;return eD(e)?e:null},tW=function(e){var t,r,s,n;let i=l(this,tP,"f"),{choices:a,...c}=e;for(let{delta:a,finish_reason:u,index:h,logprobs:f=null,...d}of(i?Object.assign(i,c):i=o(this,tP,{...c,choices:[]},"f"),e.choices)){let e=i.choices[h];if(e||(e=i.choices[h]={finish_reason:u,index:h,message:{},logprobs:f,...d}),f)if(e.logprobs){let{content:s,refusal:n,...i}=f;Object.assign(e.logprobs,i),s&&((t=e.logprobs).content??(t.content=[]),e.logprobs.content.push(...s)),n&&((r=e.logprobs).refusal??(r.refusal=[]),e.logprobs.refusal.push(...n))}else e.logprobs=Object.assign({},f);if(u&&(e.finish_reason=u,l(this,tT,"f")&&eq(l(this,tT,"f")))){if("length"===u)throw new E;if("content_filter"===u)throw new I}if(Object.assign(e,d),!a)continue;let{content:o,refusal:c,function_call:p,role:m,tool_calls:g,...y}=a;if(Object.assign(e.message,y),c&&(e.message.refusal=(e.message.refusal||"")+c),m&&(e.message.role=m),p&&(e.message.function_call?(p.name&&(e.message.function_call.name=p.name),p.arguments&&((s=e.message.function_call).arguments??(s.arguments=""),e.message.function_call.arguments+=p.arguments)):e.message.function_call=p),o&&(e.message.content=(e.message.content||"")+o,!e.message.refusal&&l(this,tk,"m",tD).call(this)&&(e.message.parsed=ez(e.message.content))),g)for(let{index:t,id:r,type:s,function:i,...a}of(e.message.tool_calls||(e.message.tool_calls=[]),g)){let o=(n=e.message.tool_calls)[t]??(n[t]={});Object.assign(o,a),r&&(o.id=r),s&&(o.type=s),i&&(o.function??(o.function={name:i.name??"",arguments:""})),i?.name&&(o.function.name=i.name),i?.arguments&&(o.function.arguments+=i.arguments,function(e,t){if(!e)return!1;let r=e.tools?.find(e=>e.function?.name===t.function.name);return eW(r)||r?.function.strict||!1}(l(this,tT,"f"),o)&&(o.function.parsed_arguments=ez(o.function.arguments)))}}return i},Symbol.asyncIterator)](){let e=[],t=[],r=!1;return this.on("chunk",r=>{let s=t.shift();s?s.resolve(r):e.push(r)}),this.on("end",()=>{for(let e of(r=!0,t))e.resolve(void 0);t.length=0}),this.on("abort",e=>{for(let s of(r=!0,t))s.reject(e);t.length=0}),this.on("error",e=>{for(let s of(r=!0,t))s.reject(e);t.length=0}),{next:async()=>e.length?{value:e.shift(),done:!1}:r?{value:void 0,done:!0}:new Promise((e,r)=>t.push({resolve:e,reject:r})).then(e=>e?{value:e,done:!1}:{value:void 0,done:!0}),return:async()=>(this.abort(),{value:void 0,done:!0})}}toReadableStream(){return new el(this[Symbol.asyncIterator].bind(this),this.controller).toReadableStream()}}function eQ(e){return JSON.stringify(e)}class eG extends eY{static fromReadableStream(e){let t=new eG(null);return t._run(()=>t._fromReadableStream(e)),t}static runTools(e,t,r){let s=new eG(t),n={...r,headers:{...r?.headers,"X-Stainless-Helper-Method":"runTools"}};return s._run(()=>s._runTools(e,t,n)),s}}class eZ extends eC{constructor(){super(...arguments),this.messages=new eL(this._client)}create(e,t){return this._client.post("/chat/completions",{body:e,...t,stream:e.stream??!1})}retrieve(e,t){return this._client.get(eN`/chat/completions/${e}`,t)}update(e,t,r){return this._client.post(eN`/chat/completions/${e}`,{body:t,...r})}list(e={},t){return this._client.getAPIList("/chat/completions",ew,{query:e,...t})}delete(e,t){return this._client.delete(eN`/chat/completions/${e}`,t)}parse(e,t){for(let t of e.tools??[]){if("function"!==t.type)throw new f(`Currently only \`function\` tool types support auto-parsing; Received \`${t.type}\``);if(!0!==t.function.strict)throw new f(`The \`${t.function.name}\` tool is not marked with \`strict: true\`. Only strict function tools can be auto-parsed`)}return this._client.chat.completions.create(e,{...t,headers:{...t?.headers,"X-Stainless-Helper-Method":"chat.completions.parse"}})._thenUnwrap(t=>eF(t,e))}runTools(e,t){return e.stream?eG.runTools(this._client,e,t):eJ.runTools(this._client,e,t)}stream(e,t){return eY.createChatCompletion(this._client,e,t)}}eZ.Messages=eL;class e0 extends eC{constructor(){super(...arguments),this.completions=new eZ(this._client)}}e0.Completions=eZ;let e1=Symbol("brand.privateNullableHeaders"),e2=e=>{let t=new Headers,r=new Set;for(let s of e){let e=new Set;for(let[n,i]of function*(e){let t;if(!e)return;if(e1 in e){let{values:t,nulls:r}=e;for(let e of(yield*t.entries(),r))yield[e,null];return}let r=!1;for(let s of(e instanceof Headers?t=e.entries():k(e)?t=e:(r=!0,t=Object.entries(e??{})),t)){let e=s[0];if("string"!=typeof e)throw TypeError("expected header name to be a string");let t=k(s[1])?s[1]:[s[1]],n=!1;for(let s of t)void 0!==s&&(r&&!n&&(n=!0,yield[e,null]),yield[e,s])}}(s)){let s=n.toLowerCase();e.has(s)||(t.delete(n),e.add(s)),null===i?(t.delete(n),r.add(s)):(t.append(n,i),r.delete(s))}}return{[e1]:!0,values:t,nulls:r}};class e8 extends eC{create(e,t){return this._client.post("/audio/speech",{body:e,...t,headers:e2([{Accept:"application/octet-stream"},t?.headers]),__binaryResponse:!0})}}class e6 extends eC{create(e,t){return this._client.post("/audio/transcriptions",eA({body:e,...t,stream:e.stream??!1,__metadata:{model:e.model}},this._client))}}class e5 extends eC{create(e,t){return this._client.post("/audio/translations",eA({body:e,...t,__metadata:{model:e.model}},this._client))}}class e3 extends eC{constructor(){super(...arguments),this.transcriptions=new e6(this._client),this.translations=new e5(this._client),this.speech=new e8(this._client)}}e3.Transcriptions=e6,e3.Translations=e5,e3.Speech=e8;class e4 extends eC{create(e,t){return this._client.post("/batches",{body:e,...t})}retrieve(e,t){return this._client.get(eN`/batches/${e}`,t)}list(e={},t){return this._client.getAPIList("/batches",ew,{query:e,...t})}cancel(e,t){return this._client.post(eN`/batches/${e}/cancel`,t)}}class e9 extends eC{create(e,t){return this._client.post("/assistants",{body:e,...t,headers:e2([{"OpenAI-Beta":"assistants=v2"},t?.headers])})}retrieve(e,t){return this._client.get(eN`/assistants/${e}`,{...t,headers:e2([{"OpenAI-Beta":"assistants=v2"},t?.headers])})}update(e,t,r){return this._client.post(eN`/assistants/${e}`,{body:t,...r,headers:e2([{"OpenAI-Beta":"assistants=v2"},r?.headers])})}list(e={},t){return this._client.getAPIList("/assistants",ew,{query:e,...t,headers:e2([{"OpenAI-Beta":"assistants=v2"},t?.headers])})}delete(e,t){return this._client.delete(eN`/assistants/${e}`,{...t,headers:e2([{"OpenAI-Beta":"assistants=v2"},t?.headers])})}}class e7 extends eC{create(e,t){return this._client.post("/realtime/sessions",{body:e,...t,headers:e2([{"OpenAI-Beta":"assistants=v2"},t?.headers])})}}class te extends eC{create(e,t){return this._client.post("/realtime/transcription_sessions",{body:e,...t,headers:e2([{"OpenAI-Beta":"assistants=v2"},t?.headers])})}}class tt extends eC{constructor(){super(...arguments),this.sessions=new e7(this._client),this.transcriptionSessions=new te(this._client)}}tt.Sessions=e7,tt.TranscriptionSessions=te;class tr extends eC{create(e,t,r){return this._client.post(eN`/threads/${e}/messages`,{body:t,...r,headers:e2([{"OpenAI-Beta":"assistants=v2"},r?.headers])})}retrieve(e,t,r){let{thread_id:s}=t;return this._client.get(eN`/threads/${s}/messages/${e}`,{...r,headers:e2([{"OpenAI-Beta":"assistants=v2"},r?.headers])})}update(e,t,r){let{thread_id:s,...n}=t;return this._client.post(eN`/threads/${s}/messages/${e}`,{body:n,...r,headers:e2([{"OpenAI-Beta":"assistants=v2"},r?.headers])})}list(e,t={},r){return this._client.getAPIList(eN`/threads/${e}/messages`,ew,{query:t,...r,headers:e2([{"OpenAI-Beta":"assistants=v2"},r?.headers])})}delete(e,t,r){let{thread_id:s}=t;return this._client.delete(eN`/threads/${s}/messages/${e}`,{...r,headers:e2([{"OpenAI-Beta":"assistants=v2"},r?.headers])})}}class ts extends eC{retrieve(e,t,r){let{thread_id:s,run_id:n,...i}=t;return this._client.get(eN`/threads/${s}/runs/${n}/steps/${e}`,{query:i,...r,headers:e2([{"OpenAI-Beta":"assistants=v2"},r?.headers])})}list(e,t,r){let{thread_id:s,...n}=t;return this._client.getAPIList(eN`/threads/${s}/runs/${e}/steps`,ew,{query:n,...r,headers:e2([{"OpenAI-Beta":"assistants=v2"},r?.headers])})}}var tn,ti,ta,to,tl,tc,tu,th,tf,td,tp,tm,tg,ty,tw,tb,t_,tv,tx,tA,tS,tE,tI,tO,t$,tR,tk,tT,tC,tP,tB,tN,tL,tM,tU,tj,tD,tW,tF,tq,tX,tJ,tH,tK,tV,tz,tY,tQ,tG,tZ,t0,t1,t2,t8,t6,t5,t3,t4,t9,t7,re,rt,rr,rs,rn,ri,ra,ro,rl,rc,ru,rh,rf,rd,rp,rm,rg=r(4134).hp;let ry=e=>void 0!==globalThis.process?globalThis.process.env?.[e]?.trim()??void 0:void 0!==globalThis.Deno?globalThis.Deno.env?.get?.(e)?.trim():void 0;class rw extends ej{constructor(){super(...arguments),tF.add(this),tX.set(this,[]),tJ.set(this,{}),tH.set(this,{}),tK.set(this,void 0),tV.set(this,void 0),tz.set(this,void 0),tY.set(this,void 0),tQ.set(this,void 0),tG.set(this,void 0),tZ.set(this,void 0),t0.set(this,void 0),t1.set(this,void 0)}[(tX=new WeakMap,tJ=new WeakMap,tH=new WeakMap,tK=new WeakMap,tV=new WeakMap,tz=new WeakMap,tY=new WeakMap,tQ=new WeakMap,tG=new WeakMap,tZ=new WeakMap,t0=new WeakMap,t1=new WeakMap,tF=new WeakSet,Symbol.asyncIterator)](){let e=[],t=[],r=!1;return this.on("event",r=>{let s=t.shift();s?s.resolve(r):e.push(r)}),this.on("end",()=>{for(let e of(r=!0,t))e.resolve(void 0);t.length=0}),this.on("abort",e=>{for(let s of(r=!0,t))s.reject(e);t.length=0}),this.on("error",e=>{for(let s of(r=!0,t))s.reject(e);t.length=0}),{next:async()=>e.length?{value:e.shift(),done:!1}:r?{value:void 0,done:!0}:new Promise((e,r)=>t.push({resolve:e,reject:r})).then(e=>e?{value:e,done:!1}:{value:void 0,done:!0}),return:async()=>(this.abort(),{value:void 0,done:!0})}}static fromReadableStream(e){let t=new tq;return t._run(()=>t._fromReadableStream(e)),t}async _fromReadableStream(e,t){let r=t?.signal;r&&(r.aborted&&this.controller.abort(),r.addEventListener("abort",()=>this.controller.abort())),this._connected();let s=el.fromReadableStream(e,this.controller);for await(let e of s)l(this,tF,"m",t2).call(this,e);if(s.controller.signal?.aborted)throw new p;return this._addRun(l(this,tF,"m",t8).call(this))}toReadableStream(){return new el(this[Symbol.asyncIterator].bind(this),this.controller).toReadableStream()}static createToolAssistantStream(e,t,r,s){let n=new tq;return n._run(()=>n._runToolAssistantStream(e,t,r,{...s,headers:{...s?.headers,"X-Stainless-Helper-Method":"stream"}})),n}async _createToolAssistantStream(e,t,r,s){let n=s?.signal;n&&(n.aborted&&this.controller.abort(),n.addEventListener("abort",()=>this.controller.abort()));let i={...r,stream:!0},a=await e.submitToolOutputs(t,i,{...s,signal:this.controller.signal});for await(let e of(this._connected(),a))l(this,tF,"m",t2).call(this,e);if(a.controller.signal?.aborted)throw new p;return this._addRun(l(this,tF,"m",t8).call(this))}static createThreadAssistantStream(e,t,r){let s=new tq;return s._run(()=>s._threadAssistantStream(e,t,{...r,headers:{...r?.headers,"X-Stainless-Helper-Method":"stream"}})),s}static createAssistantStream(e,t,r,s){let n=new tq;return n._run(()=>n._runAssistantStream(e,t,r,{...s,headers:{...s?.headers,"X-Stainless-Helper-Method":"stream"}})),n}currentEvent(){return l(this,tZ,"f")}currentRun(){return l(this,t0,"f")}currentMessageSnapshot(){return l(this,tK,"f")}currentRunStepSnapshot(){return l(this,t1,"f")}async finalRunSteps(){return await this.done(),Object.values(l(this,tJ,"f"))}async finalMessages(){return await this.done(),Object.values(l(this,tH,"f"))}async finalRun(){if(await this.done(),!l(this,tV,"f"))throw Error("Final run was not received.");return l(this,tV,"f")}async _createThreadAssistantStream(e,t,r){let s=r?.signal;s&&(s.aborted&&this.controller.abort(),s.addEventListener("abort",()=>this.controller.abort()));let n={...t,stream:!0},i=await e.createAndRun(n,{...r,signal:this.controller.signal});for await(let e of(this._connected(),i))l(this,tF,"m",t2).call(this,e);if(i.controller.signal?.aborted)throw new p;return this._addRun(l(this,tF,"m",t8).call(this))}async _createAssistantStream(e,t,r,s){let n=s?.signal;n&&(n.aborted&&this.controller.abort(),n.addEventListener("abort",()=>this.controller.abort()));let i={...r,stream:!0},a=await e.create(t,i,{...s,signal:this.controller.signal});for await(let e of(this._connected(),a))l(this,tF,"m",t2).call(this,e);if(a.controller.signal?.aborted)throw new p;return this._addRun(l(this,tF,"m",t8).call(this))}static accumulateDelta(e,t){for(let[r,s]of Object.entries(t)){if(!e.hasOwnProperty(r)){e[r]=s;continue}let t=e[r];if(null==t||"index"===r||"type"===r){e[r]=s;continue}if("string"==typeof t&&"string"==typeof s)t+=s;else if("number"==typeof t&&"number"==typeof s)t+=s;else if(T(t)&&T(s))t=this.accumulateDelta(t,s);else if(Array.isArray(t)&&Array.isArray(s)){if(t.every(e=>"string"==typeof e||"number"==typeof e)){t.push(...s);continue}for(let e of s){if(!T(e))throw Error(`Expected array delta entry to be an object but got: ${e}`);let r=e.index;if(null==r)throw console.error(e),Error("Expected array delta entry to have an `index` property");if("number"!=typeof r)throw Error(`Expected array delta entry \`index\` property to be a number but got ${r}`);let s=t[r];null==s?t.push(e):t[r]=this.accumulateDelta(s,e)}continue}else throw Error(`Unhandled record type: ${r}, deltaValue: ${s}, accValue: ${t}`);e[r]=t}return e}_addRun(e){return e}async _threadAssistantStream(e,t,r){return await this._createThreadAssistantStream(t,e,r)}async _runAssistantStream(e,t,r,s){return await this._createAssistantStream(t,e,r,s)}async _runToolAssistantStream(e,t,r,s){return await this._createToolAssistantStream(t,e,r,s)}}tq=rw,t2=function(e){if(!this.ended)switch(o(this,tZ,e,"f"),l(this,tF,"m",t3).call(this,e),e.event){case"thread.created":break;case"thread.run.created":case"thread.run.queued":case"thread.run.in_progress":case"thread.run.requires_action":case"thread.run.completed":case"thread.run.incomplete":case"thread.run.failed":case"thread.run.cancelling":case"thread.run.cancelled":case"thread.run.expired":l(this,tF,"m",re).call(this,e);break;case"thread.run.step.created":case"thread.run.step.in_progress":case"thread.run.step.delta":case"thread.run.step.completed":case"thread.run.step.failed":case"thread.run.step.cancelled":case"thread.run.step.expired":l(this,tF,"m",t5).call(this,e);break;case"thread.message.created":case"thread.message.in_progress":case"thread.message.delta":case"thread.message.completed":case"thread.message.incomplete":l(this,tF,"m",t6).call(this,e);break;case"error":throw Error("Encountered an error event in event processing - errors should be processed earlier")}},t8=function(){if(this.ended)throw new f("stream has ended, this shouldn't happen");if(!l(this,tV,"f"))throw Error("Final run has not been received");return l(this,tV,"f")},t6=function(e){let[t,r]=l(this,tF,"m",t9).call(this,e,l(this,tK,"f"));for(let e of(o(this,tK,t,"f"),l(this,tH,"f")[t.id]=t,r)){let r=t.content[e.index];r?.type=="text"&&this._emit("textCreated",r.text)}switch(e.event){case"thread.message.created":this._emit("messageCreated",e.data);break;case"thread.message.in_progress":break;case"thread.message.delta":if(this._emit("messageDelta",e.data.delta,t),e.data.delta.content)for(let r of e.data.delta.content){if("text"==r.type&&r.text){let e=r.text,s=t.content[r.index];if(s&&"text"==s.type)this._emit("textDelta",e,s.text);else throw Error("The snapshot associated with this text delta is not text or missing")}if(r.index!=l(this,tz,"f")){if(l(this,tY,"f"))switch(l(this,tY,"f").type){case"text":this._emit("textDone",l(this,tY,"f").text,l(this,tK,"f"));break;case"image_file":this._emit("imageFileDone",l(this,tY,"f").image_file,l(this,tK,"f"))}o(this,tz,r.index,"f")}o(this,tY,t.content[r.index],"f")}break;case"thread.message.completed":case"thread.message.incomplete":if(void 0!==l(this,tz,"f")){let t=e.data.content[l(this,tz,"f")];if(t)switch(t.type){case"image_file":this._emit("imageFileDone",t.image_file,l(this,tK,"f"));break;case"text":this._emit("textDone",t.text,l(this,tK,"f"))}}l(this,tK,"f")&&this._emit("messageDone",e.data),o(this,tK,void 0,"f")}},t5=function(e){let t=l(this,tF,"m",t4).call(this,e);switch(o(this,t1,t,"f"),e.event){case"thread.run.step.created":this._emit("runStepCreated",e.data);break;case"thread.run.step.delta":let r=e.data.delta;if(r.step_details&&"tool_calls"==r.step_details.type&&r.step_details.tool_calls&&"tool_calls"==t.step_details.type)for(let e of r.step_details.tool_calls)e.index==l(this,tQ,"f")?this._emit("toolCallDelta",e,t.step_details.tool_calls[e.index]):(l(this,tG,"f")&&this._emit("toolCallDone",l(this,tG,"f")),o(this,tQ,e.index,"f"),o(this,tG,t.step_details.tool_calls[e.index],"f"),l(this,tG,"f")&&this._emit("toolCallCreated",l(this,tG,"f")));this._emit("runStepDelta",e.data.delta,t);break;case"thread.run.step.completed":case"thread.run.step.failed":case"thread.run.step.cancelled":case"thread.run.step.expired":o(this,t1,void 0,"f"),"tool_calls"==e.data.step_details.type&&l(this,tG,"f")&&(this._emit("toolCallDone",l(this,tG,"f")),o(this,tG,void 0,"f")),this._emit("runStepDone",e.data,t)}},t3=function(e){l(this,tX,"f").push(e),this._emit("event",e)},t4=function(e){switch(e.event){case"thread.run.step.created":return l(this,tJ,"f")[e.data.id]=e.data,e.data;case"thread.run.step.delta":let t=l(this,tJ,"f")[e.data.id];if(!t)throw Error("Received a RunStepDelta before creation of a snapshot");let r=e.data;if(r.delta){let s=tq.accumulateDelta(t,r.delta);l(this,tJ,"f")[e.data.id]=s}return l(this,tJ,"f")[e.data.id];case"thread.run.step.completed":case"thread.run.step.failed":case"thread.run.step.cancelled":case"thread.run.step.expired":case"thread.run.step.in_progress":l(this,tJ,"f")[e.data.id]=e.data}if(l(this,tJ,"f")[e.data.id])return l(this,tJ,"f")[e.data.id];throw Error("No snapshot available")},t9=function(e,t){let r=[];switch(e.event){case"thread.message.created":return[e.data,r];case"thread.message.delta":if(!t)throw Error("Received a delta with no existing snapshot (there should be one from message creation)");let s=e.data;if(s.delta.content)for(let e of s.delta.content)if(e.index in t.content){let r=t.content[e.index];t.content[e.index]=l(this,tF,"m",t7).call(this,e,r)}else t.content[e.index]=e,r.push(e);return[t,r];case"thread.message.in_progress":case"thread.message.completed":case"thread.message.incomplete":if(t)return[t,r];throw Error("Received thread message event with no existing snapshot")}throw Error("Tried to accumulate a non-message event")},t7=function(e,t){return tq.accumulateDelta(t,e)},re=function(e){switch(o(this,t0,e.data,"f"),e.event){case"thread.run.created":case"thread.run.queued":case"thread.run.in_progress":break;case"thread.run.requires_action":case"thread.run.cancelled":case"thread.run.failed":case"thread.run.completed":case"thread.run.expired":case"thread.run.incomplete":o(this,tV,e.data,"f"),l(this,tG,"f")&&(this._emit("toolCallDone",l(this,tG,"f")),o(this,tG,void 0,"f"))}};class rb extends eC{constructor(){super(...arguments),this.steps=new ts(this._client)}create(e,t,r){let{include:s,...n}=t;return this._client.post(eN`/threads/${e}/runs`,{query:{include:s},body:n,...r,headers:e2([{"OpenAI-Beta":"assistants=v2"},r?.headers]),stream:t.stream??!1})}retrieve(e,t,r){let{thread_id:s}=t;return this._client.get(eN`/threads/${s}/runs/${e}`,{...r,headers:e2([{"OpenAI-Beta":"assistants=v2"},r?.headers])})}update(e,t,r){let{thread_id:s,...n}=t;return this._client.post(eN`/threads/${s}/runs/${e}`,{body:n,...r,headers:e2([{"OpenAI-Beta":"assistants=v2"},r?.headers])})}list(e,t={},r){return this._client.getAPIList(eN`/threads/${e}/runs`,ew,{query:t,...r,headers:e2([{"OpenAI-Beta":"assistants=v2"},r?.headers])})}cancel(e,t,r){let{thread_id:s}=t;return this._client.post(eN`/threads/${s}/runs/${e}/cancel`,{...r,headers:e2([{"OpenAI-Beta":"assistants=v2"},r?.headers])})}async createAndPoll(e,t,r){let s=await this.create(e,t,r);return await this.poll(s.id,{thread_id:e},r)}createAndStream(e,t,r){return rw.createAssistantStream(e,this._client.beta.threads.runs,t,r)}async poll(e,t,r){let s=e2([r?.headers,{"X-Stainless-Poll-Helper":"true","X-Stainless-Custom-Poll-Interval":r?.pollIntervalMs?.toString()??void 0}]);for(;;){let{data:n,response:i}=await this.retrieve(e,t,{...r,headers:{...r?.headers,...s}}).withResponse();switch(n.status){case"queued":case"in_progress":case"cancelling":let a=5e3;if(r?.pollIntervalMs)a=r.pollIntervalMs;else{let e=i.headers.get("openai-poll-after-ms");if(e){let t=parseInt(e);isNaN(t)||(a=t)}}await C(a);break;case"requires_action":case"incomplete":case"cancelled":case"completed":case"failed":case"expired":return n}}}stream(e,t,r){return rw.createAssistantStream(e,this._client.beta.threads.runs,t,r)}submitToolOutputs(e,t,r){let{thread_id:s,...n}=t;return this._client.post(eN`/threads/${s}/runs/${e}/submit_tool_outputs`,{body:n,...r,headers:e2([{"OpenAI-Beta":"assistants=v2"},r?.headers]),stream:t.stream??!1})}async submitToolOutputsAndPoll(e,t,r){let s=await this.submitToolOutputs(e,t,r);return await this.poll(s.id,t,r)}submitToolOutputsStream(e,t,r){return rw.createToolAssistantStream(e,this._client.beta.threads.runs,t,r)}}rb.Steps=ts;class r_ extends eC{constructor(){super(...arguments),this.runs=new rb(this._client),this.messages=new tr(this._client)}create(e={},t){return this._client.post("/threads",{body:e,...t,headers:e2([{"OpenAI-Beta":"assistants=v2"},t?.headers])})}retrieve(e,t){return this._client.get(eN`/threads/${e}`,{...t,headers:e2([{"OpenAI-Beta":"assistants=v2"},t?.headers])})}update(e,t,r){return this._client.post(eN`/threads/${e}`,{body:t,...r,headers:e2([{"OpenAI-Beta":"assistants=v2"},r?.headers])})}delete(e,t){return this._client.delete(eN`/threads/${e}`,{...t,headers:e2([{"OpenAI-Beta":"assistants=v2"},t?.headers])})}createAndRun(e,t){return this._client.post("/threads/runs",{body:e,...t,headers:e2([{"OpenAI-Beta":"assistants=v2"},t?.headers]),stream:e.stream??!1})}async createAndRunPoll(e,t){let r=await this.createAndRun(e,t);return await this.runs.poll(r.id,{thread_id:r.thread_id},t)}createAndRunStream(e,t){return rw.createThreadAssistantStream(e,this._client.beta.threads,t)}}r_.Runs=rb,r_.Messages=tr;class rv extends eC{constructor(){super(...arguments),this.realtime=new tt(this._client),this.assistants=new e9(this._client),this.threads=new r_(this._client)}}rv.Realtime=tt,rv.Assistants=e9,rv.Threads=r_;class rx extends eC{create(e,t){return this._client.post("/completions",{body:e,...t,stream:e.stream??!1})}}class rA extends eC{retrieve(e,t,r){let{container_id:s}=t;return this._client.get(eN`/containers/${s}/files/${e}/content`,{...r,headers:e2([{Accept:"application/binary"},r?.headers]),__binaryResponse:!0})}}class rS extends eC{constructor(){super(...arguments),this.content=new rA(this._client)}create(e,t,r){return this._client.post(eN`/containers/${e}/files`,eA({body:t,...r},this._client))}retrieve(e,t,r){let{container_id:s}=t;return this._client.get(eN`/containers/${s}/files/${e}`,r)}list(e,t={},r){return this._client.getAPIList(eN`/containers/${e}/files`,ew,{query:t,...r})}delete(e,t,r){let{container_id:s}=t;return this._client.delete(eN`/containers/${s}/files/${e}`,{...r,headers:e2([{Accept:"*/*"},r?.headers])})}}rS.Content=rA;class rE extends eC{constructor(){super(...arguments),this.files=new rS(this._client)}create(e,t){return this._client.post("/containers",{body:e,...t})}retrieve(e,t){return this._client.get(eN`/containers/${e}`,t)}list(e={},t){return this._client.getAPIList("/containers",ew,{query:e,...t})}delete(e,t){return this._client.delete(eN`/containers/${e}`,{...t,headers:e2([{Accept:"*/*"},t?.headers])})}}rE.Files=rS;class rI extends eC{create(e,t){let r=!!e.encoding_format,s=r?e.encoding_format:"base64";r&&ea(this._client).debug("embeddings/user defined encoding_format:",e.encoding_format);let n=this._client.post("/embeddings",{body:{...e,encoding_format:s},...t});return r?n:(ea(this._client).debug("embeddings/decoding base64 embeddings from base64"),n._thenUnwrap(e=>(e&&e.data&&e.data.forEach(e=>{let t=e.embedding;e.embedding=(e=>{if(void 0!==rg){let t=rg.from(e,"base64");return Array.from(new Float32Array(t.buffer,t.byteOffset,t.length/Float32Array.BYTES_PER_ELEMENT))}{let t=atob(e),r=t.length,s=new Uint8Array(r);for(let e=0;e<r;e++)s[e]=t.charCodeAt(e);return Array.from(new Float32Array(s.buffer))}})(t)}),e)))}}class rO extends eC{retrieve(e,t,r){let{eval_id:s,run_id:n}=t;return this._client.get(eN`/evals/${s}/runs/${n}/output_items/${e}`,r)}list(e,t,r){let{eval_id:s,...n}=t;return this._client.getAPIList(eN`/evals/${s}/runs/${e}/output_items`,ew,{query:n,...r})}}class r$ extends eC{constructor(){super(...arguments),this.outputItems=new rO(this._client)}create(e,t,r){return this._client.post(eN`/evals/${e}/runs`,{body:t,...r})}retrieve(e,t,r){let{eval_id:s}=t;return this._client.get(eN`/evals/${s}/runs/${e}`,r)}list(e,t={},r){return this._client.getAPIList(eN`/evals/${e}/runs`,ew,{query:t,...r})}delete(e,t,r){let{eval_id:s}=t;return this._client.delete(eN`/evals/${s}/runs/${e}`,r)}cancel(e,t,r){let{eval_id:s}=t;return this._client.post(eN`/evals/${s}/runs/${e}`,r)}}r$.OutputItems=rO;class rR extends eC{constructor(){super(...arguments),this.runs=new r$(this._client)}create(e,t){return this._client.post("/evals",{body:e,...t})}retrieve(e,t){return this._client.get(eN`/evals/${e}`,t)}update(e,t,r){return this._client.post(eN`/evals/${e}`,{body:t,...r})}list(e={},t){return this._client.getAPIList("/evals",ew,{query:e,...t})}delete(e,t){return this._client.delete(eN`/evals/${e}`,t)}}rR.Runs=r$;class rk extends eC{create(e,t){return this._client.post("/files",eA({body:e,...t},this._client))}retrieve(e,t){return this._client.get(eN`/files/${e}`,t)}list(e={},t){return this._client.getAPIList("/files",ew,{query:e,...t})}delete(e,t){return this._client.delete(eN`/files/${e}`,t)}content(e,t){return this._client.get(eN`/files/${e}/content`,{...t,headers:e2([{Accept:"application/binary"},t?.headers]),__binaryResponse:!0})}async waitForProcessing(e,{pollInterval:t=5e3,maxWait:r=18e5}={}){let s=new Set(["processed","error","deleted"]),n=Date.now(),i=await this.retrieve(e);for(;!i.status||!s.has(i.status);)if(await C(t),i=await this.retrieve(e),Date.now()-n>r)throw new g({message:`Giving up on waiting for file ${e} to finish processing after ${r} milliseconds.`});return i}}class rT extends eC{}class rC extends eC{run(e,t){return this._client.post("/fine_tuning/alpha/graders/run",{body:e,...t})}validate(e,t){return this._client.post("/fine_tuning/alpha/graders/validate",{body:e,...t})}}class rP extends eC{constructor(){super(...arguments),this.graders=new rC(this._client)}}rP.Graders=rC;class rB extends eC{create(e,t,r){return this._client.getAPIList(eN`/fine_tuning/checkpoints/${e}/permissions`,ey,{body:t,method:"post",...r})}retrieve(e,t={},r){return this._client.get(eN`/fine_tuning/checkpoints/${e}/permissions`,{query:t,...r})}delete(e,t,r){let{fine_tuned_model_checkpoint:s}=t;return this._client.delete(eN`/fine_tuning/checkpoints/${s}/permissions/${e}`,r)}}class rN extends eC{constructor(){super(...arguments),this.permissions=new rB(this._client)}}rN.Permissions=rB;class rL extends eC{list(e,t={},r){return this._client.getAPIList(eN`/fine_tuning/jobs/${e}/checkpoints`,ew,{query:t,...r})}}class rM extends eC{constructor(){super(...arguments),this.checkpoints=new rL(this._client)}create(e,t){return this._client.post("/fine_tuning/jobs",{body:e,...t})}retrieve(e,t){return this._client.get(eN`/fine_tuning/jobs/${e}`,t)}list(e={},t){return this._client.getAPIList("/fine_tuning/jobs",ew,{query:e,...t})}cancel(e,t){return this._client.post(eN`/fine_tuning/jobs/${e}/cancel`,t)}listEvents(e,t={},r){return this._client.getAPIList(eN`/fine_tuning/jobs/${e}/events`,ew,{query:t,...r})}pause(e,t){return this._client.post(eN`/fine_tuning/jobs/${e}/pause`,t)}resume(e,t){return this._client.post(eN`/fine_tuning/jobs/${e}/resume`,t)}}rM.Checkpoints=rL;class rU extends eC{constructor(){super(...arguments),this.methods=new rT(this._client),this.jobs=new rM(this._client),this.checkpoints=new rN(this._client),this.alpha=new rP(this._client)}}rU.Methods=rT,rU.Jobs=rM,rU.Checkpoints=rN,rU.Alpha=rP;class rj extends eC{}class rD extends eC{constructor(){super(...arguments),this.graderModels=new rj(this._client)}}rD.GraderModels=rj;class rW extends eC{createVariation(e,t){return this._client.post("/images/variations",eA({body:e,...t},this._client))}edit(e,t){return this._client.post("/images/edits",eA({body:e,...t,stream:e.stream??!1},this._client))}generate(e,t){return this._client.post("/images/generations",{body:e,...t,stream:e.stream??!1})}}class rF extends eC{retrieve(e,t){return this._client.get(eN`/models/${e}`,t)}list(e){return this._client.getAPIList("/models",ey,e)}delete(e,t){return this._client.delete(eN`/models/${e}`,t)}}class rq extends eC{create(e,t){return this._client.post("/moderations",{body:e,...t})}}function rX(e,t){let r=e.output.map(e=>{if("function_call"===e.type)return{...e,parsed_arguments:function(e,t){var r,s;let n=(r=e.tools??[],s=t.name,r.find(e=>"function"===e.type&&e.name===s));return{...t,...t,parsed_arguments:n?.$brand==="auto-parseable-tool"?n.$parseRaw(t.arguments):n?.strict?JSON.parse(t.arguments):null}}(t,e)};if("message"===e.type){let r=e.content.map(e=>{var r,s;return"output_text"===e.type?{...e,parsed:(r=t,s=e.text,r.text?.format?.type!=="json_schema"?null:"$parseRaw"in r.text?.format?(r.text?.format).$parseRaw(s):JSON.parse(s))}:e});return{...e,content:r}}return e}),s=Object.assign({},e,{output:r});return Object.getOwnPropertyDescriptor(e,"output_text")||rJ(s),Object.defineProperty(s,"output_parsed",{enumerable:!0,get(){for(let e of s.output)if("message"===e.type){for(let t of e.content)if("output_text"===t.type&&null!==t.parsed)return t.parsed}return null}}),s}function rJ(e){let t=[];for(let r of e.output)if("message"===r.type)for(let e of r.content)"output_text"===e.type&&t.push(e.text);e.output_text=t.join("")}class rH extends ej{constructor(e){super(),rt.add(this),rr.set(this,void 0),rs.set(this,void 0),rn.set(this,void 0),o(this,rr,e,"f")}static createResponse(e,t,r){let s=new rH(t);return s._run(()=>s._createOrRetrieveResponse(e,t,{...r,headers:{...r?.headers,"X-Stainless-Helper-Method":"stream"}})),s}async _createOrRetrieveResponse(e,t,r){let s,n=r?.signal;n&&(n.aborted&&this.controller.abort(),n.addEventListener("abort",()=>this.controller.abort())),l(this,rt,"m",ri).call(this);let i=null;for await(let n of("response_id"in t?(s=await e.responses.retrieve(t.response_id,{stream:!0},{...r,signal:this.controller.signal,stream:!0}),i=t.starting_after??null):s=await e.responses.create({...t,stream:!0},{...r,signal:this.controller.signal}),this._connected(),s))l(this,rt,"m",ra).call(this,n,i);if(s.controller.signal?.aborted)throw new p;return l(this,rt,"m",ro).call(this)}[(rr=new WeakMap,rs=new WeakMap,rn=new WeakMap,rt=new WeakSet,ri=function(){this.ended||o(this,rs,void 0,"f")},ra=function(e,t){if(this.ended)return;let r=(e,r)=>{(null==t||r.sequence_number>t)&&this._emit(e,r)},s=l(this,rt,"m",rl).call(this,e);switch(r("event",e),e.type){case"response.output_text.delta":{let t=s.output[e.output_index];if(!t)throw new f(`missing output at index ${e.output_index}`);if("message"===t.type){let s=t.content[e.content_index];if(!s)throw new f(`missing content at index ${e.content_index}`);if("output_text"!==s.type)throw new f(`expected content to be 'output_text', got ${s.type}`);r("response.output_text.delta",{...e,snapshot:s.text})}break}case"response.function_call_arguments.delta":{let t=s.output[e.output_index];if(!t)throw new f(`missing output at index ${e.output_index}`);"function_call"===t.type&&r("response.function_call_arguments.delta",{...e,snapshot:t.arguments});break}default:r(e.type,e)}},ro=function(){if(this.ended)throw new f("stream has ended, this shouldn't happen");let e=l(this,rs,"f");if(!e)throw new f("request ended without sending any events");o(this,rs,void 0,"f");let t=function(e,t){var r;return t&&(r=t,eD(r.text?.format))?rX(e,t):{...e,output_parsed:null,output:e.output.map(e=>"function_call"===e.type?{...e,parsed_arguments:null}:"message"===e.type?{...e,content:e.content.map(e=>({...e,parsed:null}))}:e)}}(e,l(this,rr,"f"));return o(this,rn,t,"f"),t},rl=function(e){let t=l(this,rs,"f");if(!t){if("response.created"!==e.type)throw new f(`When snapshot hasn't been set yet, expected 'response.created' event, got ${e.type}`);return o(this,rs,e.response,"f")}switch(e.type){case"response.output_item.added":t.output.push(e.item);break;case"response.content_part.added":{let r=t.output[e.output_index];if(!r)throw new f(`missing output at index ${e.output_index}`);"message"===r.type&&r.content.push(e.part);break}case"response.output_text.delta":{let r=t.output[e.output_index];if(!r)throw new f(`missing output at index ${e.output_index}`);if("message"===r.type){let t=r.content[e.content_index];if(!t)throw new f(`missing content at index ${e.content_index}`);if("output_text"!==t.type)throw new f(`expected content to be 'output_text', got ${t.type}`);t.text+=e.delta}break}case"response.function_call_arguments.delta":{let r=t.output[e.output_index];if(!r)throw new f(`missing output at index ${e.output_index}`);"function_call"===r.type&&(r.arguments+=e.delta);break}case"response.completed":o(this,rs,e.response,"f")}return t},Symbol.asyncIterator)](){let e=[],t=[],r=!1;return this.on("event",r=>{let s=t.shift();s?s.resolve(r):e.push(r)}),this.on("end",()=>{for(let e of(r=!0,t))e.resolve(void 0);t.length=0}),this.on("abort",e=>{for(let s of(r=!0,t))s.reject(e);t.length=0}),this.on("error",e=>{for(let s of(r=!0,t))s.reject(e);t.length=0}),{next:async()=>e.length?{value:e.shift(),done:!1}:r?{value:void 0,done:!0}:new Promise((e,r)=>t.push({resolve:e,reject:r})).then(e=>e?{value:e,done:!1}:{value:void 0,done:!0}),return:async()=>(this.abort(),{value:void 0,done:!0})}}async finalResponse(){await this.done();let e=l(this,rn,"f");if(!e)throw new f("stream ended without producing a ChatCompletion");return e}}class rK extends eC{list(e,t={},r){return this._client.getAPIList(eN`/responses/${e}/input_items`,ew,{query:t,...r})}}class rV extends eC{constructor(){super(...arguments),this.inputItems=new rK(this._client)}create(e,t){return this._client.post("/responses",{body:e,...t,stream:e.stream??!1})._thenUnwrap(e=>("object"in e&&"response"===e.object&&rJ(e),e))}retrieve(e,t={},r){return this._client.get(eN`/responses/${e}`,{query:t,...r,stream:t?.stream??!1})._thenUnwrap(e=>("object"in e&&"response"===e.object&&rJ(e),e))}delete(e,t){return this._client.delete(eN`/responses/${e}`,{...t,headers:e2([{Accept:"*/*"},t?.headers])})}parse(e,t){return this._client.responses.create(e,t)._thenUnwrap(t=>rX(t,e))}stream(e,t){return rH.createResponse(this._client,e,t)}cancel(e,t){return this._client.post(eN`/responses/${e}/cancel`,t)}}rV.InputItems=rK;class rz extends eC{create(e,t,r){return this._client.post(eN`/uploads/${e}/parts`,eA({body:t,...r},this._client))}}class rY extends eC{constructor(){super(...arguments),this.parts=new rz(this._client)}create(e,t){return this._client.post("/uploads",{body:e,...t})}cancel(e,t){return this._client.post(eN`/uploads/${e}/cancel`,t)}complete(e,t,r){return this._client.post(eN`/uploads/${e}/complete`,{body:t,...r})}}rY.Parts=rz;let rQ=async e=>{let t=await Promise.allSettled(e),r=t.filter(e=>"rejected"===e.status);if(r.length){for(let e of r)console.error(e.reason);throw Error(`${r.length} promise(s) failed - see the above errors`)}let s=[];for(let e of t)"fulfilled"===e.status&&s.push(e.value);return s};class rG extends eC{create(e,t,r){return this._client.post(eN`/vector_stores/${e}/file_batches`,{body:t,...r,headers:e2([{"OpenAI-Beta":"assistants=v2"},r?.headers])})}retrieve(e,t,r){let{vector_store_id:s}=t;return this._client.get(eN`/vector_stores/${s}/file_batches/${e}`,{...r,headers:e2([{"OpenAI-Beta":"assistants=v2"},r?.headers])})}cancel(e,t,r){let{vector_store_id:s}=t;return this._client.post(eN`/vector_stores/${s}/file_batches/${e}/cancel`,{...r,headers:e2([{"OpenAI-Beta":"assistants=v2"},r?.headers])})}async createAndPoll(e,t,r){let s=await this.create(e,t);return await this.poll(e,s.id,r)}listFiles(e,t,r){let{vector_store_id:s,...n}=t;return this._client.getAPIList(eN`/vector_stores/${s}/file_batches/${e}/files`,ew,{query:n,...r,headers:e2([{"OpenAI-Beta":"assistants=v2"},r?.headers])})}async poll(e,t,r){let s=e2([r?.headers,{"X-Stainless-Poll-Helper":"true","X-Stainless-Custom-Poll-Interval":r?.pollIntervalMs?.toString()??void 0}]);for(;;){let{data:n,response:i}=await this.retrieve(t,{vector_store_id:e},{...r,headers:s}).withResponse();switch(n.status){case"in_progress":let a=5e3;if(r?.pollIntervalMs)a=r.pollIntervalMs;else{let e=i.headers.get("openai-poll-after-ms");if(e){let t=parseInt(e);isNaN(t)||(a=t)}}await C(a);break;case"failed":case"cancelled":case"completed":return n}}}async uploadAndPoll(e,{files:t,fileIds:r=[]},s){if(null==t||0==t.length)throw Error("No `files` provided to process. If you've already uploaded files you should use `.createAndPoll()` instead");let n=Math.min(s?.maxConcurrency??5,t.length),i=this._client,a=t.values(),o=[...r];async function l(e){for(let t of e){let e=await i.files.create({file:t,purpose:"assistants"},s);o.push(e.id)}}let c=Array(n).fill(a).map(l);return await rQ(c),await this.createAndPoll(e,{file_ids:o})}}class rZ extends eC{create(e,t,r){return this._client.post(eN`/vector_stores/${e}/files`,{body:t,...r,headers:e2([{"OpenAI-Beta":"assistants=v2"},r?.headers])})}retrieve(e,t,r){let{vector_store_id:s}=t;return this._client.get(eN`/vector_stores/${s}/files/${e}`,{...r,headers:e2([{"OpenAI-Beta":"assistants=v2"},r?.headers])})}update(e,t,r){let{vector_store_id:s,...n}=t;return this._client.post(eN`/vector_stores/${s}/files/${e}`,{body:n,...r,headers:e2([{"OpenAI-Beta":"assistants=v2"},r?.headers])})}list(e,t={},r){return this._client.getAPIList(eN`/vector_stores/${e}/files`,ew,{query:t,...r,headers:e2([{"OpenAI-Beta":"assistants=v2"},r?.headers])})}delete(e,t,r){let{vector_store_id:s}=t;return this._client.delete(eN`/vector_stores/${s}/files/${e}`,{...r,headers:e2([{"OpenAI-Beta":"assistants=v2"},r?.headers])})}async createAndPoll(e,t,r){let s=await this.create(e,t,r);return await this.poll(e,s.id,r)}async poll(e,t,r){let s=e2([r?.headers,{"X-Stainless-Poll-Helper":"true","X-Stainless-Custom-Poll-Interval":r?.pollIntervalMs?.toString()??void 0}]);for(;;){let n=await this.retrieve(t,{vector_store_id:e},{...r,headers:s}).withResponse(),i=n.data;switch(i.status){case"in_progress":let a=5e3;if(r?.pollIntervalMs)a=r.pollIntervalMs;else{let e=n.response.headers.get("openai-poll-after-ms");if(e){let t=parseInt(e);isNaN(t)||(a=t)}}await C(a);break;case"failed":case"completed":return i}}}async upload(e,t,r){let s=await this._client.files.create({file:t,purpose:"assistants"},r);return this.create(e,{file_id:s.id},r)}async uploadAndPoll(e,t,r){let s=await this.upload(e,t,r);return await this.poll(e,s.id,r)}content(e,t,r){let{vector_store_id:s}=t;return this._client.getAPIList(eN`/vector_stores/${s}/files/${e}/content`,ey,{...r,headers:e2([{"OpenAI-Beta":"assistants=v2"},r?.headers])})}}class r0 extends eC{constructor(){super(...arguments),this.files=new rZ(this._client),this.fileBatches=new rG(this._client)}create(e,t){return this._client.post("/vector_stores",{body:e,...t,headers:e2([{"OpenAI-Beta":"assistants=v2"},t?.headers])})}retrieve(e,t){return this._client.get(eN`/vector_stores/${e}`,{...t,headers:e2([{"OpenAI-Beta":"assistants=v2"},t?.headers])})}update(e,t,r){return this._client.post(eN`/vector_stores/${e}`,{body:t,...r,headers:e2([{"OpenAI-Beta":"assistants=v2"},r?.headers])})}list(e={},t){return this._client.getAPIList("/vector_stores",ew,{query:e,...t,headers:e2([{"OpenAI-Beta":"assistants=v2"},t?.headers])})}delete(e,t){return this._client.delete(eN`/vector_stores/${e}`,{...t,headers:e2([{"OpenAI-Beta":"assistants=v2"},t?.headers])})}search(e,t,r){return this._client.getAPIList(eN`/vector_stores/${e}/search`,ey,{body:t,method:"post",...r,headers:e2([{"OpenAI-Beta":"assistants=v2"},r?.headers])})}}r0.Files=rZ,r0.FileBatches=rG;var r1=r(4134).hp;class r2 extends eC{async unwrap(e,t){let r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:this._client.webhookSecret,s=arguments.length>3&&void 0!==arguments[3]?arguments[3]:300;return await this.verifySignature(e,t,r,s),JSON.parse(e)}async verifySignature(e,t){let r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:this._client.webhookSecret,s=arguments.length>3&&void 0!==arguments[3]?arguments[3]:300;if("undefined"==typeof crypto||"function"!=typeof crypto.subtle.importKey||"function"!=typeof crypto.subtle.verify)throw Error("Webhook signature verification is only supported when the `crypto` global is defined");l(this,rc,"m",ru).call(this,r);let n=e2([t]).values,i=l(this,rc,"m",rh).call(this,n,"webhook-signature"),a=l(this,rc,"m",rh).call(this,n,"webhook-timestamp"),o=l(this,rc,"m",rh).call(this,n,"webhook-id"),c=parseInt(a,10);if(isNaN(c))throw new O("Invalid webhook timestamp format");let u=Math.floor(Date.now()/1e3);if(u-c>s)throw new O("Webhook timestamp is too old");if(c>u+s)throw new O("Webhook timestamp is too new");let h=i.split(" ").map(e=>e.startsWith("v1,")?e.substring(3):e),f=r.startsWith("whsec_")?r1.from(r.replace("whsec_",""),"base64"):r1.from(r,"utf-8"),d=o?"".concat(o,".").concat(a,".").concat(e):"".concat(a,".").concat(e),p=await crypto.subtle.importKey("raw",f,{name:"HMAC",hash:"SHA-256"},!1,["verify"]);for(let e of h)try{let t=r1.from(e,"base64");if(await crypto.subtle.verify("HMAC",p,t,new TextEncoder().encode(d)))return}catch(e){continue}throw new O("The given webhook signature does not match the expected signature")}constructor(){super(...arguments),rc.add(this)}}rc=new WeakSet,ru=function(e){if("string"!=typeof e||0===e.length)throw Error("The webhook secret must either be set using the env var, OPENAI_WEBHOOK_SECRET, on the client class, OpenAI({ webhookSecret: '123' }), or passed to this function")},rh=function(e,t){if(!e)throw Error("Headers are required");let r=e.get(t);if(null==r)throw Error("Missing required header: ".concat(t));return r};class r8{constructor({baseURL:e=ry("OPENAI_BASE_URL"),apiKey:t=ry("OPENAI_API_KEY"),organization:r=ry("OPENAI_ORG_ID")??null,project:s=ry("OPENAI_PROJECT_ID")??null,webhookSecret:n=ry("OPENAI_WEBHOOK_SECRET")??null,...i}={}){if(rf.add(this),rp.set(this,void 0),this.completions=new rx(this),this.chat=new e0(this),this.embeddings=new rI(this),this.files=new rk(this),this.images=new rW(this),this.audio=new e3(this),this.moderations=new rq(this),this.models=new rF(this),this.fineTuning=new rU(this),this.graders=new rD(this),this.vectorStores=new r0(this),this.webhooks=new r2(this),this.beta=new rv(this),this.batches=new e4(this),this.uploads=new rY(this),this.responses=new rV(this),this.evals=new rR(this),this.containers=new rE(this),void 0===t)throw new f("The OPENAI_API_KEY environment variable is missing or empty; either provide it, or instantiate the OpenAI client with an apiKey option, like new OpenAI({ apiKey: 'My API Key' }).");let a={apiKey:t,organization:r,project:s,webhookSecret:n,...i,baseURL:e||"https://api.openai.com/v1"};if(!a.dangerouslyAllowBrowser&&"undefined"!=typeof window&&void 0!==window.document&&"undefined"!=typeof navigator)throw new f("It looks like you're running in a browser-like environment.\n\nThis is disabled by default, as it risks exposing your secret API credentials to attackers.\nIf you understand the risks and have appropriate mitigations in place,\nyou can set the `dangerouslyAllowBrowser` option to `true`, e.g.,\n\nnew OpenAI({ apiKey, dangerouslyAllowBrowser: true });\n\nhttps://help.openai.com/en/articles/5112595-best-practices-for-api-key-safety\n");this.baseURL=a.baseURL,this.timeout=a.timeout??rd.DEFAULT_TIMEOUT,this.logger=a.logger??console;let l="warn";this.logLevel=l,this.logLevel=et(a.logLevel,"ClientOptions.logLevel",this)??et(ry("OPENAI_LOG"),"process.env['OPENAI_LOG']",this)??l,this.fetchOptions=a.fetchOptions,this.maxRetries=a.maxRetries??2,this.fetch=a.fetch??function(){if("undefined"!=typeof fetch)return fetch;throw Error("`fetch` is not defined as a global; Either pass `fetch` to the client, `new OpenAI({ fetch })` or polyfill the global, `globalThis.fetch = fetch`")}(),o(this,rp,D,"f"),this._options=a,this.apiKey=t,this.organization=r,this.project=s,this.webhookSecret=n}withOptions(e){return new this.constructor({...this._options,baseURL:this.baseURL,maxRetries:this.maxRetries,timeout:this.timeout,logger:this.logger,logLevel:this.logLevel,fetch:this.fetch,fetchOptions:this.fetchOptions,apiKey:this.apiKey,organization:this.organization,project:this.project,webhookSecret:this.webhookSecret,...e})}defaultQuery(){return this._options.defaultQuery}validateHeaders({values:e,nulls:t}){}async authHeaders(e){return e2([{Authorization:`Bearer ${this.apiKey}`}])}stringifyQuery(e){return function(e,t={}){let r,s=e,n=function(e=z){let t;if(void 0!==e.allowEmptyArrays&&"boolean"!=typeof e.allowEmptyArrays)throw TypeError("`allowEmptyArrays` option can only be `true` or `false`, when provided");if(void 0!==e.encodeDotInKeys&&"boolean"!=typeof e.encodeDotInKeys)throw TypeError("`encodeDotInKeys` option can only be `true` or `false`, when provided");if(null!==e.encoder&&void 0!==e.encoder&&"function"!=typeof e.encoder)throw TypeError("Encoder has to be a function.");let r=e.charset||z.charset;if(void 0!==e.charset&&"utf-8"!==e.charset&&"iso-8859-1"!==e.charset)throw TypeError("The charset option must be either utf-8, iso-8859-1, or undefined");let s=W;if(void 0!==e.format){if(!X(q,e.format))throw TypeError("Unknown format option provided.");s=e.format}let n=q[s],i=z.filter;if(("function"==typeof e.filter||R(e.filter))&&(i=e.filter),t=e.arrayFormat&&e.arrayFormat in K?e.arrayFormat:"indices"in e?e.indices?"indices":"repeat":z.arrayFormat,"commaRoundTrip"in e&&"boolean"!=typeof e.commaRoundTrip)throw TypeError("`commaRoundTrip` must be a boolean, or absent");let a=void 0===e.allowDots?!0==!!e.encodeDotInKeys||z.allowDots:!!e.allowDots;return{addQueryPrefix:"boolean"==typeof e.addQueryPrefix?e.addQueryPrefix:z.addQueryPrefix,allowDots:a,allowEmptyArrays:"boolean"==typeof e.allowEmptyArrays?!!e.allowEmptyArrays:z.allowEmptyArrays,arrayFormat:t,charset:r,charsetSentinel:"boolean"==typeof e.charsetSentinel?e.charsetSentinel:z.charsetSentinel,commaRoundTrip:!!e.commaRoundTrip,delimiter:void 0===e.delimiter?z.delimiter:e.delimiter,encode:"boolean"==typeof e.encode?e.encode:z.encode,encodeDotInKeys:"boolean"==typeof e.encodeDotInKeys?e.encodeDotInKeys:z.encodeDotInKeys,encoder:"function"==typeof e.encoder?e.encoder:z.encoder,encodeValuesOnly:"boolean"==typeof e.encodeValuesOnly?e.encodeValuesOnly:z.encodeValuesOnly,filter:i,format:s,formatter:n,serializeDate:"function"==typeof e.serializeDate?e.serializeDate:z.serializeDate,skipNulls:"boolean"==typeof e.skipNulls?e.skipNulls:z.skipNulls,sort:"function"==typeof e.sort?e.sort:null,strictNullHandling:"boolean"==typeof e.strictNullHandling?e.strictNullHandling:z.strictNullHandling}}(t);"function"==typeof n.filter?s=(0,n.filter)("",s):R(n.filter)&&(r=n.filter);let i=[];if("object"!=typeof s||null===s)return"";let a=K[n.arrayFormat],o="comma"===a&&n.commaRoundTrip;r||(r=Object.keys(s)),n.sort&&r.sort(n.sort);let l=new WeakMap;for(let e=0;e<r.length;++e){let t=r[e];n.skipNulls&&null===s[t]||V(i,function e(t,r,s,n,i,a,o,l,c,u,h,f,d,p,m,g,y,w){var b,_;let v,x=t,A=w,S=0,E=!1;for(;void 0!==(A=A.get(Y))&&!E;){let e=A.get(t);if(S+=1,void 0!==e)if(e===S)throw RangeError("Cyclic object value");else E=!0;void 0===A.get(Y)&&(S=0)}if("function"==typeof u?x=u(r,x):x instanceof Date?x=d?.(x):"comma"===s&&R(x)&&(x=H(x,function(e){return e instanceof Date?d?.(e):e})),null===x){if(a)return c&&!g?c(r,z.encoder,y,"key",p):r;x=""}if("string"==typeof(b=x)||"number"==typeof b||"boolean"==typeof b||"symbol"==typeof b||"bigint"==typeof b||(_=x)&&"object"==typeof _&&_.constructor&&_.constructor.isBuffer&&_.constructor.isBuffer(_)){if(c){let e=g?r:c(r,z.encoder,y,"key",p);return[m?.(e)+"="+m?.(c(x,z.encoder,y,"value",p))]}return[m?.(r)+"="+m?.(String(x))]}let I=[];if(void 0===x)return I;if("comma"===s&&R(x))g&&c&&(x=H(x,c)),v=[{value:x.length>0?x.join(",")||null:void 0}];else if(R(u))v=u;else{let e=Object.keys(x);v=h?e.sort(h):e}let O=l?String(r).replace(/\./g,"%2E"):String(r),$=n&&R(x)&&1===x.length?O+"[]":O;if(i&&R(x)&&0===x.length)return $+"[]";for(let r=0;r<v.length;++r){let b=v[r],_="object"==typeof b&&void 0!==b.value?b.value:x[b];if(o&&null===_)continue;let A=f&&l?b.replace(/\./g,"%2E"):b,E=R(x)?"function"==typeof s?s($,A):$:$+(f?"."+A:"["+A+"]");w.set(t,S);let O=new WeakMap;O.set(Y,w),V(I,e(_,E,s,n,i,a,o,l,"comma"===s&&g&&R(x)?null:c,u,h,f,d,p,m,g,y,O))}return I}(s[t],t,a,o,n.allowEmptyArrays,n.strictNullHandling,n.skipNulls,n.encodeDotInKeys,n.encode?n.encoder:null,n.filter,n.sort,n.allowDots,n.serializeDate,n.format,n.formatter,n.encodeValuesOnly,n.charset,l))}let c=i.join(n.delimiter),u=!0===n.addQueryPrefix?"?":"";return n.charsetSentinel&&("iso-8859-1"===n.charset?u+="utf8=%26%2310003%3B&":u+="utf8=%E2%9C%93&"),c.length>0?u+c:""}(e,{arrayFormat:"brackets"})}getUserAgent(){return`${this.constructor.name}/JS ${P}`}defaultIdempotencyKey(){return`stainless-node-retry-${c()}`}makeStatusError(e,t,r,s){return d.generate(e,t,r,s)}buildURL(e,t,r){let s=!l(this,rf,"m",rm).call(this)&&r||this.baseURL,n=new URL($.test(e)?e:s+(s.endsWith("/")&&e.startsWith("/")?e.slice(1):e)),i=this.defaultQuery();return!function(e){if(!e)return!0;for(let t in e)return!1;return!0}(i)&&(t={...i,...t}),"object"==typeof t&&t&&!Array.isArray(t)&&(n.search=this.stringifyQuery(t)),n.toString()}async prepareOptions(e){}async prepareRequest(e,{url:t,options:r}){}get(e,t){return this.methodRequest("get",e,t)}post(e,t){return this.methodRequest("post",e,t)}patch(e,t){return this.methodRequest("patch",e,t)}put(e,t){return this.methodRequest("put",e,t)}delete(e,t){return this.methodRequest("delete",e,t)}methodRequest(e,t,r){return this.request(Promise.resolve(r).then(r=>({method:e,path:t,...r})))}request(e,t=null){return new ep(this,this.makeRequest(e,t,void 0))}async makeRequest(e,t,r){let s=await e,n=s.maxRetries??this.maxRetries;null==t&&(t=n),await this.prepareOptions(s);let{req:i,url:a,timeout:o}=await this.buildRequest(s,{retryCount:n-t});await this.prepareRequest(i,{url:a,options:s});let l="log_"+(0x1000000*Math.random()|0).toString(16).padStart(6,"0"),c=void 0===r?"":`, retryOf: ${r}`,f=Date.now();if(ea(this).debug(`[${l}] sending request`,eo({retryOfRequestLogID:r,method:s.method,url:a,options:s,headers:i.headers})),s.signal?.aborted)throw new p;let d=new AbortController,y=await this.fetchWithTimeout(a,i,o,d).catch(h),w=Date.now();if(y instanceof Error){let e=`retrying, ${t} attempts remaining`;if(s.signal?.aborted)throw new p;let n=u(y)||/timed? ?out/i.test(String(y)+("cause"in y?String(y.cause):""));if(t)return ea(this).info(`[${l}] connection ${n?"timed out":"failed"} - ${e}`),ea(this).debug(`[${l}] connection ${n?"timed out":"failed"} (${e})`,eo({retryOfRequestLogID:r,url:a,durationMs:w-f,message:y.message})),this.retryRequest(s,t,r??l);if(ea(this).info(`[${l}] connection ${n?"timed out":"failed"} - error; no more retries left`),ea(this).debug(`[${l}] connection ${n?"timed out":"failed"} (error; no more retries left)`,eo({retryOfRequestLogID:r,url:a,durationMs:w-f,message:y.message})),n)throw new g;throw new m({cause:y})}let b=[...y.headers.entries()].filter(([e])=>"x-request-id"===e).map(([e,t])=>", "+e+": "+JSON.stringify(t)).join(""),_=`[${l}${c}${b}] ${i.method} ${a} ${y.ok?"succeeded":"failed"} with status ${y.status} in ${w-f}ms`;if(!y.ok){let e=await this.shouldRetry(y);if(t&&e){let e=`retrying, ${t} attempts remaining`;return await j(y.body),ea(this).info(`${_} - ${e}`),ea(this).debug(`[${l}] response error (${e})`,eo({retryOfRequestLogID:r,url:y.url,status:y.status,headers:y.headers,durationMs:w-f})),this.retryRequest(s,t,r??l,y.headers)}let n=e?"error; no more retries left":"error; not retryable";ea(this).info(`${_} - ${n}`);let i=await y.text().catch(e=>h(e).message),a=(e=>{try{return JSON.parse(e)}catch(e){return}})(i),o=a?void 0:i;throw ea(this).debug(`[${l}] response error (${n})`,eo({retryOfRequestLogID:r,url:y.url,status:y.status,headers:y.headers,message:o,durationMs:Date.now()-f})),this.makeStatusError(y.status,a,o,y.headers)}return ea(this).info(_),ea(this).debug(`[${l}] response start`,eo({retryOfRequestLogID:r,url:y.url,status:y.status,headers:y.headers,durationMs:w-f})),{response:y,options:s,controller:d,requestLogID:l,retryOfRequestLogID:r,startTime:f}}getAPIList(e,t,r){return this.requestAPIList(t,{method:"get",path:e,...r})}requestAPIList(e,t){return new eg(this,this.makeRequest(t,null,void 0),e)}async fetchWithTimeout(e,t,r,s){let{signal:n,method:i,...a}=t||{};n&&n.addEventListener("abort",()=>s.abort());let o=setTimeout(()=>s.abort(),r),l=globalThis.ReadableStream&&a.body instanceof globalThis.ReadableStream||"object"==typeof a.body&&null!==a.body&&Symbol.asyncIterator in a.body,c={signal:s.signal,...l?{duplex:"half"}:{},method:"GET",...a};i&&(c.method=i.toUpperCase());try{return await this.fetch.call(void 0,e,c)}finally{clearTimeout(o)}}async shouldRetry(e){let t=e.headers.get("x-should-retry");return"true"===t||"false"!==t&&(408===e.status||409===e.status||429===e.status||!!(e.status>=500))}async retryRequest(e,t,r,s){let n,i=s?.get("retry-after-ms");if(i){let e=parseFloat(i);Number.isNaN(e)||(n=e)}let a=s?.get("retry-after");if(a&&!n){let e=parseFloat(a);n=Number.isNaN(e)?Date.parse(a)-Date.now():1e3*e}if(!(n&&0<=n&&n<6e4)){let r=e.maxRetries??this.maxRetries;n=this.calculateDefaultRetryTimeoutMillis(t,r)}return await C(n),this.makeRequest(e,t-1,r)}calculateDefaultRetryTimeoutMillis(e,t){return Math.min(.5*Math.pow(2,t-e),8)*(1-.25*Math.random())*1e3}async buildRequest(e,{retryCount:t=0}={}){let r={...e},{method:s,path:n,query:i,defaultBaseURL:a}=r,o=this.buildURL(n,i,a);"timeout"in r&&((e,t)=>{if("number"!=typeof t||!Number.isInteger(t))throw new f(`${e} must be an integer`);if(t<0)throw new f(`${e} must be a positive integer`)})("timeout",r.timeout),r.timeout=r.timeout??this.timeout;let{bodyHeaders:l,body:c}=this.buildBody({options:r}),u=await this.buildHeaders({options:e,method:s,bodyHeaders:l,retryCount:t});return{req:{method:s,headers:u,...r.signal&&{signal:r.signal},...globalThis.ReadableStream&&c instanceof globalThis.ReadableStream&&{duplex:"half"},...c&&{body:c},...this.fetchOptions??{},...r.fetchOptions??{}},url:o,timeout:r.timeout}}async buildHeaders({options:e,method:t,bodyHeaders:r,retryCount:n}){let i={};this.idempotencyHeader&&"get"!==t&&(e.idempotencyKey||(e.idempotencyKey=this.defaultIdempotencyKey()),i[this.idempotencyHeader]=e.idempotencyKey);let a=e2([i,{Accept:"application/json","User-Agent":this.getUserAgent(),"X-Stainless-Retry-Count":String(n),...e.timeout?{"X-Stainless-Timeout":String(Math.trunc(e.timeout/1e3))}:{},...s??(s=(()=>{let e="undefined"!=typeof Deno&&null!=Deno.build?"deno":"undefined"!=typeof EdgeRuntime?"edge":"[object process]"===Object.prototype.toString.call(void 0!==globalThis.process?globalThis.process:0)?"node":"unknown";if("deno"===e)return{"X-Stainless-Lang":"js","X-Stainless-Package-Version":P,"X-Stainless-OS":N(Deno.build.os),"X-Stainless-Arch":B(Deno.build.arch),"X-Stainless-Runtime":"deno","X-Stainless-Runtime-Version":"string"==typeof Deno.version?Deno.version:Deno.version?.deno??"unknown"};if("undefined"!=typeof EdgeRuntime)return{"X-Stainless-Lang":"js","X-Stainless-Package-Version":P,"X-Stainless-OS":"Unknown","X-Stainless-Arch":`other:${EdgeRuntime}`,"X-Stainless-Runtime":"edge","X-Stainless-Runtime-Version":globalThis.process.version};if("node"===e)return{"X-Stainless-Lang":"js","X-Stainless-Package-Version":P,"X-Stainless-OS":N(globalThis.process.platform??"unknown"),"X-Stainless-Arch":B(globalThis.process.arch??"unknown"),"X-Stainless-Runtime":"node","X-Stainless-Runtime-Version":globalThis.process.version??"unknown"};let t=function(){if("undefined"==typeof navigator||!navigator)return null;for(let{key:e,pattern:t}of[{key:"edge",pattern:/Edge(?:\W+(\d+)\.(\d+)(?:\.(\d+))?)?/},{key:"ie",pattern:/MSIE(?:\W+(\d+)\.(\d+)(?:\.(\d+))?)?/},{key:"ie",pattern:/Trident(?:.*rv\:(\d+)\.(\d+)(?:\.(\d+))?)?/},{key:"chrome",pattern:/Chrome(?:\W+(\d+)\.(\d+)(?:\.(\d+))?)?/},{key:"firefox",pattern:/Firefox(?:\W+(\d+)\.(\d+)(?:\.(\d+))?)?/},{key:"safari",pattern:/(?:Version\W+(\d+)\.(\d+)(?:\.(\d+))?)?(?:\W+Mobile\S*)?\W+Safari/}]){let r=t.exec(navigator.userAgent);if(r){let t=r[1]||0,s=r[2]||0,n=r[3]||0;return{browser:e,version:`${t}.${s}.${n}`}}}return null}();return t?{"X-Stainless-Lang":"js","X-Stainless-Package-Version":P,"X-Stainless-OS":"Unknown","X-Stainless-Arch":"unknown","X-Stainless-Runtime":`browser:${t.browser}`,"X-Stainless-Runtime-Version":t.version}:{"X-Stainless-Lang":"js","X-Stainless-Package-Version":P,"X-Stainless-OS":"Unknown","X-Stainless-Arch":"unknown","X-Stainless-Runtime":"unknown","X-Stainless-Runtime-Version":"unknown"}})()),"OpenAI-Organization":this.organization,"OpenAI-Project":this.project},await this.authHeaders(e),this._options.defaultHeaders,r,e.headers]);return this.validateHeaders(a),a.values}buildBody({options:{body:e,headers:t}}){if(!e)return{bodyHeaders:void 0,body:void 0};let r=e2([t]);return ArrayBuffer.isView(e)||e instanceof ArrayBuffer||e instanceof DataView||"string"==typeof e&&r.values.has("content-type")||e instanceof Blob||e instanceof FormData||e instanceof URLSearchParams||globalThis.ReadableStream&&e instanceof globalThis.ReadableStream?{bodyHeaders:void 0,body:e}:"object"==typeof e&&(Symbol.asyncIterator in e||Symbol.iterator in e&&"next"in e&&"function"==typeof e.next)?{bodyHeaders:void 0,body:M(e)}:l(this,rp,"f").call(this,{body:e,headers:r})}}rd=r8,rp=new WeakMap,rf=new WeakSet,rm=function(){return"https://api.openai.com/v1"!==this.baseURL},r8.OpenAI=rd,r8.DEFAULT_TIMEOUT=6e5,r8.OpenAIError=f,r8.APIError=d,r8.APIConnectionError=m,r8.APIConnectionTimeoutError=g,r8.APIUserAbortError=p,r8.NotFoundError=_,r8.ConflictError=v,r8.RateLimitError=A,r8.BadRequestError=y,r8.AuthenticationError=w,r8.InternalServerError=S,r8.PermissionDeniedError=b,r8.UnprocessableEntityError=x,r8.InvalidWebhookSignatureError=O,r8.toFile=ek,r8.Completions=rx,r8.Chat=e0,r8.Embeddings=rI,r8.Files=rk,r8.Images=rW,r8.Audio=e3,r8.Moderations=rq,r8.Models=rF,r8.FineTuning=rU,r8.Graders=rD,r8.VectorStores=r0,r8.Webhooks=r2,r8.Beta=rv,r8.Batches=e4,r8.Uploads=rY,r8.Responses=rV,r8.Evals=rR,r8.Containers=rE,r(9509)}}]);