if(!self.define){let e,s={};const a=(a,c)=>(a=new URL(a+".js",c).href,s[a]||new Promise(s=>{if("document"in self){const e=document.createElement("script");e.src=a,e.onload=s,document.head.appendChild(e)}else e=a,importScripts(a),s()}).then(()=>{let e=s[a];if(!e)throw new Error(`Module ${a} didn’t register its module`);return e}));self.define=(c,i)=>{const f=e||("document"in self?document.currentScript.src:"")||location.href;if(s[f])return;let n={};const t=e=>a(e,f),r={module:{uri:f},exports:n,require:t};s[f]=Promise.all(c.map(e=>r[e]||t(e))).then(e=>(i(...e),n))}}define(["./workbox-93867f5d"],function(e){"use strict";importScripts(),self.skipWaiting(),e.clientsClaim(),e.precacheAndRoute([{url:"/_next/app-build-manifest.json",revision:"74f5f44666e71fc41f35d82f7d391b12"},{url:"/_next/static/ItftDSk3oHPTR61bjPR35/_buildManifest.js",revision:"17728c7bb8fd7f39f91acc4e70b4b63d"},{url:"/_next/static/ItftDSk3oHPTR61bjPR35/_ssgManifest.js",revision:"b6652df95db52feb4daf4eca35380933"},{url:"/_next/static/chunks/341.716d46e6e5cb6bdc.js",revision:"716d46e6e5cb6bdc"},{url:"/_next/static/chunks/472.a3826d29d6854395.js",revision:"a3826d29d6854395"},{url:"/_next/static/chunks/4bd1b696-cf72ae8a39fa05aa.js",revision:"cf72ae8a39fa05aa"},{url:"/_next/static/chunks/617-5b71743baa007a80.js",revision:"5b71743baa007a80"},{url:"/_next/static/chunks/964-02efbd2195ef91bd.js",revision:"02efbd2195ef91bd"},{url:"/_next/static/chunks/app/_not-found/page-6e9992f31ece25f2.js",revision:"6e9992f31ece25f2"},{url:"/_next/static/chunks/app/layout-ceb9eb410b1aa6ec.js",revision:"ceb9eb410b1aa6ec"},{url:"/_next/static/chunks/app/page-3376ff04ad72bc0a.js",revision:"3376ff04ad72bc0a"},{url:"/_next/static/chunks/framework-7c95b8e5103c9e90.js",revision:"7c95b8e5103c9e90"},{url:"/_next/static/chunks/main-6ee7f5ee57d5d8ca.js",revision:"6ee7f5ee57d5d8ca"},{url:"/_next/static/chunks/main-app-5e37efe2210c373e.js",revision:"5e37efe2210c373e"},{url:"/_next/static/chunks/pages/_app-663ec5428c344dae.js",revision:"663ec5428c344dae"},{url:"/_next/static/chunks/pages/_error-544778206352ce59.js",revision:"544778206352ce59"},{url:"/_next/static/chunks/polyfills-42372ed130431b0a.js",revision:"846118c33b2c0e922d7b3a7676f81f6f"},{url:"/_next/static/chunks/webpack-f0934ce8f449c9d7.js",revision:"f0934ce8f449c9d7"},{url:"/_next/static/css/7b29629c25fd4e6b.css",revision:"7b29629c25fd4e6b"},{url:"/_next/static/media/569ce4b8f30dc480-s.p.woff2",revision:"ef6cefb32024deac234e82f932a95cbd"},{url:"/_next/static/media/747892c23ea88013-s.woff2",revision:"a0761690ccf4441ace5cec893b82d4ab"},{url:"/_next/static/media/8d697b304b401681-s.woff2",revision:"cc728f6c0adb04da0dfcb0fc436a8ae5"},{url:"/_next/static/media/93f479601ee12b01-s.p.woff2",revision:"da83d5f06d825c5ae65b7cca706cb312"},{url:"/_next/static/media/9610d9e46709d722-s.woff2",revision:"7b7c0ef93df188a852344fc272fc096b"},{url:"/_next/static/media/ba015fad6dcf6784-s.woff2",revision:"8ea4f719af3312a055caf09f34c89a77"},{url:"/audio-processor.js",revision:"2b0aca2589f610f481cd493f2add2ba1"},{url:"/file.svg",revision:"d09f95206c3fa0bb9bd9fefabfd0ea71"},{url:"/globe.svg",revision:"2aaafa6a49b6563925fe440891e32717"},{url:"/icon-192x192.png",revision:"9f45b4bba75fcb7532e03516fd70f4c5"},{url:"/icon-512x512.png",revision:"9f45b4bba75fcb7532e03516fd70f4c5"},{url:"/icon.svg",revision:"9f45b4bba75fcb7532e03516fd70f4c5"},{url:"/manifest.json",revision:"f1a4db6b9223bb3b97e5ff9176eb2e9d"},{url:"/next.svg",revision:"8e061864f388b47f33a1c3780831193e"},{url:"/tts-worker.js",revision:"8757becfa6a36e690d6fd1fb0bf74113"},{url:"/vercel.svg",revision:"c0af2f507b369b085b35ef4bbe3bcf1e"},{url:"/whisper-worker.js",revision:"f6ec55d15daa9afab288ee38a71e5d86"},{url:"/window.svg",revision:"a2760511c65806022ad20adf74370ff3"}],{ignoreURLParametersMatching:[]}),e.cleanupOutdatedCaches(),e.registerRoute("/",new e.NetworkFirst({cacheName:"start-url",plugins:[{cacheWillUpdate:async({request:e,response:s,event:a,state:c})=>s&&"opaqueredirect"===s.type?new Response(s.body,{status:200,statusText:"OK",headers:s.headers}):s}]}),"GET"),e.registerRoute(/^https:\/\/api\.openai\.com\/.*/i,new e.NetworkOnly,"GET"),e.registerRoute(/\.(?:wasm|bin|onnx)$/i,new e.CacheFirst({cacheName:"ml-models",plugins:[new e.ExpirationPlugin({maxEntries:10,maxAgeSeconds:2592e3})]}),"GET")});
