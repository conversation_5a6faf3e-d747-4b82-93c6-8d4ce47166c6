{"version": 3, "sources": ["../../../src/client/portal/index.tsx"], "names": ["Portal", "children", "type", "portalNode", "setPortalNode", "useState", "useEffect", "element", "document", "createElement", "body", "append<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "createPortal"], "mappings": ";;;;+BAQaA;;;eAAAA;;;uBARuB;0BACP;AAOtB,MAAMA,SAAS;QAAC,EAAEC,QAAQ,EAAEC,IAAI,EAAe;IACpD,MAAM,CAACC,YAAYC,cAAc,GAAGC,IAAAA,eAAQ,EAAqB;IAEjEC,IAAAA,gBAAS,EAAC;QACR,MAAMC,UAAUC,SAASC,aAAa,CAACP;QACvCM,SAASE,IAAI,CAACC,WAAW,CAACJ;QAC1BH,cAAcG;QACd,OAAO;YACLC,SAASE,IAAI,CAACE,WAAW,CAACL;QAC5B;IACF,GAAG;QAACL;KAAK;IAET,OAAOC,2BAAaU,IAAAA,sBAAY,EAACZ,UAAUE,cAAc;AAC3D"}