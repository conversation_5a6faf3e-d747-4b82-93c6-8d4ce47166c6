{"version": 3, "sources": [], "sections": [{"offset": {"line": 13, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Web_dev/coding_jr/voice-assistant/src/lib/audioManager.ts"], "sourcesContent": ["export interface AudioChunk {\n  data: Float32Array;\n  timestamp: number;\n  sampleRate: number;\n}\n\nexport interface AudioManagerEvents {\n  onAudioChunk: (chunk: AudioChunk) => void;\n  onSpeechStart: () => void;\n  onSpeechEnd: () => void;\n  onError: (error: Error) => void;\n  onVolumeChange: (volume: number) => void;\n}\n\nexport class AudioManager {\n  private mediaStream: MediaStream | null = null;\n  private audioContext: AudioContext | null = null;\n  private analyser: AnalyserNode | null = null;\n  private processor: ScriptProcessorNode | null = null;\n  private workletNode: AudioWorkletNode | null = null;\n  private isRecording = false;\n  private events: Partial<AudioManagerEvents> = {};\n  \n  // Voice Activity Detection\n  private vadThreshold = 0.01;\n  private vadSilenceFrames = 0;\n  private vadSilenceThreshold = 30; // frames of silence before speech end\n  private vadSpeechFrames = 0;\n  private vadSpeechThreshold = 5; // frames of speech before speech start\n  private isSpeaking = false;\n  \n  // Audio buffer for streaming\n  private audioBuffer: Float32Array[] = [];\n  private bufferSize = 4096;\n\n  constructor(events: Partial<AudioManagerEvents> = {}) {\n    this.events = events;\n  }\n\n  async initialize(): Promise<void> {\n    try {\n      // Request microphone access\n      this.mediaStream = await navigator.mediaDevices.getUserMedia({\n        audio: {\n          sampleRate: 16000,\n          channelCount: 1,\n          echoCancellation: true,\n          noiseSuppression: true,\n          autoGainControl: true,\n        },\n      });\n\n      // Create audio context\n      this.audioContext = new AudioContext({ sampleRate: 16000 });\n      \n      // Create analyser for volume and VAD\n      this.analyser = this.audioContext.createAnalyser();\n      this.analyser.fftSize = 2048;\n      this.analyser.smoothingTimeConstant = 0.8;\n\n      // Create source from media stream\n      const source = this.audioContext.createMediaStreamSource(this.mediaStream);\n      source.connect(this.analyser);\n\n      // Try to use AudioWorklet for better performance\n      try {\n        await this.audioContext.audioWorklet.addModule('/audio-processor.js');\n        this.workletNode = new AudioWorkletNode(this.audioContext, 'audio-processor');\n        \n        this.workletNode.port.onmessage = (event) => {\n          const { audioData, timestamp } = event.data;\n          this.handleAudioData(audioData, timestamp);\n        };\n        \n        source.connect(this.workletNode);\n      } catch {\n        console.warn('AudioWorklet not available, falling back to ScriptProcessor');\n        \n        // Fallback to ScriptProcessor\n        this.processor = this.audioContext.createScriptProcessor(this.bufferSize, 1, 1);\n        this.processor.onaudioprocess = (event) => {\n          const inputBuffer = event.inputBuffer;\n          const audioData = inputBuffer.getChannelData(0);\n          this.handleAudioData(new Float32Array(audioData), this.audioContext!.currentTime);\n        };\n        \n        source.connect(this.processor);\n        this.processor.connect(this.audioContext.destination);\n      }\n\n      console.log('Audio manager initialized successfully');\n    } catch (error) {\n      const audioError = new Error(`Failed to initialize audio: ${error}`);\n      this.events.onError?.(audioError);\n      throw audioError;\n    }\n  }\n\n  private handleAudioData(audioData: Float32Array, timestamp: number): void {\n    if (!this.isRecording) return;\n\n    // Calculate RMS for volume and VAD\n    const rms = this.calculateRMS(audioData);\n    this.events.onVolumeChange?.(rms);\n\n    // Voice Activity Detection\n    this.performVAD(rms);\n\n    // Buffer audio data\n    this.audioBuffer.push(new Float32Array(audioData));\n\n    // Send audio chunk\n    const chunk: AudioChunk = {\n      data: new Float32Array(audioData),\n      timestamp,\n      sampleRate: this.audioContext?.sampleRate || 16000,\n    };\n\n    this.events.onAudioChunk?.(chunk);\n\n    // Send buffered audio when we have enough\n    if (this.audioBuffer.length >= 10) { // ~0.25 seconds at 4096 buffer size\n      this.sendBufferedAudio();\n    }\n  }\n\n  private calculateRMS(audioData: Float32Array): number {\n    let sum = 0;\n    for (let i = 0; i < audioData.length; i++) {\n      sum += audioData[i] * audioData[i];\n    }\n    return Math.sqrt(sum / audioData.length);\n  }\n\n  private performVAD(rms: number): void {\n    const isSpeechFrame = rms > this.vadThreshold;\n\n    if (isSpeechFrame) {\n      this.vadSpeechFrames++;\n      this.vadSilenceFrames = 0;\n\n      if (!this.isSpeaking && this.vadSpeechFrames >= this.vadSpeechThreshold) {\n        this.isSpeaking = true;\n        this.events.onSpeechStart?.();\n      }\n    } else {\n      this.vadSilenceFrames++;\n      this.vadSpeechFrames = 0;\n\n      if (this.isSpeaking && this.vadSilenceFrames >= this.vadSilenceThreshold) {\n        this.isSpeaking = false;\n        this.events.onSpeechEnd?.();\n        this.sendBufferedAudio(); // Send remaining audio\n      }\n    }\n  }\n\n  private sendBufferedAudio(): void {\n    if (this.audioBuffer.length === 0) return;\n\n    // Concatenate all buffered audio\n    const totalLength = this.audioBuffer.reduce((sum, buffer) => sum + buffer.length, 0);\n    const concatenated = new Float32Array(totalLength);\n    \n    let offset = 0;\n    for (const buffer of this.audioBuffer) {\n      concatenated.set(buffer, offset);\n      offset += buffer.length;\n    }\n\n    // Clear buffer\n    this.audioBuffer = [];\n\n    // Send as chunk\n    const chunk: AudioChunk = {\n      data: concatenated,\n      timestamp: this.audioContext?.currentTime || Date.now(),\n      sampleRate: this.audioContext?.sampleRate || 16000,\n    };\n\n    this.events.onAudioChunk?.(chunk);\n  }\n\n  startRecording(): void {\n    if (!this.audioContext) {\n      throw new Error('Audio manager not initialized');\n    }\n\n    if (this.audioContext.state === 'suspended') {\n      this.audioContext.resume();\n    }\n\n    this.isRecording = true;\n    this.audioBuffer = [];\n    console.log('Recording started');\n  }\n\n  stopRecording(): void {\n    this.isRecording = false;\n    this.sendBufferedAudio(); // Send any remaining audio\n    console.log('Recording stopped');\n  }\n\n  setVADThreshold(threshold: number): void {\n    this.vadThreshold = threshold;\n  }\n\n  getVADThreshold(): number {\n    return this.vadThreshold;\n  }\n\n  isCurrentlyRecording(): boolean {\n    return this.isRecording;\n  }\n\n  isCurrentlySpeaking(): boolean {\n    return this.isSpeaking;\n  }\n\n  async cleanup(): Promise<void> {\n    this.stopRecording();\n\n    if (this.processor) {\n      this.processor.disconnect();\n      this.processor = null;\n    }\n\n    if (this.workletNode) {\n      this.workletNode.disconnect();\n      this.workletNode = null;\n    }\n\n    if (this.analyser) {\n      this.analyser.disconnect();\n      this.analyser = null;\n    }\n\n    if (this.audioContext) {\n      await this.audioContext.close();\n      this.audioContext = null;\n    }\n\n    if (this.mediaStream) {\n      this.mediaStream.getTracks().forEach(track => track.stop());\n      this.mediaStream = null;\n    }\n\n    console.log('Audio manager cleaned up');\n  }\n}\n"], "names": [], "mappings": ";;;AAcO,MAAM;IACH,cAAkC,KAAK;IACvC,eAAoC,KAAK;IACzC,WAAgC,KAAK;IACrC,YAAwC,KAAK;IAC7C,cAAuC,KAAK;IAC5C,cAAc,MAAM;IACpB,SAAsC,CAAC,EAAE;IAEjD,2BAA2B;IACnB,eAAe,KAAK;IACpB,mBAAmB,EAAE;IACrB,sBAAsB,GAAG;IACzB,kBAAkB,EAAE;IACpB,qBAAqB,EAAE;IACvB,aAAa,MAAM;IAE3B,6BAA6B;IACrB,cAA8B,EAAE,CAAC;IACjC,aAAa,KAAK;IAE1B,YAAY,SAAsC,CAAC,CAAC,CAAE;QACpD,IAAI,CAAC,MAAM,GAAG;IAChB;IAEA,MAAM,aAA4B;QAChC,IAAI;YACF,4BAA4B;YAC5B,IAAI,CAAC,WAAW,GAAG,MAAM,UAAU,YAAY,CAAC,YAAY,CAAC;gBAC3D,OAAO;oBACL,YAAY;oBACZ,cAAc;oBACd,kBAAkB;oBAClB,kBAAkB;oBAClB,iBAAiB;gBACnB;YACF;YAEA,uBAAuB;YACvB,IAAI,CAAC,YAAY,GAAG,IAAI,aAAa;gBAAE,YAAY;YAAM;YAEzD,qCAAqC;YACrC,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,YAAY,CAAC,cAAc;YAChD,IAAI,CAAC,QAAQ,CAAC,OAAO,GAAG;YACxB,IAAI,CAAC,QAAQ,CAAC,qBAAqB,GAAG;YAEtC,kCAAkC;YAClC,MAAM,SAAS,IAAI,CAAC,YAAY,CAAC,uBAAuB,CAAC,IAAI,CAAC,WAAW;YACzE,OAAO,OAAO,CAAC,IAAI,CAAC,QAAQ;YAE5B,iDAAiD;YACjD,IAAI;gBACF,MAAM,IAAI,CAAC,YAAY,CAAC,YAAY,CAAC,SAAS,CAAC;gBAC/C,IAAI,CAAC,WAAW,GAAG,IAAI,iBAAiB,IAAI,CAAC,YAAY,EAAE;gBAE3D,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,SAAS,GAAG,CAAC;oBACjC,MAAM,EAAE,SAAS,EAAE,SAAS,EAAE,GAAG,MAAM,IAAI;oBAC3C,IAAI,CAAC,eAAe,CAAC,WAAW;gBAClC;gBAEA,OAAO,OAAO,CAAC,IAAI,CAAC,WAAW;YACjC,EAAE,OAAM;gBACN,QAAQ,IAAI,CAAC;gBAEb,8BAA8B;gBAC9B,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,YAAY,CAAC,qBAAqB,CAAC,IAAI,CAAC,UAAU,EAAE,GAAG;gBAC7E,IAAI,CAAC,SAAS,CAAC,cAAc,GAAG,CAAC;oBAC/B,MAAM,cAAc,MAAM,WAAW;oBACrC,MAAM,YAAY,YAAY,cAAc,CAAC;oBAC7C,IAAI,CAAC,eAAe,CAAC,IAAI,aAAa,YAAY,IAAI,CAAC,YAAY,CAAE,WAAW;gBAClF;gBAEA,OAAO,OAAO,CAAC,IAAI,CAAC,SAAS;gBAC7B,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,IAAI,CAAC,YAAY,CAAC,WAAW;YACtD;YAEA,QAAQ,GAAG,CAAC;QACd,EAAE,OAAO,OAAO;YACd,MAAM,aAAa,IAAI,MAAM,CAAC,4BAA4B,EAAE,OAAO;YACnE,IAAI,CAAC,MAAM,CAAC,OAAO,GAAG;YACtB,MAAM;QACR;IACF;IAEQ,gBAAgB,SAAuB,EAAE,SAAiB,EAAQ;QACxE,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE;QAEvB,mCAAmC;QACnC,MAAM,MAAM,IAAI,CAAC,YAAY,CAAC;QAC9B,IAAI,CAAC,MAAM,CAAC,cAAc,GAAG;QAE7B,2BAA2B;QAC3B,IAAI,CAAC,UAAU,CAAC;QAEhB,oBAAoB;QACpB,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,IAAI,aAAa;QAEvC,mBAAmB;QACnB,MAAM,QAAoB;YACxB,MAAM,IAAI,aAAa;YACvB;YACA,YAAY,IAAI,CAAC,YAAY,EAAE,cAAc;QAC/C;QAEA,IAAI,CAAC,MAAM,CAAC,YAAY,GAAG;QAE3B,0CAA0C;QAC1C,IAAI,IAAI,CAAC,WAAW,CAAC,MAAM,IAAI,IAAI;YACjC,IAAI,CAAC,iBAAiB;QACxB;IACF;IAEQ,aAAa,SAAuB,EAAU;QACpD,IAAI,MAAM;QACV,IAAK,IAAI,IAAI,GAAG,IAAI,UAAU,MAAM,EAAE,IAAK;YACzC,OAAO,SAAS,CAAC,EAAE,GAAG,SAAS,CAAC,EAAE;QACpC;QACA,OAAO,KAAK,IAAI,CAAC,MAAM,UAAU,MAAM;IACzC;IAEQ,WAAW,GAAW,EAAQ;QACpC,MAAM,gBAAgB,MAAM,IAAI,CAAC,YAAY;QAE7C,IAAI,eAAe;YACjB,IAAI,CAAC,eAAe;YACpB,IAAI,CAAC,gBAAgB,GAAG;YAExB,IAAI,CAAC,IAAI,CAAC,UAAU,IAAI,IAAI,CAAC,eAAe,IAAI,IAAI,CAAC,kBAAkB,EAAE;gBACvE,IAAI,CAAC,UAAU,GAAG;gBAClB,IAAI,CAAC,MAAM,CAAC,aAAa;YAC3B;QACF,OAAO;YACL,IAAI,CAAC,gBAAgB;YACrB,IAAI,CAAC,eAAe,GAAG;YAEvB,IAAI,IAAI,CAAC,UAAU,IAAI,IAAI,CAAC,gBAAgB,IAAI,IAAI,CAAC,mBAAmB,EAAE;gBACxE,IAAI,CAAC,UAAU,GAAG;gBAClB,IAAI,CAAC,MAAM,CAAC,WAAW;gBACvB,IAAI,CAAC,iBAAiB,IAAI,uBAAuB;YACnD;QACF;IACF;IAEQ,oBAA0B;QAChC,IAAI,IAAI,CAAC,WAAW,CAAC,MAAM,KAAK,GAAG;QAEnC,iCAAiC;QACjC,MAAM,cAAc,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC,KAAK,SAAW,MAAM,OAAO,MAAM,EAAE;QAClF,MAAM,eAAe,IAAI,aAAa;QAEtC,IAAI,SAAS;QACb,KAAK,MAAM,UAAU,IAAI,CAAC,WAAW,CAAE;YACrC,aAAa,GAAG,CAAC,QAAQ;YACzB,UAAU,OAAO,MAAM;QACzB;QAEA,eAAe;QACf,IAAI,CAAC,WAAW,GAAG,EAAE;QAErB,gBAAgB;QAChB,MAAM,QAAoB;YACxB,MAAM;YACN,WAAW,IAAI,CAAC,YAAY,EAAE,eAAe,KAAK,GAAG;YACrD,YAAY,IAAI,CAAC,YAAY,EAAE,cAAc;QAC/C;QAEA,IAAI,CAAC,MAAM,CAAC,YAAY,GAAG;IAC7B;IAEA,iBAAuB;QACrB,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE;YACtB,MAAM,IAAI,MAAM;QAClB;QAEA,IAAI,IAAI,CAAC,YAAY,CAAC,KAAK,KAAK,aAAa;YAC3C,IAAI,CAAC,YAAY,CAAC,MAAM;QAC1B;QAEA,IAAI,CAAC,WAAW,GAAG;QACnB,IAAI,CAAC,WAAW,GAAG,EAAE;QACrB,QAAQ,GAAG,CAAC;IACd;IAEA,gBAAsB;QACpB,IAAI,CAAC,WAAW,GAAG;QACnB,IAAI,CAAC,iBAAiB,IAAI,2BAA2B;QACrD,QAAQ,GAAG,CAAC;IACd;IAEA,gBAAgB,SAAiB,EAAQ;QACvC,IAAI,CAAC,YAAY,GAAG;IACtB;IAEA,kBAA0B;QACxB,OAAO,IAAI,CAAC,YAAY;IAC1B;IAEA,uBAAgC;QAC9B,OAAO,IAAI,CAAC,WAAW;IACzB;IAEA,sBAA+B;QAC7B,OAAO,IAAI,CAAC,UAAU;IACxB;IAEA,MAAM,UAAyB;QAC7B,IAAI,CAAC,aAAa;QAElB,IAAI,IAAI,CAAC,SAAS,EAAE;YAClB,IAAI,CAAC,SAAS,CAAC,UAAU;YACzB,IAAI,CAAC,SAAS,GAAG;QACnB;QAEA,IAAI,IAAI,CAAC,WAAW,EAAE;YACpB,IAAI,CAAC,WAAW,CAAC,UAAU;YAC3B,IAAI,CAAC,WAAW,GAAG;QACrB;QAEA,IAAI,IAAI,CAAC,QAAQ,EAAE;YACjB,IAAI,CAAC,QAAQ,CAAC,UAAU;YACxB,IAAI,CAAC,QAAQ,GAAG;QAClB;QAEA,IAAI,IAAI,CAAC,YAAY,EAAE;YACrB,MAAM,IAAI,CAAC,YAAY,CAAC,KAAK;YAC7B,IAAI,CAAC,YAAY,GAAG;QACtB;QAEA,IAAI,IAAI,CAAC,WAAW,EAAE;YACpB,IAAI,CAAC,WAAW,CAAC,SAAS,GAAG,OAAO,CAAC,CAAA,QAAS,MAAM,IAAI;YACxD,IAAI,CAAC,WAAW,GAAG;QACrB;QAEA,QAAQ,GAAG,CAAC;IACd;AACF", "debugId": null}}, {"offset": {"line": 212, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Web_dev/coding_jr/voice-assistant/src/hooks/useAudioRecording.ts"], "sourcesContent": ["import { useEffect, useRef, useState, useCallback } from 'react';\nimport { AudioManager, AudioChunk, AudioManagerEvents } from '@/lib/audioManager';\n\nexport interface AudioRecordingState {\n  isInitialized: boolean;\n  isRecording: boolean;\n  isSpeaking: boolean;\n  volume: number;\n  error: string | null;\n}\n\nexport interface AudioRecordingControls {\n  initialize: () => Promise<void>;\n  startRecording: () => void;\n  stopRecording: () => void;\n  cleanup: () => Promise<void>;\n  setVADThreshold: (threshold: number) => void;\n  getVADThreshold: () => number;\n}\n\nexport interface UseAudioRecordingOptions {\n  onAudioChunk?: (chunk: AudioChunk) => void;\n  onSpeechStart?: () => void;\n  onSpeechEnd?: () => void;\n  onVolumeChange?: (volume: number) => void;\n  onError?: (error: Error) => void;\n  autoStart?: boolean;\n}\n\nexport function useAudioRecording(\n  options: UseAudioRecordingOptions = {}\n): [AudioRecordingState, AudioRecordingControls] {\n  const audioManagerRef = useRef<AudioManager | null>(null);\n  const [state, setState] = useState<AudioRecordingState>({\n    isInitialized: false,\n    isRecording: false,\n    isSpeaking: false,\n    volume: 0,\n    error: null,\n  });\n\n  const updateState = useCallback((updates: Partial<AudioRecordingState>) => {\n    setState(prev => ({ ...prev, ...updates }));\n  }, []);\n\n  const initialize = useCallback(async () => {\n    try {\n      updateState({ error: null });\n\n      const events: AudioManagerEvents = {\n        onAudioChunk: (chunk) => {\n          options.onAudioChunk?.(chunk);\n        },\n        onSpeechStart: () => {\n          updateState({ isSpeaking: true });\n          options.onSpeechStart?.();\n        },\n        onSpeechEnd: () => {\n          updateState({ isSpeaking: false });\n          options.onSpeechEnd?.();\n        },\n        onError: (error) => {\n          updateState({ error: error.message });\n          console.error('Audio manager error:', error);\n          options.onError?.(error);\n        },\n        onVolumeChange: (volume) => {\n          updateState({ volume });\n          options.onVolumeChange?.(volume);\n        },\n      };\n\n      audioManagerRef.current = new AudioManager(events);\n      await audioManagerRef.current.initialize();\n      \n      updateState({ isInitialized: true });\n\n      if (options.autoStart) {\n        audioManagerRef.current.startRecording();\n        updateState({ isRecording: true, error: null });\n      }\n    } catch (error) {\n      const errorMessage = error instanceof Error ? error.message : 'Unknown audio error';\n      updateState({ error: errorMessage, isInitialized: false });\n    }\n  }, [options, updateState]);\n\n  const startRecording = useCallback(() => {\n    if (!audioManagerRef.current) {\n      updateState({ error: 'Audio manager not initialized' });\n      return;\n    }\n\n    try {\n      audioManagerRef.current.startRecording();\n      updateState({ isRecording: true, error: null });\n    } catch (error) {\n      const errorMessage = error instanceof Error ? error.message : 'Failed to start recording';\n      updateState({ error: errorMessage });\n    }\n  }, [updateState]);\n\n  const stopRecording = useCallback(() => {\n    if (!audioManagerRef.current) {\n      return;\n    }\n\n    audioManagerRef.current.stopRecording();\n    updateState({ isRecording: false, isSpeaking: false });\n  }, [updateState]);\n\n  const cleanup = useCallback(async () => {\n    if (audioManagerRef.current) {\n      await audioManagerRef.current.cleanup();\n      audioManagerRef.current = null;\n    }\n    \n    setState({\n      isInitialized: false,\n      isRecording: false,\n      isSpeaking: false,\n      volume: 0,\n      error: null,\n    });\n  }, []);\n\n  const setVADThreshold = useCallback((threshold: number) => {\n    audioManagerRef.current?.setVADThreshold(threshold);\n  }, []);\n\n  const getVADThreshold = useCallback(() => {\n    return audioManagerRef.current?.getVADThreshold() || 0.01;\n  }, []);\n\n  // Cleanup on unmount\n  useEffect(() => {\n    return () => {\n      cleanup();\n    };\n  }, [cleanup]);\n\n  // Update recording state based on audio manager\n  useEffect(() => {\n    const interval = setInterval(() => {\n      if (audioManagerRef.current) {\n        const isRecording = audioManagerRef.current.isCurrentlyRecording();\n        const isSpeaking = audioManagerRef.current.isCurrentlySpeaking();\n        \n        setState(prev => {\n          if (prev.isRecording !== isRecording || prev.isSpeaking !== isSpeaking) {\n            return { ...prev, isRecording, isSpeaking };\n          }\n          return prev;\n        });\n      }\n    }, 100);\n\n    return () => clearInterval(interval);\n  }, []);\n\n  const controls: AudioRecordingControls = {\n    initialize,\n    startRecording,\n    stopRecording,\n    cleanup,\n    setVADThreshold,\n    getVADThreshold,\n  };\n\n  return [state, controls];\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AA4BO,SAAS,kBACd,UAAoC,CAAC,CAAC;IAEtC,MAAM,kBAAkB,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAuB;IACpD,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAuB;QACtD,eAAe;QACf,aAAa;QACb,YAAY;QACZ,QAAQ;QACR,OAAO;IACT;IAEA,MAAM,cAAc,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QAC/B,SAAS,CAAA,OAAQ,CAAC;gBAAE,GAAG,IAAI;gBAAE,GAAG,OAAO;YAAC,CAAC;IAC3C,GAAG,EAAE;IAEL,MAAM,aAAa,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QAC7B,IAAI;YACF,YAAY;gBAAE,OAAO;YAAK;YAE1B,MAAM,SAA6B;gBACjC,cAAc,CAAC;oBACb,QAAQ,YAAY,GAAG;gBACzB;gBACA,eAAe;oBACb,YAAY;wBAAE,YAAY;oBAAK;oBAC/B,QAAQ,aAAa;gBACvB;gBACA,aAAa;oBACX,YAAY;wBAAE,YAAY;oBAAM;oBAChC,QAAQ,WAAW;gBACrB;gBACA,SAAS,CAAC;oBACR,YAAY;wBAAE,OAAO,MAAM,OAAO;oBAAC;oBACnC,QAAQ,KAAK,CAAC,wBAAwB;oBACtC,QAAQ,OAAO,GAAG;gBACpB;gBACA,gBAAgB,CAAC;oBACf,YAAY;wBAAE;oBAAO;oBACrB,QAAQ,cAAc,GAAG;gBAC3B;YACF;YAEA,gBAAgB,OAAO,GAAG,IAAI,0HAAA,CAAA,eAAY,CAAC;YAC3C,MAAM,gBAAgB,OAAO,CAAC,UAAU;YAExC,YAAY;gBAAE,eAAe;YAAK;YAElC,IAAI,QAAQ,SAAS,EAAE;gBACrB,gBAAgB,OAAO,CAAC,cAAc;gBACtC,YAAY;oBAAE,aAAa;oBAAM,OAAO;gBAAK;YAC/C;QACF,EAAE,OAAO,OAAO;YACd,MAAM,eAAe,iBAAiB,QAAQ,MAAM,OAAO,GAAG;YAC9D,YAAY;gBAAE,OAAO;gBAAc,eAAe;YAAM;QAC1D;IACF,GAAG;QAAC;QAAS;KAAY;IAEzB,MAAM,iBAAiB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QACjC,IAAI,CAAC,gBAAgB,OAAO,EAAE;YAC5B,YAAY;gBAAE,OAAO;YAAgC;YACrD;QACF;QAEA,IAAI;YACF,gBAAgB,OAAO,CAAC,cAAc;YACtC,YAAY;gBAAE,aAAa;gBAAM,OAAO;YAAK;QAC/C,EAAE,OAAO,OAAO;YACd,MAAM,eAAe,iBAAiB,QAAQ,MAAM,OAAO,GAAG;YAC9D,YAAY;gBAAE,OAAO;YAAa;QACpC;IACF,GAAG;QAAC;KAAY;IAEhB,MAAM,gBAAgB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QAChC,IAAI,CAAC,gBAAgB,OAAO,EAAE;YAC5B;QACF;QAEA,gBAAgB,OAAO,CAAC,aAAa;QACrC,YAAY;YAAE,aAAa;YAAO,YAAY;QAAM;IACtD,GAAG;QAAC;KAAY;IAEhB,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QAC1B,IAAI,gBAAgB,OAAO,EAAE;YAC3B,MAAM,gBAAgB,OAAO,CAAC,OAAO;YACrC,gBAAgB,OAAO,GAAG;QAC5B;QAEA,SAAS;YACP,eAAe;YACf,aAAa;YACb,YAAY;YACZ,QAAQ;YACR,OAAO;QACT;IACF,GAAG,EAAE;IAEL,MAAM,kBAAkB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QACnC,gBAAgB,OAAO,EAAE,gBAAgB;IAC3C,GAAG,EAAE;IAEL,MAAM,kBAAkB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QAClC,OAAO,gBAAgB,OAAO,EAAE,qBAAqB;IACvD,GAAG,EAAE;IAEL,qBAAqB;IACrB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,OAAO;YACL;QACF;IACF,GAAG;QAAC;KAAQ;IAEZ,gDAAgD;IAChD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,WAAW,YAAY;YAC3B,IAAI,gBAAgB,OAAO,EAAE;gBAC3B,MAAM,cAAc,gBAAgB,OAAO,CAAC,oBAAoB;gBAChE,MAAM,aAAa,gBAAgB,OAAO,CAAC,mBAAmB;gBAE9D,SAAS,CAAA;oBACP,IAAI,KAAK,WAAW,KAAK,eAAe,KAAK,UAAU,KAAK,YAAY;wBACtE,OAAO;4BAAE,GAAG,IAAI;4BAAE;4BAAa;wBAAW;oBAC5C;oBACA,OAAO;gBACT;YACF;QACF,GAAG;QAEH,OAAO,IAAM,cAAc;IAC7B,GAAG,EAAE;IAEL,MAAM,WAAmC;QACvC;QACA;QACA;QACA;QACA;QACA;IACF;IAEA,OAAO;QAAC;QAAO;KAAS;AAC1B", "debugId": null}}, {"offset": {"line": 390, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Web_dev/coding_jr/voice-assistant/src/hooks/useWhisperSTT.ts"], "sourcesContent": ["import { useEffect, useRef, useState, useCallback } from 'react';\nimport { AudioChunk } from '@/lib/audioManager';\n\nexport interface TranscriptionResult {\n  text: string;\n  chunks?: Array<{\n    text: string;\n    timestamp: [number, number];\n  }>;\n  confidence?: number;\n  processingTime: number;\n  audioLength: number;\n}\n\nexport interface WhisperSTTState {\n  isInitialized: boolean;\n  isTranscribing: boolean;\n  error: string | null;\n  lastTranscription: TranscriptionResult | null;\n  initializationProgress: number;\n}\n\nexport interface WhisperSTTControls {\n  initialize: () => Promise<void>;\n  transcribeAudio: (audioData: Float32Array, options?: TranscriptionOptions) => Promise<void>;\n  bufferAudio: (chunk: AudioChunk) => void;\n  flushBuffer: () => void;\n  reset: () => void;\n}\n\nexport interface TranscriptionOptions {\n  returnTimestamps?: boolean;\n  chunkLength?: number;\n  strideLength?: number;\n}\n\nexport interface UseWhisperSTTOptions {\n  onTranscription?: (result: TranscriptionResult) => void;\n  onError?: (error: string) => void;\n  autoInitialize?: boolean;\n}\n\nexport function useWhisperSTT(\n  options: UseWhisperSTTOptions = {}\n): [WhisperSTTState, WhisperSTTControls] {\n  const workerRef = useRef<Worker | null>(null);\n  const [state, setState] = useState<WhisperSTTState>({\n    isInitialized: false,\n    isTranscribing: false,\n    error: null,\n    lastTranscription: null,\n    initializationProgress: 0,\n  });\n\n  const updateState = useCallback((updates: Partial<WhisperSTTState>) => {\n    setState(prev => ({ ...prev, ...updates }));\n  }, []);\n\n  const initialize = useCallback(async () => {\n    try {\n      updateState({ error: null, initializationProgress: 0 });\n\n      // Create worker\n      workerRef.current = new Worker('/whisper-worker.js');\n\n      // Set up message handler\n      workerRef.current.onmessage = (event) => {\n        const { type, ...data } = event.data;\n\n        switch (type) {\n          case 'initialized':\n            if (data.success) {\n              updateState({ \n                isInitialized: true, \n                initializationProgress: 100,\n                error: null \n              });\n              console.log('Whisper STT initialized successfully');\n            } else {\n              updateState({ \n                error: data.error || 'Failed to initialize Whisper',\n                isInitialized: false,\n                initializationProgress: 0\n              });\n            }\n            break;\n\n          case 'transcription':\n            updateState({ \n              isTranscribing: false,\n              lastTranscription: data.result,\n              error: null\n            });\n            options.onTranscription?.(data.result);\n            break;\n\n          case 'error':\n            const errorMessage = data.error || 'Unknown transcription error';\n            updateState({ \n              error: errorMessage,\n              isTranscribing: false\n            });\n            options.onError?.(errorMessage);\n            break;\n\n          case 'progress':\n            updateState({ \n              initializationProgress: data.progress || 0\n            });\n            break;\n\n          default:\n            console.warn('Unknown message type from Whisper worker:', type);\n        }\n      };\n\n      workerRef.current.onerror = (error) => {\n        const errorMessage = `Worker error: ${error.message}`;\n        updateState({ \n          error: errorMessage,\n          isInitialized: false,\n          initializationProgress: 0\n        });\n        options.onError?.(errorMessage);\n      };\n\n      // Initialize the worker\n      updateState({ initializationProgress: 10 });\n      workerRef.current.postMessage({ type: 'initialize' });\n\n    } catch (error) {\n      const errorMessage = error instanceof Error ? error.message : 'Failed to create Whisper worker';\n      updateState({ \n        error: errorMessage,\n        isInitialized: false,\n        initializationProgress: 0\n      });\n      options.onError?.(errorMessage);\n    }\n  }, [options, updateState]);\n\n  const transcribeAudio = useCallback(async (\n    audioData: Float32Array, \n    transcriptionOptions: TranscriptionOptions = {}\n  ) => {\n    if (!workerRef.current || !state.isInitialized) {\n      throw new Error('Whisper STT not initialized');\n    }\n\n    updateState({ isTranscribing: true, error: null });\n\n    workerRef.current.postMessage({\n      type: 'transcribe',\n      data: {\n        audioData: Array.from(audioData), // Convert to regular array for transfer\n        options: transcriptionOptions\n      }\n    });\n  }, [state.isInitialized, updateState]);\n\n  const bufferAudio = useCallback((chunk: AudioChunk) => {\n    if (!workerRef.current || !state.isInitialized) {\n      return;\n    }\n\n    workerRef.current.postMessage({\n      type: 'buffer-audio',\n      data: {\n        data: Array.from(chunk.data),\n        timestamp: chunk.timestamp,\n        sampleRate: chunk.sampleRate\n      }\n    });\n  }, [state.isInitialized]);\n\n  const flushBuffer = useCallback(() => {\n    if (!workerRef.current || !state.isInitialized) {\n      return;\n    }\n\n    workerRef.current.postMessage({ type: 'flush-buffer' });\n  }, [state.isInitialized]);\n\n  const reset = useCallback(() => {\n    if (!workerRef.current) {\n      return;\n    }\n\n    workerRef.current.postMessage({ type: 'reset' });\n    updateState({ \n      isTranscribing: false,\n      lastTranscription: null,\n      error: null\n    });\n  }, [updateState]);\n\n  // Auto-initialize if requested\n  useEffect(() => {\n    if (options.autoInitialize) {\n      initialize();\n    }\n  }, [options.autoInitialize, initialize]);\n\n  // Cleanup on unmount\n  useEffect(() => {\n    return () => {\n      if (workerRef.current) {\n        workerRef.current.terminate();\n        workerRef.current = null;\n      }\n    };\n  }, []);\n\n  const controls: WhisperSTTControls = {\n    initialize,\n    transcribeAudio,\n    bufferAudio,\n    flushBuffer,\n    reset,\n  };\n\n  return [state, controls];\n}\n"], "names": [], "mappings": ";;;AAAA;;AA0CO,SAAS,cACd,UAAgC,CAAC,CAAC;IAElC,MAAM,YAAY,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAiB;IACxC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAmB;QAClD,eAAe;QACf,gBAAgB;QAChB,OAAO;QACP,mBAAmB;QACnB,wBAAwB;IAC1B;IAEA,MAAM,cAAc,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QAC/B,SAAS,CAAA,OAAQ,CAAC;gBAAE,GAAG,IAAI;gBAAE,GAAG,OAAO;YAAC,CAAC;IAC3C,GAAG,EAAE;IAEL,MAAM,aAAa,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QAC7B,IAAI;YACF,YAAY;gBAAE,OAAO;gBAAM,wBAAwB;YAAE;YAErD,gBAAgB;YAChB,UAAU,OAAO,GAAG,IAAI,OAAO;YAE/B,yBAAyB;YACzB,UAAU,OAAO,CAAC,SAAS,GAAG,CAAC;gBAC7B,MAAM,EAAE,IAAI,EAAE,GAAG,MAAM,GAAG,MAAM,IAAI;gBAEpC,OAAQ;oBACN,KAAK;wBACH,IAAI,KAAK,OAAO,EAAE;4BAChB,YAAY;gCACV,eAAe;gCACf,wBAAwB;gCACxB,OAAO;4BACT;4BACA,QAAQ,GAAG,CAAC;wBACd,OAAO;4BACL,YAAY;gCACV,OAAO,KAAK,KAAK,IAAI;gCACrB,eAAe;gCACf,wBAAwB;4BAC1B;wBACF;wBACA;oBAEF,KAAK;wBACH,YAAY;4BACV,gBAAgB;4BAChB,mBAAmB,KAAK,MAAM;4BAC9B,OAAO;wBACT;wBACA,QAAQ,eAAe,GAAG,KAAK,MAAM;wBACrC;oBAEF,KAAK;wBACH,MAAM,eAAe,KAAK,KAAK,IAAI;wBACnC,YAAY;4BACV,OAAO;4BACP,gBAAgB;wBAClB;wBACA,QAAQ,OAAO,GAAG;wBAClB;oBAEF,KAAK;wBACH,YAAY;4BACV,wBAAwB,KAAK,QAAQ,IAAI;wBAC3C;wBACA;oBAEF;wBACE,QAAQ,IAAI,CAAC,6CAA6C;gBAC9D;YACF;YAEA,UAAU,OAAO,CAAC,OAAO,GAAG,CAAC;gBAC3B,MAAM,eAAe,CAAC,cAAc,EAAE,MAAM,OAAO,EAAE;gBACrD,YAAY;oBACV,OAAO;oBACP,eAAe;oBACf,wBAAwB;gBAC1B;gBACA,QAAQ,OAAO,GAAG;YACpB;YAEA,wBAAwB;YACxB,YAAY;gBAAE,wBAAwB;YAAG;YACzC,UAAU,OAAO,CAAC,WAAW,CAAC;gBAAE,MAAM;YAAa;QAErD,EAAE,OAAO,OAAO;YACd,MAAM,eAAe,iBAAiB,QAAQ,MAAM,OAAO,GAAG;YAC9D,YAAY;gBACV,OAAO;gBACP,eAAe;gBACf,wBAAwB;YAC1B;YACA,QAAQ,OAAO,GAAG;QACpB;IACF,GAAG;QAAC;QAAS;KAAY;IAEzB,MAAM,kBAAkB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,OAClC,WACA,uBAA6C,CAAC,CAAC;QAE/C,IAAI,CAAC,UAAU,OAAO,IAAI,CAAC,MAAM,aAAa,EAAE;YAC9C,MAAM,IAAI,MAAM;QAClB;QAEA,YAAY;YAAE,gBAAgB;YAAM,OAAO;QAAK;QAEhD,UAAU,OAAO,CAAC,WAAW,CAAC;YAC5B,MAAM;YACN,MAAM;gBACJ,WAAW,MAAM,IAAI,CAAC;gBACtB,SAAS;YACX;QACF;IACF,GAAG;QAAC,MAAM,aAAa;QAAE;KAAY;IAErC,MAAM,cAAc,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QAC/B,IAAI,CAAC,UAAU,OAAO,IAAI,CAAC,MAAM,aAAa,EAAE;YAC9C;QACF;QAEA,UAAU,OAAO,CAAC,WAAW,CAAC;YAC5B,MAAM;YACN,MAAM;gBACJ,MAAM,MAAM,IAAI,CAAC,MAAM,IAAI;gBAC3B,WAAW,MAAM,SAAS;gBAC1B,YAAY,MAAM,UAAU;YAC9B;QACF;IACF,GAAG;QAAC,MAAM,aAAa;KAAC;IAExB,MAAM,cAAc,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QAC9B,IAAI,CAAC,UAAU,OAAO,IAAI,CAAC,MAAM,aAAa,EAAE;YAC9C;QACF;QAEA,UAAU,OAAO,CAAC,WAAW,CAAC;YAAE,MAAM;QAAe;IACvD,GAAG;QAAC,MAAM,aAAa;KAAC;IAExB,MAAM,QAAQ,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QACxB,IAAI,CAAC,UAAU,OAAO,EAAE;YACtB;QACF;QAEA,UAAU,OAAO,CAAC,WAAW,CAAC;YAAE,MAAM;QAAQ;QAC9C,YAAY;YACV,gBAAgB;YAChB,mBAAmB;YACnB,OAAO;QACT;IACF,GAAG;QAAC;KAAY;IAEhB,+BAA+B;IAC/B,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,QAAQ,cAAc,EAAE;YAC1B;QACF;IACF,GAAG;QAAC,QAAQ,cAAc;QAAE;KAAW;IAEvC,qBAAqB;IACrB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,OAAO;YACL,IAAI,UAAU,OAAO,EAAE;gBACrB,UAAU,OAAO,CAAC,SAAS;gBAC3B,UAAU,OAAO,GAAG;YACtB;QACF;IACF,GAAG,EAAE;IAEL,MAAM,WAA+B;QACnC;QACA;QACA;QACA;QACA;IACF;IAEA,OAAO;QAAC;QAAO;KAAS;AAC1B", "debugId": null}}, {"offset": {"line": 585, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Web_dev/coding_jr/voice-assistant/src/lib/openaiClient.ts"], "sourcesContent": ["import OpenAI from 'openai';\n\nexport interface ChatMessage {\n  role: 'system' | 'user' | 'assistant';\n  content: string;\n  timestamp?: number;\n}\n\nexport interface ChatCompletionOptions {\n  model?: string;\n  temperature?: number;\n  maxTokens?: number;\n  systemPrompt?: string;\n}\n\nexport interface ChatResponse {\n  message: string;\n  usage?: {\n    promptTokens: number;\n    completionTokens: number;\n    totalTokens: number;\n  };\n  processingTime: number;\n}\n\nexport class OpenAIClient {\n  private client: OpenAI;\n  private conversationHistory: ChatMessage[] = [];\n  private defaultSystemPrompt = `You are a helpful voice assistant. Keep your responses concise and conversational, as they will be spoken aloud. Aim for responses that are 1-2 sentences unless more detail is specifically requested.`;\n\n  constructor(apiKey: string) {\n    this.client = new OpenAI({\n      apiKey,\n      dangerouslyAllowBrowser: true, // Note: In production, use a backend proxy\n    });\n  }\n\n  async sendMessage(\n    userMessage: string,\n    options: ChatCompletionOptions = {}\n  ): Promise<ChatResponse> {\n    const startTime = performance.now();\n\n    try {\n      // Add user message to history\n      const userChatMessage: ChatMessage = {\n        role: 'user',\n        content: userMessage,\n        timestamp: Date.now(),\n      };\n      this.conversationHistory.push(userChatMessage);\n\n      // Prepare messages for API\n      const messages: ChatMessage[] = [];\n\n      // Add system prompt\n      const systemPrompt = options.systemPrompt || this.defaultSystemPrompt;\n      messages.push({\n        role: 'system',\n        content: systemPrompt,\n      });\n\n      // Add conversation history (keep last 10 messages to manage context length)\n      const recentHistory = this.conversationHistory.slice(-10);\n      messages.push(...recentHistory);\n\n      // Make API call\n      const completion = await this.client.chat.completions.create({\n        model: options.model || 'gpt-3.5-turbo',\n        messages: messages.map(msg => ({\n          role: msg.role,\n          content: msg.content,\n        })),\n        temperature: options.temperature || 0.7,\n        max_tokens: options.maxTokens || 150,\n        stream: false,\n      });\n\n      const endTime = performance.now();\n      const processingTime = endTime - startTime;\n\n      const assistantMessage = completion.choices[0]?.message?.content || '';\n\n      // Add assistant response to history\n      const assistantChatMessage: ChatMessage = {\n        role: 'assistant',\n        content: assistantMessage,\n        timestamp: Date.now(),\n      };\n      this.conversationHistory.push(assistantChatMessage);\n\n      const response: ChatResponse = {\n        message: assistantMessage,\n        usage: completion.usage ? {\n          promptTokens: completion.usage.prompt_tokens,\n          completionTokens: completion.usage.completion_tokens,\n          totalTokens: completion.usage.total_tokens,\n        } : undefined,\n        processingTime,\n      };\n\n      console.log(`OpenAI response received in ${processingTime.toFixed(2)}ms`);\n      return response;\n\n    } catch (error) {\n      const endTime = performance.now();\n      const processingTime = endTime - startTime;\n\n      console.error('OpenAI API error:', error);\n      \n      // Return error response\n      return {\n        message: 'Sorry, I encountered an error processing your request.',\n        processingTime,\n      };\n    }\n  }\n\n  getConversationHistory(): ChatMessage[] {\n    return [...this.conversationHistory];\n  }\n\n  clearConversationHistory(): void {\n    this.conversationHistory = [];\n  }\n\n  setSystemPrompt(prompt: string): void {\n    this.defaultSystemPrompt = prompt;\n  }\n\n  getSystemPrompt(): string {\n    return this.defaultSystemPrompt;\n  }\n\n  // Remove old messages to manage memory and context length\n  trimConversationHistory(maxMessages: number = 20): void {\n    if (this.conversationHistory.length > maxMessages) {\n      this.conversationHistory = this.conversationHistory.slice(-maxMessages);\n    }\n  }\n}\n"], "names": [], "mappings": ";;;AAAA;AAAA;;AAyBO,MAAM;IACH,OAAe;IACf,sBAAqC,EAAE,CAAC;IACxC,sBAAsB,CAAC,uMAAuM,CAAC,CAAC;IAExO,YAAY,MAAc,CAAE;QAC1B,IAAI,CAAC,MAAM,GAAG,IAAI,sKAAA,CAAA,UAAM,CAAC;YACvB;YACA,yBAAyB;QAC3B;IACF;IAEA,MAAM,YACJ,WAAmB,EACnB,UAAiC,CAAC,CAAC,EACZ;QACvB,MAAM,YAAY,YAAY,GAAG;QAEjC,IAAI;YACF,8BAA8B;YAC9B,MAAM,kBAA+B;gBACnC,MAAM;gBACN,SAAS;gBACT,WAAW,KAAK,GAAG;YACrB;YACA,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC;YAE9B,2BAA2B;YAC3B,MAAM,WAA0B,EAAE;YAElC,oBAAoB;YACpB,MAAM,eAAe,QAAQ,YAAY,IAAI,IAAI,CAAC,mBAAmB;YACrE,SAAS,IAAI,CAAC;gBACZ,MAAM;gBACN,SAAS;YACX;YAEA,4EAA4E;YAC5E,MAAM,gBAAgB,IAAI,CAAC,mBAAmB,CAAC,KAAK,CAAC,CAAC;YACtD,SAAS,IAAI,IAAI;YAEjB,gBAAgB;YAChB,MAAM,aAAa,MAAM,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC;gBAC3D,OAAO,QAAQ,KAAK,IAAI;gBACxB,UAAU,SAAS,GAAG,CAAC,CAAA,MAAO,CAAC;wBAC7B,MAAM,IAAI,IAAI;wBACd,SAAS,IAAI,OAAO;oBACtB,CAAC;gBACD,aAAa,QAAQ,WAAW,IAAI;gBACpC,YAAY,QAAQ,SAAS,IAAI;gBACjC,QAAQ;YACV;YAEA,MAAM,UAAU,YAAY,GAAG;YAC/B,MAAM,iBAAiB,UAAU;YAEjC,MAAM,mBAAmB,WAAW,OAAO,CAAC,EAAE,EAAE,SAAS,WAAW;YAEpE,oCAAoC;YACpC,MAAM,uBAAoC;gBACxC,MAAM;gBACN,SAAS;gBACT,WAAW,KAAK,GAAG;YACrB;YACA,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC;YAE9B,MAAM,WAAyB;gBAC7B,SAAS;gBACT,OAAO,WAAW,KAAK,GAAG;oBACxB,cAAc,WAAW,KAAK,CAAC,aAAa;oBAC5C,kBAAkB,WAAW,KAAK,CAAC,iBAAiB;oBACpD,aAAa,WAAW,KAAK,CAAC,YAAY;gBAC5C,IAAI;gBACJ;YACF;YAEA,QAAQ,GAAG,CAAC,CAAC,4BAA4B,EAAE,eAAe,OAAO,CAAC,GAAG,EAAE,CAAC;YACxE,OAAO;QAET,EAAE,OAAO,OAAO;YACd,MAAM,UAAU,YAAY,GAAG;YAC/B,MAAM,iBAAiB,UAAU;YAEjC,QAAQ,KAAK,CAAC,qBAAqB;YAEnC,wBAAwB;YACxB,OAAO;gBACL,SAAS;gBACT;YACF;QACF;IACF;IAEA,yBAAwC;QACtC,OAAO;eAAI,IAAI,CAAC,mBAAmB;SAAC;IACtC;IAEA,2BAAiC;QAC/B,IAAI,CAAC,mBAAmB,GAAG,EAAE;IAC/B;IAEA,gBAAgB,MAAc,EAAQ;QACpC,IAAI,CAAC,mBAAmB,GAAG;IAC7B;IAEA,kBAA0B;QACxB,OAAO,IAAI,CAAC,mBAAmB;IACjC;IAEA,0DAA0D;IAC1D,wBAAwB,cAAsB,EAAE,EAAQ;QACtD,IAAI,IAAI,CAAC,mBAAmB,CAAC,MAAM,GAAG,aAAa;YACjD,IAAI,CAAC,mBAAmB,GAAG,IAAI,CAAC,mBAAmB,CAAC,KAAK,CAAC,CAAC;QAC7D;IACF;AACF", "debugId": null}}, {"offset": {"line": 690, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Web_dev/coding_jr/voice-assistant/src/hooks/useOpenAIChat.ts"], "sourcesContent": ["import { useState, useCallback, useRef } from 'react';\nimport { OpenAIClient, ChatMessage, ChatResponse, ChatCompletionOptions } from '@/lib/openaiClient';\n\nexport interface OpenAIChatState {\n  isInitialized: boolean;\n  isProcessing: boolean;\n  error: string | null;\n  conversationHistory: ChatMessage[];\n  lastResponse: ChatResponse | null;\n}\n\nexport interface OpenAIChatControls {\n  initialize: (apiKey: string) => void;\n  sendMessage: (message: string, options?: ChatCompletionOptions) => Promise<ChatResponse | null>;\n  clearHistory: () => void;\n  setSystemPrompt: (prompt: string) => void;\n  getSystemPrompt: () => string;\n}\n\nexport interface UseOpenAIChatOptions {\n  onResponse?: (response: ChatResponse) => void;\n  onError?: (error: string) => void;\n  autoTrimHistory?: boolean;\n  maxHistoryLength?: number;\n}\n\nexport function useOpenAIChat(\n  options: UseOpenAIChatOptions = {}\n): [OpenAIChatState, OpenAIChatControls] {\n  const clientRef = useRef<OpenAIClient | null>(null);\n  const [state, setState] = useState<OpenAIChatState>({\n    isInitialized: false,\n    isProcessing: false,\n    error: null,\n    conversationHistory: [],\n    lastResponse: null,\n  });\n\n  const updateState = useCallback((updates: Partial<OpenAIChatState>) => {\n    setState(prev => ({ ...prev, ...updates }));\n  }, []);\n\n  const initialize = useCallback((apiKey: string) => {\n    try {\n      if (!apiKey || apiKey.trim() === '') {\n        throw new Error('OpenAI API key is required');\n      }\n\n      clientRef.current = new OpenAIClient(apiKey);\n      updateState({ \n        isInitialized: true, \n        error: null,\n        conversationHistory: []\n      });\n      \n      console.log('OpenAI client initialized successfully');\n    } catch (error) {\n      const errorMessage = error instanceof Error ? error.message : 'Failed to initialize OpenAI client';\n      updateState({ \n        error: errorMessage,\n        isInitialized: false\n      });\n      options.onError?.(errorMessage);\n    }\n  }, [options, updateState]);\n\n  const sendMessage = useCallback(async (\n    message: string,\n    chatOptions: ChatCompletionOptions = {}\n  ): Promise<ChatResponse | null> => {\n    if (!clientRef.current || !state.isInitialized) {\n      const error = 'OpenAI client not initialized';\n      updateState({ error });\n      options.onError?.(error);\n      return null;\n    }\n\n    if (!message || message.trim() === '') {\n      const error = 'Message cannot be empty';\n      updateState({ error });\n      options.onError?.(error);\n      return null;\n    }\n\n    try {\n      updateState({ \n        isProcessing: true, \n        error: null \n      });\n\n      const response = await clientRef.current.sendMessage(message, chatOptions);\n      \n      // Update conversation history\n      const updatedHistory = clientRef.current.getConversationHistory();\n      \n      updateState({ \n        isProcessing: false,\n        lastResponse: response,\n        conversationHistory: updatedHistory,\n        error: null\n      });\n\n      // Auto-trim history if enabled\n      if (options.autoTrimHistory && options.maxHistoryLength) {\n        clientRef.current.trimConversationHistory(options.maxHistoryLength);\n      }\n\n      options.onResponse?.(response);\n      return response;\n\n    } catch (error) {\n      const errorMessage = error instanceof Error ? error.message : 'Failed to send message';\n      updateState({ \n        error: errorMessage,\n        isProcessing: false\n      });\n      options.onError?.(errorMessage);\n      return null;\n    }\n  }, [state.isInitialized, options, updateState]);\n\n  const clearHistory = useCallback(() => {\n    if (clientRef.current) {\n      clientRef.current.clearConversationHistory();\n      updateState({ \n        conversationHistory: [],\n        lastResponse: null,\n        error: null\n      });\n    }\n  }, [updateState]);\n\n  const setSystemPrompt = useCallback((prompt: string) => {\n    if (clientRef.current) {\n      clientRef.current.setSystemPrompt(prompt);\n    }\n  }, []);\n\n  const getSystemPrompt = useCallback((): string => {\n    return clientRef.current?.getSystemPrompt() || '';\n  }, []);\n\n  const controls: OpenAIChatControls = {\n    initialize,\n    sendMessage,\n    clearHistory,\n    setSystemPrompt,\n    getSystemPrompt,\n  };\n\n  return [state, controls];\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAyBO,SAAS,cACd,UAAgC,CAAC,CAAC;IAElC,MAAM,YAAY,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAuB;IAC9C,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAmB;QAClD,eAAe;QACf,cAAc;QACd,OAAO;QACP,qBAAqB,EAAE;QACvB,cAAc;IAChB;IAEA,MAAM,cAAc,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QAC/B,SAAS,CAAA,OAAQ,CAAC;gBAAE,GAAG,IAAI;gBAAE,GAAG,OAAO;YAAC,CAAC;IAC3C,GAAG,EAAE;IAEL,MAAM,aAAa,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QAC9B,IAAI;YACF,IAAI,CAAC,UAAU,OAAO,IAAI,OAAO,IAAI;gBACnC,MAAM,IAAI,MAAM;YAClB;YAEA,UAAU,OAAO,GAAG,IAAI,0HAAA,CAAA,eAAY,CAAC;YACrC,YAAY;gBACV,eAAe;gBACf,OAAO;gBACP,qBAAqB,EAAE;YACzB;YAEA,QAAQ,GAAG,CAAC;QACd,EAAE,OAAO,OAAO;YACd,MAAM,eAAe,iBAAiB,QAAQ,MAAM,OAAO,GAAG;YAC9D,YAAY;gBACV,OAAO;gBACP,eAAe;YACjB;YACA,QAAQ,OAAO,GAAG;QACpB;IACF,GAAG;QAAC;QAAS;KAAY;IAEzB,MAAM,cAAc,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,OAC9B,SACA,cAAqC,CAAC,CAAC;QAEvC,IAAI,CAAC,UAAU,OAAO,IAAI,CAAC,MAAM,aAAa,EAAE;YAC9C,MAAM,QAAQ;YACd,YAAY;gBAAE;YAAM;YACpB,QAAQ,OAAO,GAAG;YAClB,OAAO;QACT;QAEA,IAAI,CAAC,WAAW,QAAQ,IAAI,OAAO,IAAI;YACrC,MAAM,QAAQ;YACd,YAAY;gBAAE;YAAM;YACpB,QAAQ,OAAO,GAAG;YAClB,OAAO;QACT;QAEA,IAAI;YACF,YAAY;gBACV,cAAc;gBACd,OAAO;YACT;YAEA,MAAM,WAAW,MAAM,UAAU,OAAO,CAAC,WAAW,CAAC,SAAS;YAE9D,8BAA8B;YAC9B,MAAM,iBAAiB,UAAU,OAAO,CAAC,sBAAsB;YAE/D,YAAY;gBACV,cAAc;gBACd,cAAc;gBACd,qBAAqB;gBACrB,OAAO;YACT;YAEA,+BAA+B;YAC/B,IAAI,QAAQ,eAAe,IAAI,QAAQ,gBAAgB,EAAE;gBACvD,UAAU,OAAO,CAAC,uBAAuB,CAAC,QAAQ,gBAAgB;YACpE;YAEA,QAAQ,UAAU,GAAG;YACrB,OAAO;QAET,EAAE,OAAO,OAAO;YACd,MAAM,eAAe,iBAAiB,QAAQ,MAAM,OAAO,GAAG;YAC9D,YAAY;gBACV,OAAO;gBACP,cAAc;YAChB;YACA,QAAQ,OAAO,GAAG;YAClB,OAAO;QACT;IACF,GAAG;QAAC,MAAM,aAAa;QAAE;QAAS;KAAY;IAE9C,MAAM,eAAe,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QAC/B,IAAI,UAAU,OAAO,EAAE;YACrB,UAAU,OAAO,CAAC,wBAAwB;YAC1C,YAAY;gBACV,qBAAqB,EAAE;gBACvB,cAAc;gBACd,OAAO;YACT;QACF;IACF,GAAG;QAAC;KAAY;IAEhB,MAAM,kBAAkB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QACnC,IAAI,UAAU,OAAO,EAAE;YACrB,UAAU,OAAO,CAAC,eAAe,CAAC;QACpC;IACF,GAAG,EAAE;IAEL,MAAM,kBAAkB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QAClC,OAAO,UAAU,OAAO,EAAE,qBAAqB;IACjD,GAAG,EAAE;IAEL,MAAM,WAA+B;QACnC;QACA;QACA;QACA;QACA;IACF;IAEA,OAAO;QAAC;QAAO;KAAS;AAC1B", "debugId": null}}, {"offset": {"line": 823, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Web_dev/coding_jr/voice-assistant/src/hooks/useTTS.ts"], "sourcesContent": ["import { useEffect, useRef, useState, useCallback } from 'react';\n\nexport interface SynthesisResult {\n  audioData: Float32Array;\n  sampleRate: number;\n  duration: number;\n  processingTime: number;\n  text: string;\n}\n\nexport interface TTSState {\n  isInitialized: boolean;\n  isSynthesizing: boolean;\n  error: string | null;\n  lastSynthesis: SynthesisResult | null;\n  initializationProgress: number;\n}\n\nexport interface TTSControls {\n  initialize: () => Promise<void>;\n  synthesizeText: (text: string, options?: SynthesisOptions) => Promise<void>;\n  playAudio: (audioData: Float32Array, sampleRate: number) => Promise<void>;\n  reset: () => void;\n}\n\nexport interface SynthesisOptions {\n  speakerEmbeddings?: Float32Array;\n  speed?: number;\n  pitch?: number;\n}\n\nexport interface UseTTSOptions {\n  onSynthesis?: (result: SynthesisResult) => void;\n  onError?: (error: string) => void;\n  autoInitialize?: boolean;\n  autoPlay?: boolean;\n}\n\nexport function useTTS(\n  options: UseTTSOptions = {}\n): [TTSState, TTSControls] {\n  const workerRef = useRef<Worker | null>(null);\n  const audioContextRef = useRef<AudioContext | null>(null);\n  const [state, setState] = useState<TTSState>({\n    isInitialized: false,\n    isSynthesizing: false,\n    error: null,\n    lastSynthesis: null,\n    initializationProgress: 0,\n  });\n\n  const updateState = useCallback((updates: Partial<TTSState>) => {\n    setState(prev => ({ ...prev, ...updates }));\n  }, []);\n\n  const playAudio = useCallback(async (\n    audioData: Float32Array, \n    sampleRate: number\n  ): Promise<void> => {\n    if (!audioContextRef.current) {\n      throw new Error('Audio context not available');\n    }\n\n    try {\n      // Resume audio context if suspended\n      if (audioContextRef.current.state === 'suspended') {\n        await audioContextRef.current.resume();\n      }\n\n      // Create audio buffer\n      const audioBuffer = audioContextRef.current.createBuffer(\n        1, // mono\n        audioData.length,\n        sampleRate\n      );\n\n      // Copy audio data to buffer\n      const channelData = audioBuffer.getChannelData(0);\n      channelData.set(audioData);\n\n      // Create and play audio source\n      const source = audioContextRef.current.createBufferSource();\n      source.buffer = audioBuffer;\n      source.connect(audioContextRef.current.destination);\n      \n      return new Promise((resolve) => {\n        source.onended = () => resolve();\n        source.start();\n      });\n\n    } catch (error) {\n      console.error('Audio playback error:', error);\n      throw error;\n    }\n  }, []);\n\n  const initialize = useCallback(async () => {\n    try {\n      updateState({ error: null, initializationProgress: 0 });\n\n      // Create audio context for playback\n      audioContextRef.current = new AudioContext();\n\n      // Create worker\n      workerRef.current = new Worker('/tts-worker.js');\n\n      // Set up message handler\n      workerRef.current.onmessage = async (event) => {\n        const { type, ...data } = event.data;\n\n        switch (type) {\n          case 'initialized':\n            if (data.success) {\n              updateState({ \n                isInitialized: true, \n                initializationProgress: 100,\n                error: null \n              });\n              console.log('TTS initialized successfully');\n            } else {\n              updateState({ \n                error: data.error || 'Failed to initialize TTS',\n                isInitialized: false,\n                initializationProgress: 0\n              });\n            }\n            break;\n\n          case 'synthesis':\n            const result: SynthesisResult = {\n              audioData: new Float32Array(data.result.audioData),\n              sampleRate: data.result.sampleRate,\n              duration: data.result.duration,\n              processingTime: data.result.processingTime,\n              text: data.result.text,\n            };\n\n            updateState({ \n              isSynthesizing: false,\n              lastSynthesis: result,\n              error: null\n            });\n\n            options.onSynthesis?.(result);\n\n            // Auto-play if enabled\n            if (options.autoPlay) {\n              await playAudio(result.audioData, result.sampleRate);\n            }\n            break;\n\n          case 'error':\n            const errorMessage = data.error || 'Unknown TTS error';\n            updateState({ \n              error: errorMessage,\n              isSynthesizing: false\n            });\n            options.onError?.(errorMessage);\n            break;\n\n          case 'progress':\n            updateState({ \n              initializationProgress: data.progress || 0\n            });\n            break;\n\n          default:\n            console.warn('Unknown message type from TTS worker:', type);\n        }\n      };\n\n      workerRef.current.onerror = (error) => {\n        const errorMessage = `TTS Worker error: ${error.message}`;\n        updateState({ \n          error: errorMessage,\n          isInitialized: false,\n          initializationProgress: 0\n        });\n        options.onError?.(errorMessage);\n      };\n\n      // Initialize the worker\n      updateState({ initializationProgress: 10 });\n      workerRef.current.postMessage({ type: 'initialize' });\n\n    } catch (error) {\n      const errorMessage = error instanceof Error ? error.message : 'Failed to create TTS worker';\n      updateState({ \n        error: errorMessage,\n        isInitialized: false,\n        initializationProgress: 0\n      });\n      options.onError?.(errorMessage);\n    }\n  }, [options, updateState, playAudio]);\n\n  const synthesizeText = useCallback(async (\n    text: string, \n    synthesisOptions: SynthesisOptions = {}\n  ) => {\n    if (!workerRef.current || !state.isInitialized) {\n      throw new Error('TTS not initialized');\n    }\n\n    if (!text || text.trim() === '') {\n      throw new Error('Text cannot be empty');\n    }\n\n    updateState({ isSynthesizing: true, error: null });\n\n    workerRef.current.postMessage({\n      type: 'synthesize',\n      data: {\n        text: text.trim(),\n        options: synthesisOptions\n      }\n    });\n  }, [state.isInitialized, updateState]);\n\n  const reset = useCallback(() => {\n    if (!workerRef.current) {\n      return;\n    }\n\n    workerRef.current.postMessage({ type: 'reset' });\n    updateState({ \n      isSynthesizing: false,\n      lastSynthesis: null,\n      error: null\n    });\n  }, [updateState]);\n\n  // Auto-initialize if requested\n  useEffect(() => {\n    if (options.autoInitialize) {\n      initialize();\n    }\n  }, [options.autoInitialize, initialize]);\n\n  // Cleanup on unmount\n  useEffect(() => {\n    return () => {\n      if (workerRef.current) {\n        workerRef.current.terminate();\n        workerRef.current = null;\n      }\n      if (audioContextRef.current) {\n        audioContextRef.current.close();\n        audioContextRef.current = null;\n      }\n    };\n  }, []);\n\n  const controls: TTSControls = {\n    initialize,\n    synthesizeText,\n    playAudio,\n    reset,\n  };\n\n  return [state, controls];\n}\n"], "names": [], "mappings": ";;;AAAA;;AAsCO,SAAS,OACd,UAAyB,CAAC,CAAC;IAE3B,MAAM,YAAY,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAiB;IACxC,MAAM,kBAAkB,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAuB;IACpD,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAY;QAC3C,eAAe;QACf,gBAAgB;QAChB,OAAO;QACP,eAAe;QACf,wBAAwB;IAC1B;IAEA,MAAM,cAAc,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QAC/B,SAAS,CAAA,OAAQ,CAAC;gBAAE,GAAG,IAAI;gBAAE,GAAG,OAAO;YAAC,CAAC;IAC3C,GAAG,EAAE;IAEL,MAAM,YAAY,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,OAC5B,WACA;QAEA,IAAI,CAAC,gBAAgB,OAAO,EAAE;YAC5B,MAAM,IAAI,MAAM;QAClB;QAEA,IAAI;YACF,oCAAoC;YACpC,IAAI,gBAAgB,OAAO,CAAC,KAAK,KAAK,aAAa;gBACjD,MAAM,gBAAgB,OAAO,CAAC,MAAM;YACtC;YAEA,sBAAsB;YACtB,MAAM,cAAc,gBAAgB,OAAO,CAAC,YAAY,CACtD,GACA,UAAU,MAAM,EAChB;YAGF,4BAA4B;YAC5B,MAAM,cAAc,YAAY,cAAc,CAAC;YAC/C,YAAY,GAAG,CAAC;YAEhB,+BAA+B;YAC/B,MAAM,SAAS,gBAAgB,OAAO,CAAC,kBAAkB;YACzD,OAAO,MAAM,GAAG;YAChB,OAAO,OAAO,CAAC,gBAAgB,OAAO,CAAC,WAAW;YAElD,OAAO,IAAI,QAAQ,CAAC;gBAClB,OAAO,OAAO,GAAG,IAAM;gBACvB,OAAO,KAAK;YACd;QAEF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,yBAAyB;YACvC,MAAM;QACR;IACF,GAAG,EAAE;IAEL,MAAM,aAAa,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QAC7B,IAAI;YACF,YAAY;gBAAE,OAAO;gBAAM,wBAAwB;YAAE;YAErD,oCAAoC;YACpC,gBAAgB,OAAO,GAAG,IAAI;YAE9B,gBAAgB;YAChB,UAAU,OAAO,GAAG,IAAI,OAAO;YAE/B,yBAAyB;YACzB,UAAU,OAAO,CAAC,SAAS,GAAG,OAAO;gBACnC,MAAM,EAAE,IAAI,EAAE,GAAG,MAAM,GAAG,MAAM,IAAI;gBAEpC,OAAQ;oBACN,KAAK;wBACH,IAAI,KAAK,OAAO,EAAE;4BAChB,YAAY;gCACV,eAAe;gCACf,wBAAwB;gCACxB,OAAO;4BACT;4BACA,QAAQ,GAAG,CAAC;wBACd,OAAO;4BACL,YAAY;gCACV,OAAO,KAAK,KAAK,IAAI;gCACrB,eAAe;gCACf,wBAAwB;4BAC1B;wBACF;wBACA;oBAEF,KAAK;wBACH,MAAM,SAA0B;4BAC9B,WAAW,IAAI,aAAa,KAAK,MAAM,CAAC,SAAS;4BACjD,YAAY,KAAK,MAAM,CAAC,UAAU;4BAClC,UAAU,KAAK,MAAM,CAAC,QAAQ;4BAC9B,gBAAgB,KAAK,MAAM,CAAC,cAAc;4BAC1C,MAAM,KAAK,MAAM,CAAC,IAAI;wBACxB;wBAEA,YAAY;4BACV,gBAAgB;4BAChB,eAAe;4BACf,OAAO;wBACT;wBAEA,QAAQ,WAAW,GAAG;wBAEtB,uBAAuB;wBACvB,IAAI,QAAQ,QAAQ,EAAE;4BACpB,MAAM,UAAU,OAAO,SAAS,EAAE,OAAO,UAAU;wBACrD;wBACA;oBAEF,KAAK;wBACH,MAAM,eAAe,KAAK,KAAK,IAAI;wBACnC,YAAY;4BACV,OAAO;4BACP,gBAAgB;wBAClB;wBACA,QAAQ,OAAO,GAAG;wBAClB;oBAEF,KAAK;wBACH,YAAY;4BACV,wBAAwB,KAAK,QAAQ,IAAI;wBAC3C;wBACA;oBAEF;wBACE,QAAQ,IAAI,CAAC,yCAAyC;gBAC1D;YACF;YAEA,UAAU,OAAO,CAAC,OAAO,GAAG,CAAC;gBAC3B,MAAM,eAAe,CAAC,kBAAkB,EAAE,MAAM,OAAO,EAAE;gBACzD,YAAY;oBACV,OAAO;oBACP,eAAe;oBACf,wBAAwB;gBAC1B;gBACA,QAAQ,OAAO,GAAG;YACpB;YAEA,wBAAwB;YACxB,YAAY;gBAAE,wBAAwB;YAAG;YACzC,UAAU,OAAO,CAAC,WAAW,CAAC;gBAAE,MAAM;YAAa;QAErD,EAAE,OAAO,OAAO;YACd,MAAM,eAAe,iBAAiB,QAAQ,MAAM,OAAO,GAAG;YAC9D,YAAY;gBACV,OAAO;gBACP,eAAe;gBACf,wBAAwB;YAC1B;YACA,QAAQ,OAAO,GAAG;QACpB;IACF,GAAG;QAAC;QAAS;QAAa;KAAU;IAEpC,MAAM,iBAAiB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,OACjC,MACA,mBAAqC,CAAC,CAAC;QAEvC,IAAI,CAAC,UAAU,OAAO,IAAI,CAAC,MAAM,aAAa,EAAE;YAC9C,MAAM,IAAI,MAAM;QAClB;QAEA,IAAI,CAAC,QAAQ,KAAK,IAAI,OAAO,IAAI;YAC/B,MAAM,IAAI,MAAM;QAClB;QAEA,YAAY;YAAE,gBAAgB;YAAM,OAAO;QAAK;QAEhD,UAAU,OAAO,CAAC,WAAW,CAAC;YAC5B,MAAM;YACN,MAAM;gBACJ,MAAM,KAAK,IAAI;gBACf,SAAS;YACX;QACF;IACF,GAAG;QAAC,MAAM,aAAa;QAAE;KAAY;IAErC,MAAM,QAAQ,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QACxB,IAAI,CAAC,UAAU,OAAO,EAAE;YACtB;QACF;QAEA,UAAU,OAAO,CAAC,WAAW,CAAC;YAAE,MAAM;QAAQ;QAC9C,YAAY;YACV,gBAAgB;YAChB,eAAe;YACf,OAAO;QACT;IACF,GAAG;QAAC;KAAY;IAEhB,+BAA+B;IAC/B,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,QAAQ,cAAc,EAAE;YAC1B;QACF;IACF,GAAG;QAAC,QAAQ,cAAc;QAAE;KAAW;IAEvC,qBAAqB;IACrB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,OAAO;YACL,IAAI,UAAU,OAAO,EAAE;gBACrB,UAAU,OAAO,CAAC,SAAS;gBAC3B,UAAU,OAAO,GAAG;YACtB;YACA,IAAI,gBAAgB,OAAO,EAAE;gBAC3B,gBAAgB,OAAO,CAAC,KAAK;gBAC7B,gBAAgB,OAAO,GAAG;YAC5B;QACF;IACF,GAAG,EAAE;IAEL,MAAM,WAAwB;QAC5B;QACA;QACA;QACA;IACF;IAEA,OAAO;QAAC;QAAO;KAAS;AAC1B", "debugId": null}}, {"offset": {"line": 1041, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Web_dev/coding_jr/voice-assistant/src/lib/performanceMonitor.ts"], "sourcesContent": ["export interface PerformanceMetrics {\n  timestamp: number;\n  sessionId: string;\n  audioLength: number;\n  sttLatency: number;\n  llmLatency: number;\n  ttsLatency: number;\n  totalLatency: number;\n  networkLatency?: number;\n  cacheHit?: boolean;\n  userAgent: string;\n  deviceInfo: {\n    platform: string;\n    memory?: number;\n    cores?: number;\n  };\n}\n\nexport interface PerformanceReport {\n  totalSessions: number;\n  averageLatency: number;\n  medianLatency: number;\n  p95Latency: number;\n  successRate: number;\n  cacheHitRate: number;\n  breakdown: {\n    stt: { avg: number; median: number; p95: number };\n    llm: { avg: number; median: number; p95: number };\n    tts: { avg: number; median: number; p95: number };\n  };\n  recommendations: string[];\n}\n\nclass PerformanceMonitor {\n  private metrics: PerformanceMetrics[] = [];\n  private sessionId: string;\n  private maxMetrics = 1000; // Keep last 1000 metrics\n\n  constructor() {\n    this.sessionId = this.generateSessionId();\n  }\n\n  private generateSessionId(): string {\n    return `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;\n  }\n\n  private getDeviceInfo() {\n    const nav = navigator as Navigator & {\n      deviceMemory?: number;\n      hardwareConcurrency?: number;\n    };\n    return {\n      platform: navigator.platform,\n      memory: nav.deviceMemory,\n      cores: nav.hardwareConcurrency,\n    };\n  }\n\n  recordMetrics(metrics: Omit<PerformanceMetrics, 'timestamp' | 'sessionId' | 'userAgent' | 'deviceInfo'>): void {\n    const fullMetrics: PerformanceMetrics = {\n      ...metrics,\n      timestamp: Date.now(),\n      sessionId: this.sessionId,\n      userAgent: navigator.userAgent,\n      deviceInfo: this.getDeviceInfo(),\n    };\n\n    this.metrics.push(fullMetrics);\n\n    // Keep only the last N metrics\n    if (this.metrics.length > this.maxMetrics) {\n      this.metrics = this.metrics.slice(-this.maxMetrics);\n    }\n\n    // Log performance\n    this.logPerformance(fullMetrics);\n\n    // Store in localStorage for persistence\n    this.persistMetrics();\n  }\n\n  private logPerformance(metrics: PerformanceMetrics): void {\n    const { totalLatency, sttLatency, llmLatency, ttsLatency, audioLength } = metrics;\n    \n    console.group('🔍 Performance Metrics');\n    console.log(`Total Latency: ${totalLatency.toFixed(0)}ms ${totalLatency < 1200 ? '✅' : '⚠️'}`);\n    console.log(`STT: ${sttLatency.toFixed(0)}ms`);\n    console.log(`LLM: ${llmLatency.toFixed(0)}ms`);\n    console.log(`TTS: ${ttsLatency.toFixed(0)}ms`);\n    console.log(`Audio Length: ${audioLength.toFixed(1)}s`);\n    console.log(`Efficiency: ${(audioLength * 1000 / totalLatency).toFixed(2)}x realtime`);\n    \n    if (totalLatency > 1200) {\n      console.warn('⚠️ Total latency exceeds 1.2s target');\n      this.analyzeBottlenecks(metrics);\n    }\n    \n    console.groupEnd();\n  }\n\n  private analyzeBottlenecks(metrics: PerformanceMetrics): void {\n    const { sttLatency, llmLatency, ttsLatency } = metrics;\n    const total = sttLatency + llmLatency + ttsLatency;\n    \n    console.group('🔍 Bottleneck Analysis');\n    \n    if (sttLatency > total * 0.4) {\n      console.warn('STT is the bottleneck (>40% of total time)');\n      console.log('Recommendations: Use smaller Whisper model, optimize audio preprocessing');\n    }\n    \n    if (llmLatency > total * 0.5) {\n      console.warn('LLM is the bottleneck (>50% of total time)');\n      console.log('Recommendations: Use faster model (gpt-3.5-turbo), reduce max_tokens, optimize prompt');\n    }\n    \n    if (ttsLatency > total * 0.3) {\n      console.warn('TTS is the bottleneck (>30% of total time)');\n      console.log('Recommendations: Use smaller TTS model, optimize text preprocessing');\n    }\n    \n    console.groupEnd();\n  }\n\n  private persistMetrics(): void {\n    try {\n      const recentMetrics = this.metrics.slice(-100); // Store last 100\n      localStorage.setItem('voiceAssistantMetrics', JSON.stringify(recentMetrics));\n    } catch (error) {\n      console.warn('Failed to persist metrics:', error);\n    }\n  }\n\n  private loadPersistedMetrics(): void {\n    try {\n      const stored = localStorage.getItem('voiceAssistantMetrics');\n      if (stored) {\n        const parsed = JSON.parse(stored);\n        if (Array.isArray(parsed)) {\n          this.metrics = parsed;\n        }\n      }\n    } catch (error) {\n      console.warn('Failed to load persisted metrics:', error);\n    }\n  }\n\n  generateReport(): PerformanceReport {\n    if (this.metrics.length === 0) {\n      return {\n        totalSessions: 0,\n        averageLatency: 0,\n        medianLatency: 0,\n        p95Latency: 0,\n        successRate: 0,\n        cacheHitRate: 0,\n        breakdown: {\n          stt: { avg: 0, median: 0, p95: 0 },\n          llm: { avg: 0, median: 0, p95: 0 },\n          tts: { avg: 0, median: 0, p95: 0 },\n        },\n        recommendations: [],\n      };\n    }\n\n    const latencies = this.metrics.map(m => m.totalLatency).sort((a, b) => a - b);\n    const sttLatencies = this.metrics.map(m => m.sttLatency).sort((a, b) => a - b);\n    const llmLatencies = this.metrics.map(m => m.llmLatency).sort((a, b) => a - b);\n    const ttsLatencies = this.metrics.map(m => m.ttsLatency).sort((a, b) => a - b);\n\n    const avg = (arr: number[]) => arr.reduce((a, b) => a + b, 0) / arr.length;\n    const median = (arr: number[]) => arr[Math.floor(arr.length / 2)];\n    const p95 = (arr: number[]) => arr[Math.floor(arr.length * 0.95)];\n\n    const cacheHits = this.metrics.filter(m => m.cacheHit).length;\n    const successfulSessions = this.metrics.filter(m => m.totalLatency > 0).length;\n\n    const report: PerformanceReport = {\n      totalSessions: this.metrics.length,\n      averageLatency: avg(latencies),\n      medianLatency: median(latencies),\n      p95Latency: p95(latencies),\n      successRate: successfulSessions / this.metrics.length,\n      cacheHitRate: cacheHits / this.metrics.length,\n      breakdown: {\n        stt: { avg: avg(sttLatencies), median: median(sttLatencies), p95: p95(sttLatencies) },\n        llm: { avg: avg(llmLatencies), median: median(llmLatencies), p95: p95(llmLatencies) },\n        tts: { avg: avg(ttsLatencies), median: median(ttsLatencies), p95: p95(ttsLatencies) },\n      },\n      recommendations: this.generateRecommendations(report),\n    };\n\n    return report;\n  }\n\n  private generateRecommendations(report: PerformanceReport): string[] {\n    const recommendations: string[] = [];\n\n    if (report.averageLatency > 1200) {\n      recommendations.push('Average latency exceeds 1.2s target. Consider optimizing the pipeline.');\n    }\n\n    if (report.breakdown.stt.avg > 500) {\n      recommendations.push('STT latency is high. Consider using whisper-tiny or optimizing audio preprocessing.');\n    }\n\n    if (report.breakdown.llm.avg > 800) {\n      recommendations.push('LLM latency is high. Consider using gpt-3.5-turbo or reducing max_tokens.');\n    }\n\n    if (report.breakdown.tts.avg > 400) {\n      recommendations.push('TTS latency is high. Consider using a smaller TTS model or optimizing text preprocessing.');\n    }\n\n    if (report.cacheHitRate < 0.8) {\n      recommendations.push('Low cache hit rate. Ensure models are properly cached.');\n    }\n\n    if (report.successRate < 0.95) {\n      recommendations.push('Low success rate. Check for errors in the pipeline.');\n    }\n\n    return recommendations;\n  }\n\n  exportMetrics(): string {\n    return JSON.stringify(this.metrics, null, 2);\n  }\n\n  clearMetrics(): void {\n    this.metrics = [];\n    localStorage.removeItem('voiceAssistantMetrics');\n  }\n\n  getRecentMetrics(count: number = 10): PerformanceMetrics[] {\n    return this.metrics.slice(-count);\n  }\n\n  // Initialize with persisted data\n  init(): void {\n    this.loadPersistedMetrics();\n  }\n}\n\n// Singleton instance\nexport const performanceMonitor = new PerformanceMonitor();\n\n// Initialize on import\nperformanceMonitor.init();\n"], "names": [], "mappings": ";;;AAiCA,MAAM;IACI,UAAgC,EAAE,CAAC;IACnC,UAAkB;IAClB,aAAa,KAAK;IAE1B,aAAc;QACZ,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,iBAAiB;IACzC;IAEQ,oBAA4B;QAClC,OAAO,CAAC,QAAQ,EAAE,KAAK,GAAG,GAAG,CAAC,EAAE,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG,IAAI;IAC3E;IAEQ,gBAAgB;QACtB,MAAM,MAAM;QAIZ,OAAO;YACL,UAAU,UAAU,QAAQ;YAC5B,QAAQ,IAAI,YAAY;YACxB,OAAO,IAAI,mBAAmB;QAChC;IACF;IAEA,cAAc,OAAyF,EAAQ;QAC7G,MAAM,cAAkC;YACtC,GAAG,OAAO;YACV,WAAW,KAAK,GAAG;YACnB,WAAW,IAAI,CAAC,SAAS;YACzB,WAAW,UAAU,SAAS;YAC9B,YAAY,IAAI,CAAC,aAAa;QAChC;QAEA,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC;QAElB,+BAA+B;QAC/B,IAAI,IAAI,CAAC,OAAO,CAAC,MAAM,GAAG,IAAI,CAAC,UAAU,EAAE;YACzC,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,UAAU;QACpD;QAEA,kBAAkB;QAClB,IAAI,CAAC,cAAc,CAAC;QAEpB,wCAAwC;QACxC,IAAI,CAAC,cAAc;IACrB;IAEQ,eAAe,OAA2B,EAAQ;QACxD,MAAM,EAAE,YAAY,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,WAAW,EAAE,GAAG;QAE1E,QAAQ,KAAK,CAAC;QACd,QAAQ,GAAG,CAAC,CAAC,eAAe,EAAE,aAAa,OAAO,CAAC,GAAG,GAAG,EAAE,eAAe,OAAO,MAAM,MAAM;QAC7F,QAAQ,GAAG,CAAC,CAAC,KAAK,EAAE,WAAW,OAAO,CAAC,GAAG,EAAE,CAAC;QAC7C,QAAQ,GAAG,CAAC,CAAC,KAAK,EAAE,WAAW,OAAO,CAAC,GAAG,EAAE,CAAC;QAC7C,QAAQ,GAAG,CAAC,CAAC,KAAK,EAAE,WAAW,OAAO,CAAC,GAAG,EAAE,CAAC;QAC7C,QAAQ,GAAG,CAAC,CAAC,cAAc,EAAE,YAAY,OAAO,CAAC,GAAG,CAAC,CAAC;QACtD,QAAQ,GAAG,CAAC,CAAC,YAAY,EAAE,CAAC,cAAc,OAAO,YAAY,EAAE,OAAO,CAAC,GAAG,UAAU,CAAC;QAErF,IAAI,eAAe,MAAM;YACvB,QAAQ,IAAI,CAAC;YACb,IAAI,CAAC,kBAAkB,CAAC;QAC1B;QAEA,QAAQ,QAAQ;IAClB;IAEQ,mBAAmB,OAA2B,EAAQ;QAC5D,MAAM,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,GAAG;QAC/C,MAAM,QAAQ,aAAa,aAAa;QAExC,QAAQ,KAAK,CAAC;QAEd,IAAI,aAAa,QAAQ,KAAK;YAC5B,QAAQ,IAAI,CAAC;YACb,QAAQ,GAAG,CAAC;QACd;QAEA,IAAI,aAAa,QAAQ,KAAK;YAC5B,QAAQ,IAAI,CAAC;YACb,QAAQ,GAAG,CAAC;QACd;QAEA,IAAI,aAAa,QAAQ,KAAK;YAC5B,QAAQ,IAAI,CAAC;YACb,QAAQ,GAAG,CAAC;QACd;QAEA,QAAQ,QAAQ;IAClB;IAEQ,iBAAuB;QAC7B,IAAI;YACF,MAAM,gBAAgB,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,MAAM,iBAAiB;YACjE,aAAa,OAAO,CAAC,yBAAyB,KAAK,SAAS,CAAC;QAC/D,EAAE,OAAO,OAAO;YACd,QAAQ,IAAI,CAAC,8BAA8B;QAC7C;IACF;IAEQ,uBAA6B;QACnC,IAAI;YACF,MAAM,SAAS,aAAa,OAAO,CAAC;YACpC,IAAI,QAAQ;gBACV,MAAM,SAAS,KAAK,KAAK,CAAC;gBAC1B,IAAI,MAAM,OAAO,CAAC,SAAS;oBACzB,IAAI,CAAC,OAAO,GAAG;gBACjB;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,IAAI,CAAC,qCAAqC;QACpD;IACF;IAEA,iBAAoC;QAClC,IAAI,IAAI,CAAC,OAAO,CAAC,MAAM,KAAK,GAAG;YAC7B,OAAO;gBACL,eAAe;gBACf,gBAAgB;gBAChB,eAAe;gBACf,YAAY;gBACZ,aAAa;gBACb,cAAc;gBACd,WAAW;oBACT,KAAK;wBAAE,KAAK;wBAAG,QAAQ;wBAAG,KAAK;oBAAE;oBACjC,KAAK;wBAAE,KAAK;wBAAG,QAAQ;wBAAG,KAAK;oBAAE;oBACjC,KAAK;wBAAE,KAAK;wBAAG,QAAQ;wBAAG,KAAK;oBAAE;gBACnC;gBACA,iBAAiB,EAAE;YACrB;QACF;QAEA,MAAM,YAAY,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,CAAA,IAAK,EAAE,YAAY,EAAE,IAAI,CAAC,CAAC,GAAG,IAAM,IAAI;QAC3E,MAAM,eAAe,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,CAAA,IAAK,EAAE,UAAU,EAAE,IAAI,CAAC,CAAC,GAAG,IAAM,IAAI;QAC5E,MAAM,eAAe,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,CAAA,IAAK,EAAE,UAAU,EAAE,IAAI,CAAC,CAAC,GAAG,IAAM,IAAI;QAC5E,MAAM,eAAe,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,CAAA,IAAK,EAAE,UAAU,EAAE,IAAI,CAAC,CAAC,GAAG,IAAM,IAAI;QAE5E,MAAM,MAAM,CAAC,MAAkB,IAAI,MAAM,CAAC,CAAC,GAAG,IAAM,IAAI,GAAG,KAAK,IAAI,MAAM;QAC1E,MAAM,SAAS,CAAC,MAAkB,GAAG,CAAC,KAAK,KAAK,CAAC,IAAI,MAAM,GAAG,GAAG;QACjE,MAAM,MAAM,CAAC,MAAkB,GAAG,CAAC,KAAK,KAAK,CAAC,IAAI,MAAM,GAAG,MAAM;QAEjE,MAAM,YAAY,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,CAAA,IAAK,EAAE,QAAQ,EAAE,MAAM;QAC7D,MAAM,qBAAqB,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,CAAA,IAAK,EAAE,YAAY,GAAG,GAAG,MAAM;QAE9E,MAAM,SAA4B;YAChC,eAAe,IAAI,CAAC,OAAO,CAAC,MAAM;YAClC,gBAAgB,IAAI;YACpB,eAAe,OAAO;YACtB,YAAY,IAAI;YAChB,aAAa,qBAAqB,IAAI,CAAC,OAAO,CAAC,MAAM;YACrD,cAAc,YAAY,IAAI,CAAC,OAAO,CAAC,MAAM;YAC7C,WAAW;gBACT,KAAK;oBAAE,KAAK,IAAI;oBAAe,QAAQ,OAAO;oBAAe,KAAK,IAAI;gBAAc;gBACpF,KAAK;oBAAE,KAAK,IAAI;oBAAe,QAAQ,OAAO;oBAAe,KAAK,IAAI;gBAAc;gBACpF,KAAK;oBAAE,KAAK,IAAI;oBAAe,QAAQ,OAAO;oBAAe,KAAK,IAAI;gBAAc;YACtF;YACA,iBAAiB,IAAI,CAAC,uBAAuB,CAAC;QAChD;QAEA,OAAO;IACT;IAEQ,wBAAwB,MAAyB,EAAY;QACnE,MAAM,kBAA4B,EAAE;QAEpC,IAAI,OAAO,cAAc,GAAG,MAAM;YAChC,gBAAgB,IAAI,CAAC;QACvB;QAEA,IAAI,OAAO,SAAS,CAAC,GAAG,CAAC,GAAG,GAAG,KAAK;YAClC,gBAAgB,IAAI,CAAC;QACvB;QAEA,IAAI,OAAO,SAAS,CAAC,GAAG,CAAC,GAAG,GAAG,KAAK;YAClC,gBAAgB,IAAI,CAAC;QACvB;QAEA,IAAI,OAAO,SAAS,CAAC,GAAG,CAAC,GAAG,GAAG,KAAK;YAClC,gBAAgB,IAAI,CAAC;QACvB;QAEA,IAAI,OAAO,YAAY,GAAG,KAAK;YAC7B,gBAAgB,IAAI,CAAC;QACvB;QAEA,IAAI,OAAO,WAAW,GAAG,MAAM;YAC7B,gBAAgB,IAAI,CAAC;QACvB;QAEA,OAAO;IACT;IAEA,gBAAwB;QACtB,OAAO,KAAK,SAAS,CAAC,IAAI,CAAC,OAAO,EAAE,MAAM;IAC5C;IAEA,eAAqB;QACnB,IAAI,CAAC,OAAO,GAAG,EAAE;QACjB,aAAa,UAAU,CAAC;IAC1B;IAEA,iBAAiB,QAAgB,EAAE,EAAwB;QACzD,OAAO,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;IAC7B;IAEA,iCAAiC;IACjC,OAAa;QACX,IAAI,CAAC,oBAAoB;IAC3B;AACF;AAGO,MAAM,qBAAqB,IAAI;AAEtC,uBAAuB;AACvB,mBAAmB,IAAI", "debugId": null}}, {"offset": {"line": 1244, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Web_dev/coding_jr/voice-assistant/src/hooks/useVoiceAssistant.ts"], "sourcesContent": ["import { useState, useCallback, useEffect, useRef } from 'react';\nimport { useAudioRecording } from './useAudioRecording';\nimport { useWhisperSTT } from './useWhisperSTT';\nimport { useOpenAIChat } from './useOpenAIChat';\nimport { useTTS } from './useTTS';\nimport { AudioChunk } from '@/lib/audioManager';\nimport { TranscriptionResult } from './useWhisperSTT';\nimport { ChatResponse } from '@/lib/openaiClient';\nimport { SynthesisResult } from './useTTS';\nimport { performanceMonitor } from '@/lib/performanceMonitor';\n\nexport interface VoiceAssistantState {\n  isInitialized: boolean;\n  isListening: boolean;\n  isProcessing: boolean;\n  currentStage: 'idle' | 'listening' | 'transcribing' | 'thinking' | 'speaking';\n  error: string | null;\n  volume: number;\n  lastTranscription: string | null;\n  lastResponse: string | null;\n}\n\nexport interface VoiceAssistantControls {\n  initialize: (openaiApiKey: string) => Promise<void>;\n  startListening: () => void;\n  stopListening: () => void;\n  sendTextMessage: (message: string) => Promise<void>;\n  reset: () => void;\n}\n\nexport interface VoiceAssistantMetrics {\n  sttLatency: number;\n  llmLatency: number;\n  ttsLatency: number;\n  totalLatency: number;\n  audioLength: number;\n}\n\nexport interface UseVoiceAssistantOptions {\n  onStageChange?: (stage: VoiceAssistantState['currentStage']) => void;\n  onTranscription?: (text: string) => void;\n  onResponse?: (text: string) => void;\n  onMetrics?: (metrics: VoiceAssistantMetrics) => void;\n  onError?: (error: string) => void;\n  autoStartListening?: boolean;\n}\n\nexport function useVoiceAssistant(\n  options: UseVoiceAssistantOptions = {}\n): [VoiceAssistantState, VoiceAssistantControls] {\n  const [state, setState] = useState<VoiceAssistantState>({\n    isInitialized: false,\n    isListening: false,\n    isProcessing: false,\n    currentStage: 'idle',\n    error: null,\n    volume: 0,\n    lastTranscription: null,\n    lastResponse: null,\n  });\n\n  const metricsRef = useRef<Partial<VoiceAssistantMetrics>>({});\n  const pipelineStartTimeRef = useRef<number>(0);\n\n  const updateState = useCallback((updates: Partial<VoiceAssistantState>) => {\n    setState(prev => {\n      const newState = { ...prev, ...updates };\n      \n      // Notify stage change\n      if (updates.currentStage && updates.currentStage !== prev.currentStage) {\n        options.onStageChange?.(updates.currentStage);\n      }\n      \n      return newState;\n    });\n  }, [options]);\n\n  // Audio recording hook\n  const [, audioControls] = useAudioRecording({\n    onAudioChunk: (chunk: AudioChunk) => {\n      if (state.currentStage === 'listening') {\n        whisperControls.bufferAudio(chunk);\n      }\n    },\n    onSpeechStart: () => {\n      console.log('Speech started');\n      pipelineStartTimeRef.current = performance.now();\n      metricsRef.current = {};\n      updateState({ currentStage: 'listening' });\n    },\n    onSpeechEnd: () => {\n      console.log('Speech ended');\n      if (state.currentStage === 'listening') {\n        updateState({ currentStage: 'transcribing' });\n        whisperControls.flushBuffer();\n      }\n    },\n    onVolumeChange: (volume) => {\n      updateState({ volume });\n    },\n    onError: (error) => {\n      updateState({ error: error.message });\n      options.onError?.(error.message);\n    },\n  });\n\n  // Whisper STT hook\n  const [, whisperControls] = useWhisperSTT({\n    onTranscription: (result: TranscriptionResult) => {\n      const sttEndTime = performance.now();\n      metricsRef.current.sttLatency = sttEndTime - pipelineStartTimeRef.current;\n      metricsRef.current.audioLength = result.audioLength;\n\n      console.log(`STT completed: \"${result.text}\" (${metricsRef.current.sttLatency.toFixed(2)}ms)`);\n      \n      updateState({ \n        lastTranscription: result.text,\n        currentStage: 'thinking'\n      });\n      \n      options.onTranscription?.(result.text);\n      \n      // Send to OpenAI\n      if (result.text.trim()) {\n        const llmStartTime = performance.now();\n        chatControls.sendMessage(result.text).then(() => {\n          const llmEndTime = performance.now();\n          metricsRef.current.llmLatency = llmEndTime - llmStartTime;\n        });\n      }\n    },\n    onError: (error) => {\n      updateState({ error, currentStage: 'idle' });\n      options.onError?.(error);\n    },\n  });\n\n  // OpenAI Chat hook\n  const [, chatControls] = useOpenAIChat({\n    onResponse: (response: ChatResponse) => {\n      console.log(`LLM completed: \"${response.message}\" (${response.processingTime.toFixed(2)}ms)`);\n      \n      updateState({ \n        lastResponse: response.message,\n        currentStage: 'speaking'\n      });\n      \n      options.onResponse?.(response.message);\n      \n      // Send to TTS\n      ttsControls.synthesizeText(response.message);\n    },\n    onError: (error) => {\n      updateState({ error, currentStage: 'idle' });\n      options.onError?.(error);\n    },\n    autoTrimHistory: true,\n    maxHistoryLength: 10,\n  });\n\n  // TTS hook\n  const [, ttsControls] = useTTS({\n    onSynthesis: (result: SynthesisResult) => {\n      const totalEndTime = performance.now();\n      metricsRef.current.ttsLatency = result.processingTime;\n      metricsRef.current.totalLatency = totalEndTime - pipelineStartTimeRef.current;\n\n      console.log(`TTS completed (${result.processingTime.toFixed(2)}ms)`);\n      console.log(`Total pipeline latency: ${metricsRef.current.totalLatency?.toFixed(2)}ms`);\n\n      // Report metrics\n      if (metricsRef.current.totalLatency) {\n        const metrics: VoiceAssistantMetrics = {\n          sttLatency: metricsRef.current.sttLatency || 0,\n          llmLatency: metricsRef.current.llmLatency || 0,\n          ttsLatency: metricsRef.current.ttsLatency || 0,\n          totalLatency: metricsRef.current.totalLatency,\n          audioLength: metricsRef.current.audioLength || 0,\n        };\n\n        // Record performance metrics\n        performanceMonitor.recordMetrics({\n          audioLength: metrics.audioLength,\n          sttLatency: metrics.sttLatency,\n          llmLatency: metrics.llmLatency,\n          ttsLatency: metrics.ttsLatency,\n          totalLatency: metrics.totalLatency,\n        });\n\n        options.onMetrics?.(metrics);\n      }\n\n      // Return to listening after speech\n      setTimeout(() => {\n        updateState({ currentStage: 'idle' });\n      }, 500);\n    },\n    onError: (error) => {\n      updateState({ error, currentStage: 'idle' });\n      options.onError?.(error);\n    },\n    autoPlay: true,\n  });\n\n  // Initialize all components\n  const initialize = useCallback(async (openaiApiKey: string) => {\n    try {\n      updateState({ error: null });\n\n      // Initialize all components\n      await Promise.all([\n        audioControls.initialize(),\n        whisperControls.initialize(),\n        ttsControls.initialize(),\n      ]);\n\n      // Initialize OpenAI\n      chatControls.initialize(openaiApiKey);\n\n      updateState({ isInitialized: true });\n\n      // Auto-start listening if requested\n      if (options.autoStartListening) {\n        audioControls.startRecording();\n        updateState({\n          isListening: true,\n          currentStage: 'idle',\n          error: null\n        });\n      }\n\n    } catch (error) {\n      const errorMessage = error instanceof Error ? error.message : 'Initialization failed';\n      updateState({ error: errorMessage, isInitialized: false });\n      options.onError?.(errorMessage);\n    }\n  }, [audioControls, whisperControls, ttsControls, chatControls, options, updateState]);\n\n  const startListening = useCallback(() => {\n    if (!state.isInitialized) {\n      updateState({ error: 'Voice assistant not initialized' });\n      return;\n    }\n\n    audioControls.startRecording();\n    updateState({ \n      isListening: true, \n      currentStage: 'idle',\n      error: null \n    });\n  }, [state.isInitialized, audioControls, updateState]);\n\n  const stopListening = useCallback(() => {\n    audioControls.stopRecording();\n    updateState({ \n      isListening: false, \n      currentStage: 'idle' \n    });\n  }, [audioControls, updateState]);\n\n  const sendTextMessage = useCallback(async (message: string) => {\n    if (!state.isInitialized) {\n      throw new Error('Voice assistant not initialized');\n    }\n\n    updateState({ currentStage: 'thinking' });\n    \n    try {\n      const response = await chatControls.sendMessage(message);\n      if (response) {\n        updateState({ \n          lastTranscription: message,\n          lastResponse: response.message,\n          currentStage: 'speaking'\n        });\n        \n        // Synthesize response\n        await ttsControls.synthesizeText(response.message);\n      }\n    } catch (error) {\n      const errorMessage = error instanceof Error ? error.message : 'Failed to send message';\n      updateState({ error: errorMessage, currentStage: 'idle' });\n      throw error;\n    }\n  }, [state.isInitialized, chatControls, ttsControls, updateState]);\n\n  const reset = useCallback(() => {\n    audioControls.stopRecording();\n    whisperControls.reset();\n    chatControls.clearHistory();\n    ttsControls.reset();\n    \n    setState({\n      isInitialized: false,\n      isListening: false,\n      isProcessing: false,\n      currentStage: 'idle',\n      error: null,\n      volume: 0,\n      lastTranscription: null,\n      lastResponse: null,\n    });\n  }, [audioControls, whisperControls, chatControls, ttsControls]);\n\n  // Update processing state based on current stage\n  useEffect(() => {\n    const isProcessing = ['transcribing', 'thinking', 'speaking'].includes(state.currentStage);\n    if (state.isProcessing !== isProcessing) {\n      updateState({ isProcessing });\n    }\n  }, [state.currentStage, state.isProcessing, updateState]);\n\n  const controls: VoiceAssistantControls = {\n    initialize,\n    startListening,\n    stopListening,\n    sendTextMessage,\n    reset,\n  };\n\n  return [state, controls];\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;AACA;AACA;AAKA;;;;;;;AAsCO,SAAS,kBACd,UAAoC,CAAC,CAAC;IAEtC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAuB;QACtD,eAAe;QACf,aAAa;QACb,cAAc;QACd,cAAc;QACd,OAAO;QACP,QAAQ;QACR,mBAAmB;QACnB,cAAc;IAChB;IAEA,MAAM,aAAa,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAkC,CAAC;IAC3D,MAAM,uBAAuB,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAU;IAE5C,MAAM,cAAc,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QAC/B,SAAS,CAAA;YACP,MAAM,WAAW;gBAAE,GAAG,IAAI;gBAAE,GAAG,OAAO;YAAC;YAEvC,sBAAsB;YACtB,IAAI,QAAQ,YAAY,IAAI,QAAQ,YAAY,KAAK,KAAK,YAAY,EAAE;gBACtE,QAAQ,aAAa,GAAG,QAAQ,YAAY;YAC9C;YAEA,OAAO;QACT;IACF,GAAG;QAAC;KAAQ;IAEZ,uBAAuB;IACvB,MAAM,GAAG,cAAc,GAAG,CAAA,GAAA,iIAAA,CAAA,oBAAiB,AAAD,EAAE;QAC1C,cAAc,CAAC;YACb,IAAI,MAAM,YAAY,KAAK,aAAa;gBACtC,gBAAgB,WAAW,CAAC;YAC9B;QACF;QACA,eAAe;YACb,QAAQ,GAAG,CAAC;YACZ,qBAAqB,OAAO,GAAG,YAAY,GAAG;YAC9C,WAAW,OAAO,GAAG,CAAC;YACtB,YAAY;gBAAE,cAAc;YAAY;QAC1C;QACA,aAAa;YACX,QAAQ,GAAG,CAAC;YACZ,IAAI,MAAM,YAAY,KAAK,aAAa;gBACtC,YAAY;oBAAE,cAAc;gBAAe;gBAC3C,gBAAgB,WAAW;YAC7B;QACF;QACA,gBAAgB,CAAC;YACf,YAAY;gBAAE;YAAO;QACvB;QACA,SAAS,CAAC;YACR,YAAY;gBAAE,OAAO,MAAM,OAAO;YAAC;YACnC,QAAQ,OAAO,GAAG,MAAM,OAAO;QACjC;IACF;IAEA,mBAAmB;IACnB,MAAM,GAAG,gBAAgB,GAAG,CAAA,GAAA,6HAAA,CAAA,gBAAa,AAAD,EAAE;QACxC,iBAAiB,CAAC;YAChB,MAAM,aAAa,YAAY,GAAG;YAClC,WAAW,OAAO,CAAC,UAAU,GAAG,aAAa,qBAAqB,OAAO;YACzE,WAAW,OAAO,CAAC,WAAW,GAAG,OAAO,WAAW;YAEnD,QAAQ,GAAG,CAAC,CAAC,gBAAgB,EAAE,OAAO,IAAI,CAAC,GAAG,EAAE,WAAW,OAAO,CAAC,UAAU,CAAC,OAAO,CAAC,GAAG,GAAG,CAAC;YAE7F,YAAY;gBACV,mBAAmB,OAAO,IAAI;gBAC9B,cAAc;YAChB;YAEA,QAAQ,eAAe,GAAG,OAAO,IAAI;YAErC,iBAAiB;YACjB,IAAI,OAAO,IAAI,CAAC,IAAI,IAAI;gBACtB,MAAM,eAAe,YAAY,GAAG;gBACpC,aAAa,WAAW,CAAC,OAAO,IAAI,EAAE,IAAI,CAAC;oBACzC,MAAM,aAAa,YAAY,GAAG;oBAClC,WAAW,OAAO,CAAC,UAAU,GAAG,aAAa;gBAC/C;YACF;QACF;QACA,SAAS,CAAC;YACR,YAAY;gBAAE;gBAAO,cAAc;YAAO;YAC1C,QAAQ,OAAO,GAAG;QACpB;IACF;IAEA,mBAAmB;IACnB,MAAM,GAAG,aAAa,GAAG,CAAA,GAAA,6HAAA,CAAA,gBAAa,AAAD,EAAE;QACrC,YAAY,CAAC;YACX,QAAQ,GAAG,CAAC,CAAC,gBAAgB,EAAE,SAAS,OAAO,CAAC,GAAG,EAAE,SAAS,cAAc,CAAC,OAAO,CAAC,GAAG,GAAG,CAAC;YAE5F,YAAY;gBACV,cAAc,SAAS,OAAO;gBAC9B,cAAc;YAChB;YAEA,QAAQ,UAAU,GAAG,SAAS,OAAO;YAErC,cAAc;YACd,YAAY,cAAc,CAAC,SAAS,OAAO;QAC7C;QACA,SAAS,CAAC;YACR,YAAY;gBAAE;gBAAO,cAAc;YAAO;YAC1C,QAAQ,OAAO,GAAG;QACpB;QACA,iBAAiB;QACjB,kBAAkB;IACpB;IAEA,WAAW;IACX,MAAM,GAAG,YAAY,GAAG,CAAA,GAAA,sHAAA,CAAA,SAAM,AAAD,EAAE;QAC7B,aAAa,CAAC;YACZ,MAAM,eAAe,YAAY,GAAG;YACpC,WAAW,OAAO,CAAC,UAAU,GAAG,OAAO,cAAc;YACrD,WAAW,OAAO,CAAC,YAAY,GAAG,eAAe,qBAAqB,OAAO;YAE7E,QAAQ,GAAG,CAAC,CAAC,eAAe,EAAE,OAAO,cAAc,CAAC,OAAO,CAAC,GAAG,GAAG,CAAC;YACnE,QAAQ,GAAG,CAAC,CAAC,wBAAwB,EAAE,WAAW,OAAO,CAAC,YAAY,EAAE,QAAQ,GAAG,EAAE,CAAC;YAEtF,iBAAiB;YACjB,IAAI,WAAW,OAAO,CAAC,YAAY,EAAE;gBACnC,MAAM,UAAiC;oBACrC,YAAY,WAAW,OAAO,CAAC,UAAU,IAAI;oBAC7C,YAAY,WAAW,OAAO,CAAC,UAAU,IAAI;oBAC7C,YAAY,WAAW,OAAO,CAAC,UAAU,IAAI;oBAC7C,cAAc,WAAW,OAAO,CAAC,YAAY;oBAC7C,aAAa,WAAW,OAAO,CAAC,WAAW,IAAI;gBACjD;gBAEA,6BAA6B;gBAC7B,gIAAA,CAAA,qBAAkB,CAAC,aAAa,CAAC;oBAC/B,aAAa,QAAQ,WAAW;oBAChC,YAAY,QAAQ,UAAU;oBAC9B,YAAY,QAAQ,UAAU;oBAC9B,YAAY,QAAQ,UAAU;oBAC9B,cAAc,QAAQ,YAAY;gBACpC;gBAEA,QAAQ,SAAS,GAAG;YACtB;YAEA,mCAAmC;YACnC,WAAW;gBACT,YAAY;oBAAE,cAAc;gBAAO;YACrC,GAAG;QACL;QACA,SAAS,CAAC;YACR,YAAY;gBAAE;gBAAO,cAAc;YAAO;YAC1C,QAAQ,OAAO,GAAG;QACpB;QACA,UAAU;IACZ;IAEA,4BAA4B;IAC5B,MAAM,aAAa,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,OAAO;QACpC,IAAI;YACF,YAAY;gBAAE,OAAO;YAAK;YAE1B,4BAA4B;YAC5B,MAAM,QAAQ,GAAG,CAAC;gBAChB,cAAc,UAAU;gBACxB,gBAAgB,UAAU;gBAC1B,YAAY,UAAU;aACvB;YAED,oBAAoB;YACpB,aAAa,UAAU,CAAC;YAExB,YAAY;gBAAE,eAAe;YAAK;YAElC,oCAAoC;YACpC,IAAI,QAAQ,kBAAkB,EAAE;gBAC9B,cAAc,cAAc;gBAC5B,YAAY;oBACV,aAAa;oBACb,cAAc;oBACd,OAAO;gBACT;YACF;QAEF,EAAE,OAAO,OAAO;YACd,MAAM,eAAe,iBAAiB,QAAQ,MAAM,OAAO,GAAG;YAC9D,YAAY;gBAAE,OAAO;gBAAc,eAAe;YAAM;YACxD,QAAQ,OAAO,GAAG;QACpB;IACF,GAAG;QAAC;QAAe;QAAiB;QAAa;QAAc;QAAS;KAAY;IAEpF,MAAM,iBAAiB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QACjC,IAAI,CAAC,MAAM,aAAa,EAAE;YACxB,YAAY;gBAAE,OAAO;YAAkC;YACvD;QACF;QAEA,cAAc,cAAc;QAC5B,YAAY;YACV,aAAa;YACb,cAAc;YACd,OAAO;QACT;IACF,GAAG;QAAC,MAAM,aAAa;QAAE;QAAe;KAAY;IAEpD,MAAM,gBAAgB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QAChC,cAAc,aAAa;QAC3B,YAAY;YACV,aAAa;YACb,cAAc;QAChB;IACF,GAAG;QAAC;QAAe;KAAY;IAE/B,MAAM,kBAAkB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,OAAO;QACzC,IAAI,CAAC,MAAM,aAAa,EAAE;YACxB,MAAM,IAAI,MAAM;QAClB;QAEA,YAAY;YAAE,cAAc;QAAW;QAEvC,IAAI;YACF,MAAM,WAAW,MAAM,aAAa,WAAW,CAAC;YAChD,IAAI,UAAU;gBACZ,YAAY;oBACV,mBAAmB;oBACnB,cAAc,SAAS,OAAO;oBAC9B,cAAc;gBAChB;gBAEA,sBAAsB;gBACtB,MAAM,YAAY,cAAc,CAAC,SAAS,OAAO;YACnD;QACF,EAAE,OAAO,OAAO;YACd,MAAM,eAAe,iBAAiB,QAAQ,MAAM,OAAO,GAAG;YAC9D,YAAY;gBAAE,OAAO;gBAAc,cAAc;YAAO;YACxD,MAAM;QACR;IACF,GAAG;QAAC,MAAM,aAAa;QAAE;QAAc;QAAa;KAAY;IAEhE,MAAM,QAAQ,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QACxB,cAAc,aAAa;QAC3B,gBAAgB,KAAK;QACrB,aAAa,YAAY;QACzB,YAAY,KAAK;QAEjB,SAAS;YACP,eAAe;YACf,aAAa;YACb,cAAc;YACd,cAAc;YACd,OAAO;YACP,QAAQ;YACR,mBAAmB;YACnB,cAAc;QAChB;IACF,GAAG;QAAC;QAAe;QAAiB;QAAc;KAAY;IAE9D,iDAAiD;IACjD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,eAAe;YAAC;YAAgB;YAAY;SAAW,CAAC,QAAQ,CAAC,MAAM,YAAY;QACzF,IAAI,MAAM,YAAY,KAAK,cAAc;YACvC,YAAY;gBAAE;YAAa;QAC7B;IACF,GAAG;QAAC,MAAM,YAAY;QAAE,MAAM,YAAY;QAAE;KAAY;IAExD,MAAM,WAAmC;QACvC;QACA;QACA;QACA;QACA;IACF;IAEA,OAAO;QAAC;QAAO;KAAS;AAC1B", "debugId": null}}, {"offset": {"line": 1573, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Web_dev/coding_jr/voice-assistant/src/lib/serviceWorker.ts"], "sourcesContent": ["export interface CacheStatus {\n  staticCached: number;\n  modelsCached: number;\n  totalModels: number;\n  isReady: boolean;\n}\n\nexport class ServiceWorkerManager {\n  private registration: ServiceWorkerRegistration | null = null;\n  private cacheStatusCallbacks: ((status: CacheStatus) => void)[] = [];\n\n  async register(): Promise<ServiceWorkerRegistration | null> {\n    if (typeof window === 'undefined' || !('serviceWorker' in navigator)) {\n      console.warn('Service Worker not supported');\n      return null;\n    }\n\n    try {\n      this.registration = await navigator.serviceWorker.register('/sw.js');\n      console.log('Service Worker registered successfully');\n\n      // Listen for updates\n      this.registration.addEventListener('updatefound', () => {\n        const newWorker = this.registration?.installing;\n        if (newWorker) {\n          newWorker.addEventListener('statechange', () => {\n            if (newWorker.state === 'installed' && navigator.serviceWorker.controller) {\n              console.log('New Service Worker available');\n              // Optionally notify user about update\n            }\n          });\n        }\n      });\n\n      // Listen for messages from service worker\n      navigator.serviceWorker.addEventListener('message', (event) => {\n        if (event.data?.type === 'CACHE_STATUS') {\n          const status: CacheStatus = {\n            ...event.data,\n            isReady: event.data.modelsCached === event.data.totalModels\n          };\n          this.cacheStatusCallbacks.forEach(callback => callback(status));\n        }\n      });\n\n      return this.registration;\n    } catch (error) {\n      console.error('Service Worker registration failed:', error);\n      return null;\n    }\n  }\n\n  async getCacheStatus(): Promise<CacheStatus | null> {\n    if (!this.registration || !navigator.serviceWorker.controller) {\n      return null;\n    }\n\n    return new Promise((resolve) => {\n      const channel = new MessageChannel();\n      channel.port1.onmessage = (event) => {\n        if (event.data?.type === 'CACHE_STATUS') {\n          const status: CacheStatus = {\n            ...event.data,\n            isReady: event.data.modelsCached === event.data.totalModels\n          };\n          resolve(status);\n        }\n      };\n\n      navigator.serviceWorker.controller.postMessage(\n        { type: 'GET_CACHE_STATUS' },\n        [channel.port2]\n      );\n    });\n  }\n\n  onCacheStatusUpdate(callback: (status: CacheStatus) => void): () => void {\n    this.cacheStatusCallbacks.push(callback);\n    \n    // Return unsubscribe function\n    return () => {\n      const index = this.cacheStatusCallbacks.indexOf(callback);\n      if (index > -1) {\n        this.cacheStatusCallbacks.splice(index, 1);\n      }\n    };\n  }\n\n  async skipWaiting(): Promise<void> {\n    if (this.registration?.waiting) {\n      this.registration.waiting.postMessage({ type: 'SKIP_WAITING' });\n    }\n  }\n\n  async unregister(): Promise<boolean> {\n    if (this.registration) {\n      return await this.registration.unregister();\n    }\n    return false;\n  }\n}\n\n// Singleton instance\nexport const serviceWorkerManager = new ServiceWorkerManager();\n"], "names": [], "mappings": ";;;;AAOO,MAAM;IACH,eAAiD,KAAK;IACtD,uBAA0D,EAAE,CAAC;IAErE,MAAM,WAAsD;QAC1D,IAAI,gBAAkB,eAAe,CAAC,CAAC,mBAAmB,SAAS,GAAG;YACpE,QAAQ,IAAI,CAAC;YACb,OAAO;QACT;;;IAmCF;IAEA,MAAM,iBAA8C;QAClD,IAAI,CAAC,IAAI,CAAC,YAAY,IAAI,CAAC,UAAU,aAAa,CAAC,UAAU,EAAE;YAC7D,OAAO;QACT;QAEA,OAAO,IAAI,QAAQ,CAAC;YAClB,MAAM,UAAU,IAAI;YACpB,QAAQ,KAAK,CAAC,SAAS,GAAG,CAAC;gBACzB,IAAI,MAAM,IAAI,EAAE,SAAS,gBAAgB;oBACvC,MAAM,SAAsB;wBAC1B,GAAG,MAAM,IAAI;wBACb,SAAS,MAAM,IAAI,CAAC,YAAY,KAAK,MAAM,IAAI,CAAC,WAAW;oBAC7D;oBACA,QAAQ;gBACV;YACF;YAEA,UAAU,aAAa,CAAC,UAAU,CAAC,WAAW,CAC5C;gBAAE,MAAM;YAAmB,GAC3B;gBAAC,QAAQ,KAAK;aAAC;QAEnB;IACF;IAEA,oBAAoB,QAAuC,EAAc;QACvE,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC;QAE/B,8BAA8B;QAC9B,OAAO;YACL,MAAM,QAAQ,IAAI,CAAC,oBAAoB,CAAC,OAAO,CAAC;YAChD,IAAI,QAAQ,CAAC,GAAG;gBACd,IAAI,CAAC,oBAAoB,CAAC,MAAM,CAAC,OAAO;YAC1C;QACF;IACF;IAEA,MAAM,cAA6B;QACjC,IAAI,IAAI,CAAC,YAAY,EAAE,SAAS;YAC9B,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,WAAW,CAAC;gBAAE,MAAM;YAAe;QAC/D;IACF;IAEA,MAAM,aAA+B;QACnC,IAAI,IAAI,CAAC,YAAY,EAAE;YACrB,OAAO,MAAM,IAAI,CAAC,YAAY,CAAC,UAAU;QAC3C;QACA,OAAO;IACT;AACF;AAGO,MAAM,uBAAuB,IAAI", "debugId": null}}, {"offset": {"line": 1639, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Web_dev/coding_jr/voice-assistant/src/components/VoiceAssistant.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState, useEffect } from 'react';\nimport { useVoiceAssistant, VoiceAssistantMetrics } from '@/hooks/useVoiceAssistant';\nimport { serviceWorkerManager } from '@/lib/serviceWorker';\n\nconst VoiceAssistant: React.FC = () => {\n  const [apiKey, setApiKey] = useState('');\n  const [showApiKeyInput, setShowApiKeyInput] = useState(true);\n  const [metrics, setMetrics] = useState<VoiceAssistantMetrics | null>(null);\n  const [cacheStatus, setCacheStatus] = useState<{\n    modelsCached: number;\n    totalModels: number;\n    isReady: boolean;\n  } | null>(null);\n\n  const [voiceState, voiceControls] = useVoiceAssistant({\n    onStageChange: (stage) => {\n      console.log('Stage changed to:', stage);\n    },\n    onTranscription: (text) => {\n      console.log('Transcription:', text);\n    },\n    onResponse: (text) => {\n      console.log('Response:', text);\n    },\n    onMetrics: (newMetrics) => {\n      setMetrics(newMetrics);\n      console.log('Pipeline metrics:', newMetrics);\n    },\n    onError: (error) => {\n      console.error('Voice assistant error:', error);\n    },\n  });\n\n  // Initialize service worker and check cache status\n  useEffect(() => {\n    const initServiceWorker = async () => {\n      await serviceWorkerManager.register();\n      const status = await serviceWorkerManager.getCacheStatus();\n      setCacheStatus(status);\n    };\n\n    initServiceWorker();\n\n    // Update cache status periodically\n    const interval = setInterval(async () => {\n      const status = await serviceWorkerManager.getCacheStatus();\n      setCacheStatus(status);\n    }, 5000);\n\n    return () => clearInterval(interval);\n  }, []);\n\n  const handleInitialize = async () => {\n    if (!apiKey.trim()) {\n      alert('Please enter your OpenAI API key');\n      return;\n    }\n\n    try {\n      await voiceControls.initialize(apiKey);\n      setShowApiKeyInput(false);\n    } catch (error) {\n      console.error('Initialization failed:', error);\n    }\n  };\n\n  const handleStartListening = () => {\n    voiceControls.startListening();\n  };\n\n  const handleStopListening = () => {\n    voiceControls.stopListening();\n  };\n\n  const getStageColor = (stage: string) => {\n    switch (stage) {\n      case 'listening': return 'text-green-400';\n      case 'transcribing': return 'text-blue-400';\n      case 'thinking': return 'text-yellow-400';\n      case 'speaking': return 'text-purple-400';\n      default: return 'text-gray-400';\n    }\n  };\n\n  const getStageIcon = (stage: string) => {\n    switch (stage) {\n      case 'listening': return '🎤';\n      case 'transcribing': return '📝';\n      case 'thinking': return '🤔';\n      case 'speaking': return '🔊';\n      default: return '⭕';\n    }\n  };\n\n  if (showApiKeyInput) {\n    return (\n      <div className=\"min-h-screen flex items-center justify-center p-4\">\n        <div className=\"bg-gray-800 rounded-lg p-8 max-w-md w-full\">\n          <h1 className=\"text-2xl font-bold mb-6 text-center\">Voice Assistant</h1>\n          \n          <div className=\"mb-6\">\n            <label htmlFor=\"apiKey\" className=\"block text-sm font-medium mb-2\">\n              OpenAI API Key\n            </label>\n            <input\n              id=\"apiKey\"\n              type=\"password\"\n              value={apiKey}\n              onChange={(e) => setApiKey(e.target.value)}\n              placeholder=\"sk-...\"\n              className=\"w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n            />\n          </div>\n\n          {cacheStatus && (\n            <div className=\"mb-6 p-3 bg-gray-700 rounded-md\">\n              <h3 className=\"text-sm font-medium mb-2\">Cache Status</h3>\n              <div className=\"text-xs text-gray-300\">\n                <div>Models: {cacheStatus.modelsCached}/{cacheStatus.totalModels}</div>\n                <div>Ready: {cacheStatus.isReady ? '✅' : '⏳'}</div>\n              </div>\n            </div>\n          )}\n\n          <button\n            onClick={handleInitialize}\n            disabled={!apiKey.trim()}\n            className=\"w-full bg-blue-600 hover:bg-blue-700 disabled:bg-gray-600 disabled:cursor-not-allowed px-4 py-2 rounded-md font-medium transition-colors\"\n          >\n            Initialize Voice Assistant\n          </button>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"min-h-screen flex flex-col items-center justify-center p-4\">\n      <div className=\"bg-gray-800 rounded-lg p-8 max-w-2xl w-full\">\n        <h1 className=\"text-3xl font-bold mb-8 text-center\">Voice Assistant</h1>\n\n        {/* Status Display */}\n        <div className=\"text-center mb-8\">\n          <div className={`text-6xl mb-4 ${getStageColor(voiceState.currentStage)}`}>\n            {getStageIcon(voiceState.currentStage)}\n          </div>\n          <div className={`text-xl font-medium ${getStageColor(voiceState.currentStage)}`}>\n            {voiceState.currentStage.charAt(0).toUpperCase() + voiceState.currentStage.slice(1)}\n          </div>\n          \n          {voiceState.isListening && (\n            <div className=\"mt-2\">\n              <div className=\"w-full bg-gray-700 rounded-full h-2\">\n                <div \n                  className=\"bg-green-400 h-2 rounded-full transition-all duration-100\"\n                  style={{ width: `${Math.min(voiceState.volume * 100, 100)}%` }}\n                />\n              </div>\n              <div className=\"text-xs text-gray-400 mt-1\">\n                Volume: {(voiceState.volume * 100).toFixed(0)}%\n              </div>\n            </div>\n          )}\n        </div>\n\n        {/* Controls */}\n        <div className=\"flex justify-center gap-4 mb-8\">\n          {!voiceState.isListening ? (\n            <button\n              onClick={handleStartListening}\n              disabled={!voiceState.isInitialized || voiceState.isProcessing}\n              className=\"bg-green-600 hover:bg-green-700 disabled:bg-gray-600 disabled:cursor-not-allowed px-6 py-3 rounded-full font-medium transition-colors flex items-center gap-2\"\n            >\n              🎤 Start Listening\n            </button>\n          ) : (\n            <button\n              onClick={handleStopListening}\n              className=\"bg-red-600 hover:bg-red-700 px-6 py-3 rounded-full font-medium transition-colors flex items-center gap-2\"\n            >\n              ⏹️ Stop Listening\n            </button>\n          )}\n        </div>\n\n        {/* Conversation Display */}\n        <div className=\"space-y-4 mb-6\">\n          {voiceState.lastTranscription && (\n            <div className=\"bg-blue-900/30 p-4 rounded-lg\">\n              <div className=\"text-sm text-blue-300 mb-1\">You said:</div>\n              <div className=\"text-white\">{voiceState.lastTranscription}</div>\n            </div>\n          )}\n          \n          {voiceState.lastResponse && (\n            <div className=\"bg-purple-900/30 p-4 rounded-lg\">\n              <div className=\"text-sm text-purple-300 mb-1\">Assistant:</div>\n              <div className=\"text-white\">{voiceState.lastResponse}</div>\n            </div>\n          )}\n        </div>\n\n        {/* Metrics Display */}\n        {metrics && (\n          <div className=\"bg-gray-700 p-4 rounded-lg\">\n            <h3 className=\"text-sm font-medium mb-3\">Performance Metrics</h3>\n            <div className=\"grid grid-cols-2 gap-4 text-xs\">\n              <div>\n                <div className=\"text-gray-400\">STT Latency</div>\n                <div className=\"font-mono\">{metrics.sttLatency.toFixed(0)}ms</div>\n              </div>\n              <div>\n                <div className=\"text-gray-400\">LLM Latency</div>\n                <div className=\"font-mono\">{metrics.llmLatency.toFixed(0)}ms</div>\n              </div>\n              <div>\n                <div className=\"text-gray-400\">TTS Latency</div>\n                <div className=\"font-mono\">{metrics.ttsLatency.toFixed(0)}ms</div>\n              </div>\n              <div>\n                <div className=\"text-gray-400\">Total Latency</div>\n                <div className={`font-mono ${metrics.totalLatency < 1200 ? 'text-green-400' : 'text-yellow-400'}`}>\n                  {metrics.totalLatency.toFixed(0)}ms\n                </div>\n              </div>\n            </div>\n            <div className=\"mt-2 text-xs text-gray-400\">\n              Audio Length: {metrics.audioLength.toFixed(1)}s\n            </div>\n          </div>\n        )}\n\n        {/* Error Display */}\n        {voiceState.error && (\n          <div className=\"bg-red-900/30 border border-red-600 p-4 rounded-lg mt-4\">\n            <div className=\"text-red-300 text-sm\">Error: {voiceState.error}</div>\n          </div>\n        )}\n\n        {/* Reset Button */}\n        <div className=\"text-center mt-6\">\n          <button\n            onClick={() => {\n              voiceControls.reset();\n              setShowApiKeyInput(true);\n              setMetrics(null);\n            }}\n            className=\"text-gray-400 hover:text-white text-sm underline\"\n          >\n            Reset & Change API Key\n          </button>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default VoiceAssistant;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAJA;;;;;AAMA,MAAM,iBAA2B;IAC/B,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAgC;IACrE,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAInC;IAEV,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,iIAAA,CAAA,oBAAiB,AAAD,EAAE;QACpD,eAAe,CAAC;YACd,QAAQ,GAAG,CAAC,qBAAqB;QACnC;QACA,iBAAiB,CAAC;YAChB,QAAQ,GAAG,CAAC,kBAAkB;QAChC;QACA,YAAY,CAAC;YACX,QAAQ,GAAG,CAAC,aAAa;QAC3B;QACA,WAAW,CAAC;YACV,WAAW;YACX,QAAQ,GAAG,CAAC,qBAAqB;QACnC;QACA,SAAS,CAAC;YACR,QAAQ,KAAK,CAAC,0BAA0B;QAC1C;IACF;IAEA,mDAAmD;IACnD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,oBAAoB;YACxB,MAAM,2HAAA,CAAA,uBAAoB,CAAC,QAAQ;YACnC,MAAM,SAAS,MAAM,2HAAA,CAAA,uBAAoB,CAAC,cAAc;YACxD,eAAe;QACjB;QAEA;QAEA,mCAAmC;QACnC,MAAM,WAAW,YAAY;YAC3B,MAAM,SAAS,MAAM,2HAAA,CAAA,uBAAoB,CAAC,cAAc;YACxD,eAAe;QACjB,GAAG;QAEH,OAAO,IAAM,cAAc;IAC7B,GAAG,EAAE;IAEL,MAAM,mBAAmB;QACvB,IAAI,CAAC,OAAO,IAAI,IAAI;YAClB,MAAM;YACN;QACF;QAEA,IAAI;YACF,MAAM,cAAc,UAAU,CAAC;YAC/B,mBAAmB;QACrB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,0BAA0B;QAC1C;IACF;IAEA,MAAM,uBAAuB;QAC3B,cAAc,cAAc;IAC9B;IAEA,MAAM,sBAAsB;QAC1B,cAAc,aAAa;IAC7B;IAEA,MAAM,gBAAgB,CAAC;QACrB,OAAQ;YACN,KAAK;gBAAa,OAAO;YACzB,KAAK;gBAAgB,OAAO;YAC5B,KAAK;gBAAY,OAAO;YACxB,KAAK;gBAAY,OAAO;YACxB;gBAAS,OAAO;QAClB;IACF;IAEA,MAAM,eAAe,CAAC;QACpB,OAAQ;YACN,KAAK;gBAAa,OAAO;YACzB,KAAK;gBAAgB,OAAO;YAC5B,KAAK;gBAAY,OAAO;YACxB,KAAK;gBAAY,OAAO;YACxB;gBAAS,OAAO;QAClB;IACF;IAEA,IAAI,iBAAiB;QACnB,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAAsC;;;;;;kCAEpD,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAM,SAAQ;gCAAS,WAAU;0CAAiC;;;;;;0CAGnE,8OAAC;gCACC,IAAG;gCACH,MAAK;gCACL,OAAO;gCACP,UAAU,CAAC,IAAM,UAAU,EAAE,MAAM,CAAC,KAAK;gCACzC,aAAY;gCACZ,WAAU;;;;;;;;;;;;oBAIb,6BACC,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAA2B;;;;;;0CACzC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;;4CAAI;4CAAS,YAAY,YAAY;4CAAC;4CAAE,YAAY,WAAW;;;;;;;kDAChE,8OAAC;;4CAAI;4CAAQ,YAAY,OAAO,GAAG,MAAM;;;;;;;;;;;;;;;;;;;kCAK/C,8OAAC;wBACC,SAAS;wBACT,UAAU,CAAC,OAAO,IAAI;wBACtB,WAAU;kCACX;;;;;;;;;;;;;;;;;IAMT;IAEA,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAG,WAAU;8BAAsC;;;;;;8BAGpD,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAW,CAAC,cAAc,EAAE,cAAc,WAAW,YAAY,GAAG;sCACtE,aAAa,WAAW,YAAY;;;;;;sCAEvC,8OAAC;4BAAI,WAAW,CAAC,oBAAoB,EAAE,cAAc,WAAW,YAAY,GAAG;sCAC5E,WAAW,YAAY,CAAC,MAAM,CAAC,GAAG,WAAW,KAAK,WAAW,YAAY,CAAC,KAAK,CAAC;;;;;;wBAGlF,WAAW,WAAW,kBACrB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCACC,WAAU;wCACV,OAAO;4CAAE,OAAO,GAAG,KAAK,GAAG,CAAC,WAAW,MAAM,GAAG,KAAK,KAAK,CAAC,CAAC;wCAAC;;;;;;;;;;;8CAGjE,8OAAC;oCAAI,WAAU;;wCAA6B;wCACjC,CAAC,WAAW,MAAM,GAAG,GAAG,EAAE,OAAO,CAAC;wCAAG;;;;;;;;;;;;;;;;;;;8BAOtD,8OAAC;oBAAI,WAAU;8BACZ,CAAC,WAAW,WAAW,iBACtB,8OAAC;wBACC,SAAS;wBACT,UAAU,CAAC,WAAW,aAAa,IAAI,WAAW,YAAY;wBAC9D,WAAU;kCACX;;;;;iFAID,8OAAC;wBACC,SAAS;wBACT,WAAU;kCACX;;;;;;;;;;;8BAOL,8OAAC;oBAAI,WAAU;;wBACZ,WAAW,iBAAiB,kBAC3B,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CAA6B;;;;;;8CAC5C,8OAAC;oCAAI,WAAU;8CAAc,WAAW,iBAAiB;;;;;;;;;;;;wBAI5D,WAAW,YAAY,kBACtB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CAA+B;;;;;;8CAC9C,8OAAC;oCAAI,WAAU;8CAAc,WAAW,YAAY;;;;;;;;;;;;;;;;;;gBAMzD,yBACC,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAA2B;;;;;;sCACzC,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;;sDACC,8OAAC;4CAAI,WAAU;sDAAgB;;;;;;sDAC/B,8OAAC;4CAAI,WAAU;;gDAAa,QAAQ,UAAU,CAAC,OAAO,CAAC;gDAAG;;;;;;;;;;;;;8CAE5D,8OAAC;;sDACC,8OAAC;4CAAI,WAAU;sDAAgB;;;;;;sDAC/B,8OAAC;4CAAI,WAAU;;gDAAa,QAAQ,UAAU,CAAC,OAAO,CAAC;gDAAG;;;;;;;;;;;;;8CAE5D,8OAAC;;sDACC,8OAAC;4CAAI,WAAU;sDAAgB;;;;;;sDAC/B,8OAAC;4CAAI,WAAU;;gDAAa,QAAQ,UAAU,CAAC,OAAO,CAAC;gDAAG;;;;;;;;;;;;;8CAE5D,8OAAC;;sDACC,8OAAC;4CAAI,WAAU;sDAAgB;;;;;;sDAC/B,8OAAC;4CAAI,WAAW,CAAC,UAAU,EAAE,QAAQ,YAAY,GAAG,OAAO,mBAAmB,mBAAmB;;gDAC9F,QAAQ,YAAY,CAAC,OAAO,CAAC;gDAAG;;;;;;;;;;;;;;;;;;;sCAIvC,8OAAC;4BAAI,WAAU;;gCAA6B;gCAC3B,QAAQ,WAAW,CAAC,OAAO,CAAC;gCAAG;;;;;;;;;;;;;gBAMnD,WAAW,KAAK,kBACf,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;4BAAuB;4BAAQ,WAAW,KAAK;;;;;;;;;;;;8BAKlE,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBACC,SAAS;4BACP,cAAc,KAAK;4BACnB,mBAAmB;4BACnB,WAAW;wBACb;wBACA,WAAU;kCACX;;;;;;;;;;;;;;;;;;;;;;AAOX;uCAEe", "debugId": null}}, {"offset": {"line": 2210, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Web_dev/coding_jr/voice-assistant/src/app/page.tsx"], "sourcesContent": ["'use client';\n\nimport VoiceAssistant from '@/components/VoiceAssistant';\n\nexport default function Home() {\n  return (\n    <div className=\"min-h-screen bg-gradient-to-br from-gray-900 to-black text-white\">\n      <VoiceAssistant />\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AAIe,SAAS;IACtB,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC,oIAAA,CAAA,UAAc;;;;;;;;;;AAGrB", "debugId": null}}]}