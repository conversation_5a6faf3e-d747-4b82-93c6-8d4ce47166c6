(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push([typeof document === "object" ? document.currentScript : undefined, {

"[project]/src/lib/audioManager.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "AudioManager": ()=>AudioManager
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$swc$2f$helpers$2f$esm$2f$_define_property$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@swc/helpers/esm/_define_property.js [app-client] (ecmascript)");
;
class AudioManager {
    async initialize() {
        try {
            // Request microphone access
            this.mediaStream = await navigator.mediaDevices.getUserMedia({
                audio: {
                    sampleRate: 16000,
                    channelCount: 1,
                    echoCancellation: true,
                    noiseSuppression: true,
                    autoGainControl: true
                }
            });
            // Create audio context
            this.audioContext = new AudioContext({
                sampleRate: 16000
            });
            // Create analyser for volume and VAD
            this.analyser = this.audioContext.createAnalyser();
            this.analyser.fftSize = 2048;
            this.analyser.smoothingTimeConstant = 0.8;
            // Create source from media stream
            const source = this.audioContext.createMediaStreamSource(this.mediaStream);
            source.connect(this.analyser);
            // Try to use AudioWorklet for better performance
            try {
                await this.audioContext.audioWorklet.addModule('/audio-processor.js');
                this.workletNode = new AudioWorkletNode(this.audioContext, 'audio-processor');
                this.workletNode.port.onmessage = (event)=>{
                    const { audioData, timestamp } = event.data;
                    this.handleAudioData(audioData, timestamp);
                };
                source.connect(this.workletNode);
            } catch (e) {
                console.warn('AudioWorklet not available, falling back to ScriptProcessor');
                // Fallback to ScriptProcessor
                this.processor = this.audioContext.createScriptProcessor(this.bufferSize, 1, 1);
                this.processor.onaudioprocess = (event)=>{
                    const inputBuffer = event.inputBuffer;
                    const audioData = inputBuffer.getChannelData(0);
                    this.handleAudioData(new Float32Array(audioData), this.audioContext.currentTime);
                };
                source.connect(this.processor);
                this.processor.connect(this.audioContext.destination);
            }
            console.log('Audio manager initialized successfully');
        } catch (error) {
            var _this_events_onError, _this_events;
            const audioError = new Error("Failed to initialize audio: ".concat(error));
            (_this_events_onError = (_this_events = this.events).onError) === null || _this_events_onError === void 0 ? void 0 : _this_events_onError.call(_this_events, audioError);
            throw audioError;
        }
    }
    handleAudioData(audioData, timestamp) {
        var _this_events_onVolumeChange, _this_events, _this_audioContext, _this_events_onAudioChunk, _this_events1;
        if (!this.isRecording) return;
        // Calculate RMS for volume and VAD
        const rms = this.calculateRMS(audioData);
        (_this_events_onVolumeChange = (_this_events = this.events).onVolumeChange) === null || _this_events_onVolumeChange === void 0 ? void 0 : _this_events_onVolumeChange.call(_this_events, rms);
        // Voice Activity Detection
        this.performVAD(rms);
        // Buffer audio data
        this.audioBuffer.push(new Float32Array(audioData));
        // Send audio chunk
        const chunk = {
            data: new Float32Array(audioData),
            timestamp,
            sampleRate: ((_this_audioContext = this.audioContext) === null || _this_audioContext === void 0 ? void 0 : _this_audioContext.sampleRate) || 16000
        };
        (_this_events_onAudioChunk = (_this_events1 = this.events).onAudioChunk) === null || _this_events_onAudioChunk === void 0 ? void 0 : _this_events_onAudioChunk.call(_this_events1, chunk);
        // Send buffered audio when we have enough
        if (this.audioBuffer.length >= 10) {
            this.sendBufferedAudio();
        }
    }
    calculateRMS(audioData) {
        let sum = 0;
        for(let i = 0; i < audioData.length; i++){
            sum += audioData[i] * audioData[i];
        }
        return Math.sqrt(sum / audioData.length);
    }
    performVAD(rms) {
        const isSpeechFrame = rms > this.vadThreshold;
        if (isSpeechFrame) {
            this.vadSpeechFrames++;
            this.vadSilenceFrames = 0;
            if (!this.isSpeaking && this.vadSpeechFrames >= this.vadSpeechThreshold) {
                var _this_events_onSpeechStart, _this_events;
                this.isSpeaking = true;
                (_this_events_onSpeechStart = (_this_events = this.events).onSpeechStart) === null || _this_events_onSpeechStart === void 0 ? void 0 : _this_events_onSpeechStart.call(_this_events);
            }
        } else {
            this.vadSilenceFrames++;
            this.vadSpeechFrames = 0;
            if (this.isSpeaking && this.vadSilenceFrames >= this.vadSilenceThreshold) {
                var _this_events_onSpeechEnd, _this_events1;
                this.isSpeaking = false;
                (_this_events_onSpeechEnd = (_this_events1 = this.events).onSpeechEnd) === null || _this_events_onSpeechEnd === void 0 ? void 0 : _this_events_onSpeechEnd.call(_this_events1);
                this.sendBufferedAudio(); // Send remaining audio
            }
        }
    }
    sendBufferedAudio() {
        var _this_audioContext, _this_audioContext1, _this_events_onAudioChunk, _this_events;
        if (this.audioBuffer.length === 0) return;
        // Concatenate all buffered audio
        const totalLength = this.audioBuffer.reduce((sum, buffer)=>sum + buffer.length, 0);
        const concatenated = new Float32Array(totalLength);
        let offset = 0;
        for (const buffer of this.audioBuffer){
            concatenated.set(buffer, offset);
            offset += buffer.length;
        }
        // Clear buffer
        this.audioBuffer = [];
        // Send as chunk
        const chunk = {
            data: concatenated,
            timestamp: ((_this_audioContext = this.audioContext) === null || _this_audioContext === void 0 ? void 0 : _this_audioContext.currentTime) || Date.now(),
            sampleRate: ((_this_audioContext1 = this.audioContext) === null || _this_audioContext1 === void 0 ? void 0 : _this_audioContext1.sampleRate) || 16000
        };
        (_this_events_onAudioChunk = (_this_events = this.events).onAudioChunk) === null || _this_events_onAudioChunk === void 0 ? void 0 : _this_events_onAudioChunk.call(_this_events, chunk);
    }
    startRecording() {
        if (!this.audioContext) {
            throw new Error('Audio manager not initialized');
        }
        if (this.audioContext.state === 'suspended') {
            this.audioContext.resume();
        }
        this.isRecording = true;
        this.audioBuffer = [];
        console.log('Recording started');
    }
    stopRecording() {
        this.isRecording = false;
        this.sendBufferedAudio(); // Send any remaining audio
        console.log('Recording stopped');
    }
    setVADThreshold(threshold) {
        this.vadThreshold = threshold;
    }
    getVADThreshold() {
        return this.vadThreshold;
    }
    isCurrentlyRecording() {
        return this.isRecording;
    }
    isCurrentlySpeaking() {
        return this.isSpeaking;
    }
    async cleanup() {
        this.stopRecording();
        if (this.processor) {
            this.processor.disconnect();
            this.processor = null;
        }
        if (this.workletNode) {
            this.workletNode.disconnect();
            this.workletNode = null;
        }
        if (this.analyser) {
            this.analyser.disconnect();
            this.analyser = null;
        }
        if (this.audioContext) {
            await this.audioContext.close();
            this.audioContext = null;
        }
        if (this.mediaStream) {
            this.mediaStream.getTracks().forEach((track)=>track.stop());
            this.mediaStream = null;
        }
        console.log('Audio manager cleaned up');
    }
    constructor(events = {}){
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$swc$2f$helpers$2f$esm$2f$_define_property$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["_"])(this, "mediaStream", null);
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$swc$2f$helpers$2f$esm$2f$_define_property$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["_"])(this, "audioContext", null);
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$swc$2f$helpers$2f$esm$2f$_define_property$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["_"])(this, "analyser", null);
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$swc$2f$helpers$2f$esm$2f$_define_property$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["_"])(this, "processor", null);
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$swc$2f$helpers$2f$esm$2f$_define_property$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["_"])(this, "workletNode", null);
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$swc$2f$helpers$2f$esm$2f$_define_property$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["_"])(this, "isRecording", false);
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$swc$2f$helpers$2f$esm$2f$_define_property$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["_"])(this, "events", {});
        // Voice Activity Detection
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$swc$2f$helpers$2f$esm$2f$_define_property$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["_"])(this, "vadThreshold", 0.01);
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$swc$2f$helpers$2f$esm$2f$_define_property$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["_"])(this, "vadSilenceFrames", 0);
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$swc$2f$helpers$2f$esm$2f$_define_property$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["_"])(this, "vadSilenceThreshold", 30); // frames of silence before speech end
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$swc$2f$helpers$2f$esm$2f$_define_property$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["_"])(this, "vadSpeechFrames", 0);
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$swc$2f$helpers$2f$esm$2f$_define_property$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["_"])(this, "vadSpeechThreshold", 5); // frames of speech before speech start
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$swc$2f$helpers$2f$esm$2f$_define_property$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["_"])(this, "isSpeaking", false);
        // Audio buffer for streaming
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$swc$2f$helpers$2f$esm$2f$_define_property$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["_"])(this, "audioBuffer", []);
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$swc$2f$helpers$2f$esm$2f$_define_property$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["_"])(this, "bufferSize", 4096);
        this.events = events;
    }
}
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/hooks/useAudioRecording.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "useAudioRecording": ()=>useAudioRecording
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$audioManager$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/audioManager.ts [app-client] (ecmascript)");
var _s = __turbopack_context__.k.signature();
;
;
function useAudioRecording() {
    let options = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : {};
    _s();
    const audioManagerRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])(null);
    const [state, setState] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])({
        isInitialized: false,
        isRecording: false,
        isSpeaking: false,
        volume: 0,
        error: null
    });
    const updateState = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "useAudioRecording.useCallback[updateState]": (updates)=>{
            setState({
                "useAudioRecording.useCallback[updateState]": (prev)=>({
                        ...prev,
                        ...updates
                    })
            }["useAudioRecording.useCallback[updateState]"]);
        }
    }["useAudioRecording.useCallback[updateState]"], []);
    const initialize = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "useAudioRecording.useCallback[initialize]": async ()=>{
            try {
                updateState({
                    error: null
                });
                const events = {
                    onAudioChunk: {
                        "useAudioRecording.useCallback[initialize]": (chunk)=>{
                            var _options_onAudioChunk;
                            (_options_onAudioChunk = options.onAudioChunk) === null || _options_onAudioChunk === void 0 ? void 0 : _options_onAudioChunk.call(options, chunk);
                        }
                    }["useAudioRecording.useCallback[initialize]"],
                    onSpeechStart: {
                        "useAudioRecording.useCallback[initialize]": ()=>{
                            var _options_onSpeechStart;
                            updateState({
                                isSpeaking: true
                            });
                            (_options_onSpeechStart = options.onSpeechStart) === null || _options_onSpeechStart === void 0 ? void 0 : _options_onSpeechStart.call(options);
                        }
                    }["useAudioRecording.useCallback[initialize]"],
                    onSpeechEnd: {
                        "useAudioRecording.useCallback[initialize]": ()=>{
                            var _options_onSpeechEnd;
                            updateState({
                                isSpeaking: false
                            });
                            (_options_onSpeechEnd = options.onSpeechEnd) === null || _options_onSpeechEnd === void 0 ? void 0 : _options_onSpeechEnd.call(options);
                        }
                    }["useAudioRecording.useCallback[initialize]"],
                    onError: {
                        "useAudioRecording.useCallback[initialize]": (error)=>{
                            var _options_onError;
                            updateState({
                                error: error.message
                            });
                            console.error('Audio manager error:', error);
                            (_options_onError = options.onError) === null || _options_onError === void 0 ? void 0 : _options_onError.call(options, error);
                        }
                    }["useAudioRecording.useCallback[initialize]"],
                    onVolumeChange: {
                        "useAudioRecording.useCallback[initialize]": (volume)=>{
                            var _options_onVolumeChange;
                            updateState({
                                volume
                            });
                            (_options_onVolumeChange = options.onVolumeChange) === null || _options_onVolumeChange === void 0 ? void 0 : _options_onVolumeChange.call(options, volume);
                        }
                    }["useAudioRecording.useCallback[initialize]"]
                };
                audioManagerRef.current = new __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$audioManager$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["AudioManager"](events);
                await audioManagerRef.current.initialize();
                updateState({
                    isInitialized: true
                });
                if (options.autoStart) {
                    audioManagerRef.current.startRecording();
                    updateState({
                        isRecording: true,
                        error: null
                    });
                }
            } catch (error) {
                const errorMessage = error instanceof Error ? error.message : 'Unknown audio error';
                updateState({
                    error: errorMessage,
                    isInitialized: false
                });
            }
        }
    }["useAudioRecording.useCallback[initialize]"], [
        options,
        updateState
    ]);
    const startRecording = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "useAudioRecording.useCallback[startRecording]": ()=>{
            if (!audioManagerRef.current) {
                updateState({
                    error: 'Audio manager not initialized'
                });
                return;
            }
            try {
                audioManagerRef.current.startRecording();
                updateState({
                    isRecording: true,
                    error: null
                });
            } catch (error) {
                const errorMessage = error instanceof Error ? error.message : 'Failed to start recording';
                updateState({
                    error: errorMessage
                });
            }
        }
    }["useAudioRecording.useCallback[startRecording]"], [
        updateState
    ]);
    const stopRecording = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "useAudioRecording.useCallback[stopRecording]": ()=>{
            if (!audioManagerRef.current) {
                return;
            }
            audioManagerRef.current.stopRecording();
            updateState({
                isRecording: false,
                isSpeaking: false
            });
        }
    }["useAudioRecording.useCallback[stopRecording]"], [
        updateState
    ]);
    const cleanup = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "useAudioRecording.useCallback[cleanup]": async ()=>{
            if (audioManagerRef.current) {
                await audioManagerRef.current.cleanup();
                audioManagerRef.current = null;
            }
            setState({
                isInitialized: false,
                isRecording: false,
                isSpeaking: false,
                volume: 0,
                error: null
            });
        }
    }["useAudioRecording.useCallback[cleanup]"], []);
    const setVADThreshold = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "useAudioRecording.useCallback[setVADThreshold]": (threshold)=>{
            var _audioManagerRef_current;
            (_audioManagerRef_current = audioManagerRef.current) === null || _audioManagerRef_current === void 0 ? void 0 : _audioManagerRef_current.setVADThreshold(threshold);
        }
    }["useAudioRecording.useCallback[setVADThreshold]"], []);
    const getVADThreshold = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "useAudioRecording.useCallback[getVADThreshold]": ()=>{
            var _audioManagerRef_current;
            return ((_audioManagerRef_current = audioManagerRef.current) === null || _audioManagerRef_current === void 0 ? void 0 : _audioManagerRef_current.getVADThreshold()) || 0.01;
        }
    }["useAudioRecording.useCallback[getVADThreshold]"], []);
    // Cleanup on unmount
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "useAudioRecording.useEffect": ()=>{
            return ({
                "useAudioRecording.useEffect": ()=>{
                    cleanup();
                }
            })["useAudioRecording.useEffect"];
        }
    }["useAudioRecording.useEffect"], [
        cleanup
    ]);
    // Update recording state based on audio manager
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "useAudioRecording.useEffect": ()=>{
            const interval = setInterval({
                "useAudioRecording.useEffect.interval": ()=>{
                    if (audioManagerRef.current) {
                        const isRecording = audioManagerRef.current.isCurrentlyRecording();
                        const isSpeaking = audioManagerRef.current.isCurrentlySpeaking();
                        setState({
                            "useAudioRecording.useEffect.interval": (prev)=>{
                                if (prev.isRecording !== isRecording || prev.isSpeaking !== isSpeaking) {
                                    return {
                                        ...prev,
                                        isRecording,
                                        isSpeaking
                                    };
                                }
                                return prev;
                            }
                        }["useAudioRecording.useEffect.interval"]);
                    }
                }
            }["useAudioRecording.useEffect.interval"], 100);
            return ({
                "useAudioRecording.useEffect": ()=>clearInterval(interval)
            })["useAudioRecording.useEffect"];
        }
    }["useAudioRecording.useEffect"], []);
    const controls = {
        initialize,
        startRecording,
        stopRecording,
        cleanup,
        setVADThreshold,
        getVADThreshold
    };
    return [
        state,
        controls
    ];
}
_s(useAudioRecording, "jXswXLZN6Fu25Kp0DD3PSXa5VHw=");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/hooks/useWhisperSTT.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "useWhisperSTT": ()=>useWhisperSTT
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var _s = __turbopack_context__.k.signature();
;
function useWhisperSTT() {
    let options = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : {};
    _s();
    const workerRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])(null);
    const [state, setState] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])({
        isInitialized: false,
        isTranscribing: false,
        error: null,
        lastTranscription: null,
        initializationProgress: 0
    });
    const updateState = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "useWhisperSTT.useCallback[updateState]": (updates)=>{
            setState({
                "useWhisperSTT.useCallback[updateState]": (prev)=>({
                        ...prev,
                        ...updates
                    })
            }["useWhisperSTT.useCallback[updateState]"]);
        }
    }["useWhisperSTT.useCallback[updateState]"], []);
    const initialize = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "useWhisperSTT.useCallback[initialize]": async ()=>{
            try {
                updateState({
                    error: null,
                    initializationProgress: 0
                });
                // Create worker
                workerRef.current = new Worker('/whisper-worker.js');
                // Set up message handler
                workerRef.current.onmessage = ({
                    "useWhisperSTT.useCallback[initialize]": (event)=>{
                        const { type, ...data } = event.data;
                        switch(type){
                            case 'initialized':
                                if (data.success) {
                                    updateState({
                                        isInitialized: true,
                                        initializationProgress: 100,
                                        error: null
                                    });
                                    console.log('Whisper STT initialized successfully');
                                } else {
                                    updateState({
                                        error: data.error || 'Failed to initialize Whisper',
                                        isInitialized: false,
                                        initializationProgress: 0
                                    });
                                }
                                break;
                            case 'transcription':
                                var _options_onTranscription;
                                updateState({
                                    isTranscribing: false,
                                    lastTranscription: data.result,
                                    error: null
                                });
                                (_options_onTranscription = options.onTranscription) === null || _options_onTranscription === void 0 ? void 0 : _options_onTranscription.call(options, data.result);
                                break;
                            case 'error':
                                var _options_onError;
                                const errorMessage = data.error || 'Unknown transcription error';
                                updateState({
                                    error: errorMessage,
                                    isTranscribing: false
                                });
                                (_options_onError = options.onError) === null || _options_onError === void 0 ? void 0 : _options_onError.call(options, errorMessage);
                                break;
                            case 'progress':
                                updateState({
                                    initializationProgress: data.progress || 0
                                });
                                break;
                            default:
                                console.warn('Unknown message type from Whisper worker:', type);
                        }
                    }
                })["useWhisperSTT.useCallback[initialize]"];
                workerRef.current.onerror = ({
                    "useWhisperSTT.useCallback[initialize]": (error)=>{
                        var _options_onError;
                        const errorMessage = "Worker error: ".concat(error.message);
                        updateState({
                            error: errorMessage,
                            isInitialized: false,
                            initializationProgress: 0
                        });
                        (_options_onError = options.onError) === null || _options_onError === void 0 ? void 0 : _options_onError.call(options, errorMessage);
                    }
                })["useWhisperSTT.useCallback[initialize]"];
                // Initialize the worker
                updateState({
                    initializationProgress: 10
                });
                workerRef.current.postMessage({
                    type: 'initialize'
                });
            } catch (error) {
                var _options_onError;
                const errorMessage = error instanceof Error ? error.message : 'Failed to create Whisper worker';
                updateState({
                    error: errorMessage,
                    isInitialized: false,
                    initializationProgress: 0
                });
                (_options_onError = options.onError) === null || _options_onError === void 0 ? void 0 : _options_onError.call(options, errorMessage);
            }
        }
    }["useWhisperSTT.useCallback[initialize]"], [
        options,
        updateState
    ]);
    const transcribeAudio = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "useWhisperSTT.useCallback[transcribeAudio]": async function(audioData) {
            let transcriptionOptions = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : {};
            if (!workerRef.current || !state.isInitialized) {
                throw new Error('Whisper STT not initialized');
            }
            updateState({
                isTranscribing: true,
                error: null
            });
            workerRef.current.postMessage({
                type: 'transcribe',
                data: {
                    audioData: Array.from(audioData),
                    options: transcriptionOptions
                }
            });
        }
    }["useWhisperSTT.useCallback[transcribeAudio]"], [
        state.isInitialized,
        updateState
    ]);
    const bufferAudio = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "useWhisperSTT.useCallback[bufferAudio]": (chunk)=>{
            if (!workerRef.current || !state.isInitialized) {
                return;
            }
            workerRef.current.postMessage({
                type: 'buffer-audio',
                data: {
                    data: Array.from(chunk.data),
                    timestamp: chunk.timestamp,
                    sampleRate: chunk.sampleRate
                }
            });
        }
    }["useWhisperSTT.useCallback[bufferAudio]"], [
        state.isInitialized
    ]);
    const flushBuffer = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "useWhisperSTT.useCallback[flushBuffer]": ()=>{
            if (!workerRef.current || !state.isInitialized) {
                return;
            }
            workerRef.current.postMessage({
                type: 'flush-buffer'
            });
        }
    }["useWhisperSTT.useCallback[flushBuffer]"], [
        state.isInitialized
    ]);
    const reset = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "useWhisperSTT.useCallback[reset]": ()=>{
            if (!workerRef.current) {
                return;
            }
            workerRef.current.postMessage({
                type: 'reset'
            });
            updateState({
                isTranscribing: false,
                lastTranscription: null,
                error: null
            });
        }
    }["useWhisperSTT.useCallback[reset]"], [
        updateState
    ]);
    // Auto-initialize if requested
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "useWhisperSTT.useEffect": ()=>{
            if (options.autoInitialize) {
                initialize();
            }
        }
    }["useWhisperSTT.useEffect"], [
        options.autoInitialize,
        initialize
    ]);
    // Cleanup on unmount
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "useWhisperSTT.useEffect": ()=>{
            return ({
                "useWhisperSTT.useEffect": ()=>{
                    if (workerRef.current) {
                        workerRef.current.terminate();
                        workerRef.current = null;
                    }
                }
            })["useWhisperSTT.useEffect"];
        }
    }["useWhisperSTT.useEffect"], []);
    const controls = {
        initialize,
        transcribeAudio,
        bufferAudio,
        flushBuffer,
        reset
    };
    return [
        state,
        controls
    ];
}
_s(useWhisperSTT, "+EtnjWFW52I59LkgcxHODtE20Co=");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/lib/openaiClient.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "OpenAIClient": ()=>OpenAIClient
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$swc$2f$helpers$2f$esm$2f$_define_property$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@swc/helpers/esm/_define_property.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$openai$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/openai/index.mjs [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$openai$2f$client$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__OpenAI__as__default$3e$__ = __turbopack_context__.i("[project]/node_modules/openai/client.mjs [app-client] (ecmascript) <export OpenAI as default>");
;
;
class OpenAIClient {
    async sendMessage(userMessage) {
        let options = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : {};
        const startTime = performance.now();
        try {
            var _completion_choices__message, _completion_choices_;
            // Add user message to history
            const userChatMessage = {
                role: 'user',
                content: userMessage,
                timestamp: Date.now()
            };
            this.conversationHistory.push(userChatMessage);
            // Prepare messages for API
            const messages = [];
            // Add system prompt
            const systemPrompt = options.systemPrompt || this.defaultSystemPrompt;
            messages.push({
                role: 'system',
                content: systemPrompt
            });
            // Add conversation history (keep last 10 messages to manage context length)
            const recentHistory = this.conversationHistory.slice(-10);
            messages.push(...recentHistory);
            // Make API call
            const completion = await this.client.chat.completions.create({
                model: options.model || 'gpt-3.5-turbo',
                messages: messages.map((msg)=>({
                        role: msg.role,
                        content: msg.content
                    })),
                temperature: options.temperature || 0.7,
                max_tokens: options.maxTokens || 150,
                stream: false
            });
            const endTime = performance.now();
            const processingTime = endTime - startTime;
            const assistantMessage = ((_completion_choices_ = completion.choices[0]) === null || _completion_choices_ === void 0 ? void 0 : (_completion_choices__message = _completion_choices_.message) === null || _completion_choices__message === void 0 ? void 0 : _completion_choices__message.content) || '';
            // Add assistant response to history
            const assistantChatMessage = {
                role: 'assistant',
                content: assistantMessage,
                timestamp: Date.now()
            };
            this.conversationHistory.push(assistantChatMessage);
            const response = {
                message: assistantMessage,
                usage: completion.usage ? {
                    promptTokens: completion.usage.prompt_tokens,
                    completionTokens: completion.usage.completion_tokens,
                    totalTokens: completion.usage.total_tokens
                } : undefined,
                processingTime
            };
            console.log("OpenAI response received in ".concat(processingTime.toFixed(2), "ms"));
            return response;
        } catch (error) {
            const endTime = performance.now();
            const processingTime = endTime - startTime;
            console.error('OpenAI API error:', error);
            // Return error response
            return {
                message: 'Sorry, I encountered an error processing your request.',
                processingTime
            };
        }
    }
    getConversationHistory() {
        return [
            ...this.conversationHistory
        ];
    }
    clearConversationHistory() {
        this.conversationHistory = [];
    }
    setSystemPrompt(prompt) {
        this.defaultSystemPrompt = prompt;
    }
    getSystemPrompt() {
        return this.defaultSystemPrompt;
    }
    // Remove old messages to manage memory and context length
    trimConversationHistory() {
        let maxMessages = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : 20;
        if (this.conversationHistory.length > maxMessages) {
            this.conversationHistory = this.conversationHistory.slice(-maxMessages);
        }
    }
    constructor(apiKey){
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$swc$2f$helpers$2f$esm$2f$_define_property$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["_"])(this, "client", void 0);
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$swc$2f$helpers$2f$esm$2f$_define_property$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["_"])(this, "conversationHistory", []);
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$swc$2f$helpers$2f$esm$2f$_define_property$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["_"])(this, "defaultSystemPrompt", "You are a helpful voice assistant. Keep your responses concise and conversational, as they will be spoken aloud. Aim for responses that are 1-2 sentences unless more detail is specifically requested.");
        this.client = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$openai$2f$client$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__OpenAI__as__default$3e$__["default"]({
            apiKey,
            dangerouslyAllowBrowser: true
        });
    }
}
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/hooks/useOpenAIChat.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "useOpenAIChat": ()=>useOpenAIChat
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$openaiClient$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/openaiClient.ts [app-client] (ecmascript)");
var _s = __turbopack_context__.k.signature();
;
;
function useOpenAIChat() {
    let options = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : {};
    _s();
    const clientRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])(null);
    const [state, setState] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])({
        isInitialized: false,
        isProcessing: false,
        error: null,
        conversationHistory: [],
        lastResponse: null
    });
    const updateState = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "useOpenAIChat.useCallback[updateState]": (updates)=>{
            setState({
                "useOpenAIChat.useCallback[updateState]": (prev)=>({
                        ...prev,
                        ...updates
                    })
            }["useOpenAIChat.useCallback[updateState]"]);
        }
    }["useOpenAIChat.useCallback[updateState]"], []);
    const initialize = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "useOpenAIChat.useCallback[initialize]": (apiKey)=>{
            try {
                if (!apiKey || apiKey.trim() === '') {
                    throw new Error('OpenAI API key is required');
                }
                clientRef.current = new __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$openaiClient$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["OpenAIClient"](apiKey);
                updateState({
                    isInitialized: true,
                    error: null,
                    conversationHistory: []
                });
                console.log('OpenAI client initialized successfully');
            } catch (error) {
                var _options_onError;
                const errorMessage = error instanceof Error ? error.message : 'Failed to initialize OpenAI client';
                updateState({
                    error: errorMessage,
                    isInitialized: false
                });
                (_options_onError = options.onError) === null || _options_onError === void 0 ? void 0 : _options_onError.call(options, errorMessage);
            }
        }
    }["useOpenAIChat.useCallback[initialize]"], [
        options,
        updateState
    ]);
    const sendMessage = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "useOpenAIChat.useCallback[sendMessage]": async function(message) {
            let chatOptions = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : {};
            if (!clientRef.current || !state.isInitialized) {
                var _options_onError;
                const error = 'OpenAI client not initialized';
                updateState({
                    error
                });
                (_options_onError = options.onError) === null || _options_onError === void 0 ? void 0 : _options_onError.call(options, error);
                return null;
            }
            if (!message || message.trim() === '') {
                var _options_onError1;
                const error = 'Message cannot be empty';
                updateState({
                    error
                });
                (_options_onError1 = options.onError) === null || _options_onError1 === void 0 ? void 0 : _options_onError1.call(options, error);
                return null;
            }
            try {
                var _options_onResponse;
                updateState({
                    isProcessing: true,
                    error: null
                });
                const response = await clientRef.current.sendMessage(message, chatOptions);
                // Update conversation history
                const updatedHistory = clientRef.current.getConversationHistory();
                updateState({
                    isProcessing: false,
                    lastResponse: response,
                    conversationHistory: updatedHistory,
                    error: null
                });
                // Auto-trim history if enabled
                if (options.autoTrimHistory && options.maxHistoryLength) {
                    clientRef.current.trimConversationHistory(options.maxHistoryLength);
                }
                (_options_onResponse = options.onResponse) === null || _options_onResponse === void 0 ? void 0 : _options_onResponse.call(options, response);
                return response;
            } catch (error) {
                var _options_onError2;
                const errorMessage = error instanceof Error ? error.message : 'Failed to send message';
                updateState({
                    error: errorMessage,
                    isProcessing: false
                });
                (_options_onError2 = options.onError) === null || _options_onError2 === void 0 ? void 0 : _options_onError2.call(options, errorMessage);
                return null;
            }
        }
    }["useOpenAIChat.useCallback[sendMessage]"], [
        state.isInitialized,
        options,
        updateState
    ]);
    const clearHistory = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "useOpenAIChat.useCallback[clearHistory]": ()=>{
            if (clientRef.current) {
                clientRef.current.clearConversationHistory();
                updateState({
                    conversationHistory: [],
                    lastResponse: null,
                    error: null
                });
            }
        }
    }["useOpenAIChat.useCallback[clearHistory]"], [
        updateState
    ]);
    const setSystemPrompt = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "useOpenAIChat.useCallback[setSystemPrompt]": (prompt)=>{
            if (clientRef.current) {
                clientRef.current.setSystemPrompt(prompt);
            }
        }
    }["useOpenAIChat.useCallback[setSystemPrompt]"], []);
    const getSystemPrompt = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "useOpenAIChat.useCallback[getSystemPrompt]": ()=>{
            var _clientRef_current;
            return ((_clientRef_current = clientRef.current) === null || _clientRef_current === void 0 ? void 0 : _clientRef_current.getSystemPrompt()) || '';
        }
    }["useOpenAIChat.useCallback[getSystemPrompt]"], []);
    const controls = {
        initialize,
        sendMessage,
        clearHistory,
        setSystemPrompt,
        getSystemPrompt
    };
    return [
        state,
        controls
    ];
}
_s(useOpenAIChat, "bUdZn3hMn2QVP5v0WHXpDc5b3sg=");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/hooks/useTTS.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "useTTS": ()=>useTTS
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var _s = __turbopack_context__.k.signature();
;
function useTTS() {
    let options = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : {};
    _s();
    const workerRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])(null);
    const audioContextRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])(null);
    const [state, setState] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])({
        isInitialized: false,
        isSynthesizing: false,
        error: null,
        lastSynthesis: null,
        initializationProgress: 0
    });
    const updateState = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "useTTS.useCallback[updateState]": (updates)=>{
            setState({
                "useTTS.useCallback[updateState]": (prev)=>({
                        ...prev,
                        ...updates
                    })
            }["useTTS.useCallback[updateState]"]);
        }
    }["useTTS.useCallback[updateState]"], []);
    const playAudio = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "useTTS.useCallback[playAudio]": async (audioData, sampleRate)=>{
            if (!audioContextRef.current) {
                throw new Error('Audio context not available');
            }
            try {
                // Resume audio context if suspended
                if (audioContextRef.current.state === 'suspended') {
                    await audioContextRef.current.resume();
                }
                // Create audio buffer
                const audioBuffer = audioContextRef.current.createBuffer(1, audioData.length, sampleRate);
                // Copy audio data to buffer
                const channelData = audioBuffer.getChannelData(0);
                channelData.set(audioData);
                // Create and play audio source
                const source = audioContextRef.current.createBufferSource();
                source.buffer = audioBuffer;
                source.connect(audioContextRef.current.destination);
                return new Promise({
                    "useTTS.useCallback[playAudio]": (resolve)=>{
                        source.onended = ({
                            "useTTS.useCallback[playAudio]": ()=>resolve()
                        })["useTTS.useCallback[playAudio]"];
                        source.start();
                    }
                }["useTTS.useCallback[playAudio]"]);
            } catch (error) {
                console.error('Audio playback error:', error);
                throw error;
            }
        }
    }["useTTS.useCallback[playAudio]"], []);
    const initialize = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "useTTS.useCallback[initialize]": async ()=>{
            try {
                updateState({
                    error: null,
                    initializationProgress: 0
                });
                // Create audio context for playback
                audioContextRef.current = new AudioContext();
                // Create worker
                workerRef.current = new Worker('/tts-worker.js');
                // Set up message handler
                workerRef.current.onmessage = ({
                    "useTTS.useCallback[initialize]": async (event)=>{
                        const { type, ...data } = event.data;
                        switch(type){
                            case 'initialized':
                                if (data.success) {
                                    updateState({
                                        isInitialized: true,
                                        initializationProgress: 100,
                                        error: null
                                    });
                                    console.log('TTS initialized successfully');
                                } else {
                                    updateState({
                                        error: data.error || 'Failed to initialize TTS',
                                        isInitialized: false,
                                        initializationProgress: 0
                                    });
                                }
                                break;
                            case 'synthesis':
                                var _options_onSynthesis;
                                const result = {
                                    audioData: new Float32Array(data.result.audioData),
                                    sampleRate: data.result.sampleRate,
                                    duration: data.result.duration,
                                    processingTime: data.result.processingTime,
                                    text: data.result.text
                                };
                                updateState({
                                    isSynthesizing: false,
                                    lastSynthesis: result,
                                    error: null
                                });
                                (_options_onSynthesis = options.onSynthesis) === null || _options_onSynthesis === void 0 ? void 0 : _options_onSynthesis.call(options, result);
                                // Auto-play if enabled
                                if (options.autoPlay) {
                                    await playAudio(result.audioData, result.sampleRate);
                                }
                                break;
                            case 'error':
                                var _options_onError;
                                const errorMessage = data.error || 'Unknown TTS error';
                                updateState({
                                    error: errorMessage,
                                    isSynthesizing: false
                                });
                                (_options_onError = options.onError) === null || _options_onError === void 0 ? void 0 : _options_onError.call(options, errorMessage);
                                break;
                            case 'progress':
                                updateState({
                                    initializationProgress: data.progress || 0
                                });
                                break;
                            default:
                                console.warn('Unknown message type from TTS worker:', type);
                        }
                    }
                })["useTTS.useCallback[initialize]"];
                workerRef.current.onerror = ({
                    "useTTS.useCallback[initialize]": (error)=>{
                        var _options_onError;
                        const errorMessage = "TTS Worker error: ".concat(error.message);
                        updateState({
                            error: errorMessage,
                            isInitialized: false,
                            initializationProgress: 0
                        });
                        (_options_onError = options.onError) === null || _options_onError === void 0 ? void 0 : _options_onError.call(options, errorMessage);
                    }
                })["useTTS.useCallback[initialize]"];
                // Initialize the worker
                updateState({
                    initializationProgress: 10
                });
                workerRef.current.postMessage({
                    type: 'initialize'
                });
            } catch (error) {
                var _options_onError;
                const errorMessage = error instanceof Error ? error.message : 'Failed to create TTS worker';
                updateState({
                    error: errorMessage,
                    isInitialized: false,
                    initializationProgress: 0
                });
                (_options_onError = options.onError) === null || _options_onError === void 0 ? void 0 : _options_onError.call(options, errorMessage);
            }
        }
    }["useTTS.useCallback[initialize]"], [
        options,
        updateState,
        playAudio
    ]);
    const synthesizeText = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "useTTS.useCallback[synthesizeText]": async function(text) {
            let synthesisOptions = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : {};
            if (!workerRef.current || !state.isInitialized) {
                throw new Error('TTS not initialized');
            }
            if (!text || text.trim() === '') {
                throw new Error('Text cannot be empty');
            }
            updateState({
                isSynthesizing: true,
                error: null
            });
            workerRef.current.postMessage({
                type: 'synthesize',
                data: {
                    text: text.trim(),
                    options: synthesisOptions
                }
            });
        }
    }["useTTS.useCallback[synthesizeText]"], [
        state.isInitialized,
        updateState
    ]);
    const reset = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "useTTS.useCallback[reset]": ()=>{
            if (!workerRef.current) {
                return;
            }
            workerRef.current.postMessage({
                type: 'reset'
            });
            updateState({
                isSynthesizing: false,
                lastSynthesis: null,
                error: null
            });
        }
    }["useTTS.useCallback[reset]"], [
        updateState
    ]);
    // Auto-initialize if requested
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "useTTS.useEffect": ()=>{
            if (options.autoInitialize) {
                initialize();
            }
        }
    }["useTTS.useEffect"], [
        options.autoInitialize,
        initialize
    ]);
    // Cleanup on unmount
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "useTTS.useEffect": ()=>{
            return ({
                "useTTS.useEffect": ()=>{
                    if (workerRef.current) {
                        workerRef.current.terminate();
                        workerRef.current = null;
                    }
                    if (audioContextRef.current) {
                        audioContextRef.current.close();
                        audioContextRef.current = null;
                    }
                }
            })["useTTS.useEffect"];
        }
    }["useTTS.useEffect"], []);
    const controls = {
        initialize,
        synthesizeText,
        playAudio,
        reset
    };
    return [
        state,
        controls
    ];
}
_s(useTTS, "Eg2S5zS9a2oZEWvFzABToj5Nxn0=");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/lib/performanceMonitor.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "performanceMonitor": ()=>performanceMonitor
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$swc$2f$helpers$2f$esm$2f$_define_property$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@swc/helpers/esm/_define_property.js [app-client] (ecmascript)");
;
class PerformanceMonitor {
    generateSessionId() {
        return "session_".concat(Date.now(), "_").concat(Math.random().toString(36).substr(2, 9));
    }
    getDeviceInfo() {
        const nav = navigator;
        return {
            platform: navigator.platform,
            memory: nav.deviceMemory,
            cores: nav.hardwareConcurrency
        };
    }
    recordMetrics(metrics) {
        const fullMetrics = {
            ...metrics,
            timestamp: Date.now(),
            sessionId: this.sessionId,
            userAgent: navigator.userAgent,
            deviceInfo: this.getDeviceInfo()
        };
        this.metrics.push(fullMetrics);
        // Keep only the last N metrics
        if (this.metrics.length > this.maxMetrics) {
            this.metrics = this.metrics.slice(-this.maxMetrics);
        }
        // Log performance
        this.logPerformance(fullMetrics);
        // Store in localStorage for persistence
        this.persistMetrics();
    }
    logPerformance(metrics) {
        const { totalLatency, sttLatency, llmLatency, ttsLatency, audioLength } = metrics;
        console.group('🔍 Performance Metrics');
        console.log("Total Latency: ".concat(totalLatency.toFixed(0), "ms ").concat(totalLatency < 1200 ? '✅' : '⚠️'));
        console.log("STT: ".concat(sttLatency.toFixed(0), "ms"));
        console.log("LLM: ".concat(llmLatency.toFixed(0), "ms"));
        console.log("TTS: ".concat(ttsLatency.toFixed(0), "ms"));
        console.log("Audio Length: ".concat(audioLength.toFixed(1), "s"));
        console.log("Efficiency: ".concat((audioLength * 1000 / totalLatency).toFixed(2), "x realtime"));
        if (totalLatency > 1200) {
            console.warn('⚠️ Total latency exceeds 1.2s target');
            this.analyzeBottlenecks(metrics);
        }
        console.groupEnd();
    }
    analyzeBottlenecks(metrics) {
        const { sttLatency, llmLatency, ttsLatency } = metrics;
        const total = sttLatency + llmLatency + ttsLatency;
        console.group('🔍 Bottleneck Analysis');
        if (sttLatency > total * 0.4) {
            console.warn('STT is the bottleneck (>40% of total time)');
            console.log('Recommendations: Use smaller Whisper model, optimize audio preprocessing');
        }
        if (llmLatency > total * 0.5) {
            console.warn('LLM is the bottleneck (>50% of total time)');
            console.log('Recommendations: Use faster model (gpt-3.5-turbo), reduce max_tokens, optimize prompt');
        }
        if (ttsLatency > total * 0.3) {
            console.warn('TTS is the bottleneck (>30% of total time)');
            console.log('Recommendations: Use smaller TTS model, optimize text preprocessing');
        }
        console.groupEnd();
    }
    persistMetrics() {
        try {
            const recentMetrics = this.metrics.slice(-100); // Store last 100
            localStorage.setItem('voiceAssistantMetrics', JSON.stringify(recentMetrics));
        } catch (error) {
            console.warn('Failed to persist metrics:', error);
        }
    }
    loadPersistedMetrics() {
        try {
            const stored = localStorage.getItem('voiceAssistantMetrics');
            if (stored) {
                const parsed = JSON.parse(stored);
                if (Array.isArray(parsed)) {
                    this.metrics = parsed;
                }
            }
        } catch (error) {
            console.warn('Failed to load persisted metrics:', error);
        }
    }
    generateReport() {
        if (this.metrics.length === 0) {
            return {
                totalSessions: 0,
                averageLatency: 0,
                medianLatency: 0,
                p95Latency: 0,
                successRate: 0,
                cacheHitRate: 0,
                breakdown: {
                    stt: {
                        avg: 0,
                        median: 0,
                        p95: 0
                    },
                    llm: {
                        avg: 0,
                        median: 0,
                        p95: 0
                    },
                    tts: {
                        avg: 0,
                        median: 0,
                        p95: 0
                    }
                },
                recommendations: []
            };
        }
        const latencies = this.metrics.map((m)=>m.totalLatency).sort((a, b)=>a - b);
        const sttLatencies = this.metrics.map((m)=>m.sttLatency).sort((a, b)=>a - b);
        const llmLatencies = this.metrics.map((m)=>m.llmLatency).sort((a, b)=>a - b);
        const ttsLatencies = this.metrics.map((m)=>m.ttsLatency).sort((a, b)=>a - b);
        const avg = (arr)=>arr.reduce((a, b)=>a + b, 0) / arr.length;
        const median = (arr)=>arr[Math.floor(arr.length / 2)];
        const p95 = (arr)=>arr[Math.floor(arr.length * 0.95)];
        const cacheHits = this.metrics.filter((m)=>m.cacheHit).length;
        const successfulSessions = this.metrics.filter((m)=>m.totalLatency > 0).length;
        const report = {
            totalSessions: this.metrics.length,
            averageLatency: avg(latencies),
            medianLatency: median(latencies),
            p95Latency: p95(latencies),
            successRate: successfulSessions / this.metrics.length,
            cacheHitRate: cacheHits / this.metrics.length,
            breakdown: {
                stt: {
                    avg: avg(sttLatencies),
                    median: median(sttLatencies),
                    p95: p95(sttLatencies)
                },
                llm: {
                    avg: avg(llmLatencies),
                    median: median(llmLatencies),
                    p95: p95(llmLatencies)
                },
                tts: {
                    avg: avg(ttsLatencies),
                    median: median(ttsLatencies),
                    p95: p95(ttsLatencies)
                }
            },
            recommendations: this.generateRecommendations(report)
        };
        return report;
    }
    generateRecommendations(report) {
        const recommendations = [];
        if (report.averageLatency > 1200) {
            recommendations.push('Average latency exceeds 1.2s target. Consider optimizing the pipeline.');
        }
        if (report.breakdown.stt.avg > 500) {
            recommendations.push('STT latency is high. Consider using whisper-tiny or optimizing audio preprocessing.');
        }
        if (report.breakdown.llm.avg > 800) {
            recommendations.push('LLM latency is high. Consider using gpt-3.5-turbo or reducing max_tokens.');
        }
        if (report.breakdown.tts.avg > 400) {
            recommendations.push('TTS latency is high. Consider using a smaller TTS model or optimizing text preprocessing.');
        }
        if (report.cacheHitRate < 0.8) {
            recommendations.push('Low cache hit rate. Ensure models are properly cached.');
        }
        if (report.successRate < 0.95) {
            recommendations.push('Low success rate. Check for errors in the pipeline.');
        }
        return recommendations;
    }
    exportMetrics() {
        return JSON.stringify(this.metrics, null, 2);
    }
    clearMetrics() {
        this.metrics = [];
        localStorage.removeItem('voiceAssistantMetrics');
    }
    getRecentMetrics() {
        let count = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : 10;
        return this.metrics.slice(-count);
    }
    // Initialize with persisted data
    init() {
        this.loadPersistedMetrics();
    }
    constructor(){
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$swc$2f$helpers$2f$esm$2f$_define_property$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["_"])(this, "metrics", []);
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$swc$2f$helpers$2f$esm$2f$_define_property$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["_"])(this, "sessionId", void 0);
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$swc$2f$helpers$2f$esm$2f$_define_property$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["_"])(this, "maxMetrics", 1000); // Keep last 1000 metrics
        this.sessionId = this.generateSessionId();
    }
}
const performanceMonitor = new PerformanceMonitor();
// Initialize on import
performanceMonitor.init();
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/hooks/useVoiceAssistant.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "useVoiceAssistant": ()=>useVoiceAssistant
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$useAudioRecording$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/hooks/useAudioRecording.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$useWhisperSTT$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/hooks/useWhisperSTT.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$useOpenAIChat$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/hooks/useOpenAIChat.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$useTTS$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/hooks/useTTS.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$performanceMonitor$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/performanceMonitor.ts [app-client] (ecmascript)");
var _s = __turbopack_context__.k.signature();
;
;
;
;
;
;
function useVoiceAssistant() {
    let options = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : {};
    _s();
    const [state, setState] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])({
        isInitialized: false,
        isListening: false,
        isProcessing: false,
        currentStage: 'idle',
        error: null,
        volume: 0,
        lastTranscription: null,
        lastResponse: null
    });
    const metricsRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])({});
    const pipelineStartTimeRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])(0);
    const updateState = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "useVoiceAssistant.useCallback[updateState]": (updates)=>{
            setState({
                "useVoiceAssistant.useCallback[updateState]": (prev)=>{
                    const newState = {
                        ...prev,
                        ...updates
                    };
                    // Notify stage change
                    if (updates.currentStage && updates.currentStage !== prev.currentStage) {
                        var _options_onStageChange;
                        (_options_onStageChange = options.onStageChange) === null || _options_onStageChange === void 0 ? void 0 : _options_onStageChange.call(options, updates.currentStage);
                    }
                    return newState;
                }
            }["useVoiceAssistant.useCallback[updateState]"]);
        }
    }["useVoiceAssistant.useCallback[updateState]"], [
        options
    ]);
    // Audio recording hook
    const [, audioControls] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$useAudioRecording$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAudioRecording"])({
        onAudioChunk: {
            "useVoiceAssistant.useAudioRecording": (chunk)=>{
                if (state.currentStage === 'listening') {
                    whisperControls.bufferAudio(chunk);
                }
            }
        }["useVoiceAssistant.useAudioRecording"],
        onSpeechStart: {
            "useVoiceAssistant.useAudioRecording": ()=>{
                console.log('Speech started');
                pipelineStartTimeRef.current = performance.now();
                metricsRef.current = {};
                updateState({
                    currentStage: 'listening'
                });
            }
        }["useVoiceAssistant.useAudioRecording"],
        onSpeechEnd: {
            "useVoiceAssistant.useAudioRecording": ()=>{
                console.log('Speech ended');
                if (state.currentStage === 'listening') {
                    updateState({
                        currentStage: 'transcribing'
                    });
                    whisperControls.flushBuffer();
                }
            }
        }["useVoiceAssistant.useAudioRecording"],
        onVolumeChange: {
            "useVoiceAssistant.useAudioRecording": (volume)=>{
                updateState({
                    volume
                });
            }
        }["useVoiceAssistant.useAudioRecording"],
        onError: {
            "useVoiceAssistant.useAudioRecording": (error)=>{
                var _options_onError;
                updateState({
                    error: error.message
                });
                (_options_onError = options.onError) === null || _options_onError === void 0 ? void 0 : _options_onError.call(options, error.message);
            }
        }["useVoiceAssistant.useAudioRecording"]
    });
    // Whisper STT hook
    const [, whisperControls] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$useWhisperSTT$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useWhisperSTT"])({
        onTranscription: {
            "useVoiceAssistant.useWhisperSTT": (result)=>{
                var _options_onTranscription;
                const sttEndTime = performance.now();
                metricsRef.current.sttLatency = sttEndTime - pipelineStartTimeRef.current;
                metricsRef.current.audioLength = result.audioLength;
                console.log('STT completed: "'.concat(result.text, '" (').concat(metricsRef.current.sttLatency.toFixed(2), "ms)"));
                updateState({
                    lastTranscription: result.text,
                    currentStage: 'thinking'
                });
                (_options_onTranscription = options.onTranscription) === null || _options_onTranscription === void 0 ? void 0 : _options_onTranscription.call(options, result.text);
                // Send to OpenAI
                if (result.text.trim()) {
                    const llmStartTime = performance.now();
                    chatControls.sendMessage(result.text).then({
                        "useVoiceAssistant.useWhisperSTT": ()=>{
                            const llmEndTime = performance.now();
                            metricsRef.current.llmLatency = llmEndTime - llmStartTime;
                        }
                    }["useVoiceAssistant.useWhisperSTT"]);
                }
            }
        }["useVoiceAssistant.useWhisperSTT"],
        onError: {
            "useVoiceAssistant.useWhisperSTT": (error)=>{
                var _options_onError;
                updateState({
                    error,
                    currentStage: 'idle'
                });
                (_options_onError = options.onError) === null || _options_onError === void 0 ? void 0 : _options_onError.call(options, error);
            }
        }["useVoiceAssistant.useWhisperSTT"]
    });
    // OpenAI Chat hook
    const [, chatControls] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$useOpenAIChat$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useOpenAIChat"])({
        onResponse: {
            "useVoiceAssistant.useOpenAIChat": (response)=>{
                var _options_onResponse;
                console.log('LLM completed: "'.concat(response.message, '" (').concat(response.processingTime.toFixed(2), "ms)"));
                updateState({
                    lastResponse: response.message,
                    currentStage: 'speaking'
                });
                (_options_onResponse = options.onResponse) === null || _options_onResponse === void 0 ? void 0 : _options_onResponse.call(options, response.message);
                // Send to TTS
                ttsControls.synthesizeText(response.message);
            }
        }["useVoiceAssistant.useOpenAIChat"],
        onError: {
            "useVoiceAssistant.useOpenAIChat": (error)=>{
                var _options_onError;
                updateState({
                    error,
                    currentStage: 'idle'
                });
                (_options_onError = options.onError) === null || _options_onError === void 0 ? void 0 : _options_onError.call(options, error);
            }
        }["useVoiceAssistant.useOpenAIChat"],
        autoTrimHistory: true,
        maxHistoryLength: 10
    });
    // TTS hook
    const [, ttsControls] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$useTTS$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useTTS"])({
        onSynthesis: {
            "useVoiceAssistant.useTTS": (result)=>{
                var _metricsRef_current_totalLatency;
                const totalEndTime = performance.now();
                metricsRef.current.ttsLatency = result.processingTime;
                metricsRef.current.totalLatency = totalEndTime - pipelineStartTimeRef.current;
                console.log("TTS completed (".concat(result.processingTime.toFixed(2), "ms)"));
                console.log("Total pipeline latency: ".concat((_metricsRef_current_totalLatency = metricsRef.current.totalLatency) === null || _metricsRef_current_totalLatency === void 0 ? void 0 : _metricsRef_current_totalLatency.toFixed(2), "ms"));
                // Report metrics
                if (metricsRef.current.totalLatency) {
                    var _options_onMetrics;
                    const metrics = {
                        sttLatency: metricsRef.current.sttLatency || 0,
                        llmLatency: metricsRef.current.llmLatency || 0,
                        ttsLatency: metricsRef.current.ttsLatency || 0,
                        totalLatency: metricsRef.current.totalLatency,
                        audioLength: metricsRef.current.audioLength || 0
                    };
                    // Record performance metrics
                    __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$performanceMonitor$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["performanceMonitor"].recordMetrics({
                        audioLength: metrics.audioLength,
                        sttLatency: metrics.sttLatency,
                        llmLatency: metrics.llmLatency,
                        ttsLatency: metrics.ttsLatency,
                        totalLatency: metrics.totalLatency
                    });
                    (_options_onMetrics = options.onMetrics) === null || _options_onMetrics === void 0 ? void 0 : _options_onMetrics.call(options, metrics);
                }
                // Return to listening after speech
                setTimeout({
                    "useVoiceAssistant.useTTS": ()=>{
                        updateState({
                            currentStage: 'idle'
                        });
                    }
                }["useVoiceAssistant.useTTS"], 500);
            }
        }["useVoiceAssistant.useTTS"],
        onError: {
            "useVoiceAssistant.useTTS": (error)=>{
                var _options_onError;
                updateState({
                    error,
                    currentStage: 'idle'
                });
                (_options_onError = options.onError) === null || _options_onError === void 0 ? void 0 : _options_onError.call(options, error);
            }
        }["useVoiceAssistant.useTTS"],
        autoPlay: true
    });
    // Initialize all components
    const initialize = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "useVoiceAssistant.useCallback[initialize]": async (openaiApiKey)=>{
            try {
                updateState({
                    error: null
                });
                // Initialize all components
                await Promise.all([
                    audioControls.initialize(),
                    whisperControls.initialize(),
                    ttsControls.initialize()
                ]);
                // Initialize OpenAI
                chatControls.initialize(openaiApiKey);
                updateState({
                    isInitialized: true
                });
                // Auto-start listening if requested
                if (options.autoStartListening) {
                    audioControls.startRecording();
                    updateState({
                        isListening: true,
                        currentStage: 'idle',
                        error: null
                    });
                }
            } catch (error) {
                var _options_onError;
                const errorMessage = error instanceof Error ? error.message : 'Initialization failed';
                updateState({
                    error: errorMessage,
                    isInitialized: false
                });
                (_options_onError = options.onError) === null || _options_onError === void 0 ? void 0 : _options_onError.call(options, errorMessage);
            }
        }
    }["useVoiceAssistant.useCallback[initialize]"], [
        audioControls,
        whisperControls,
        ttsControls,
        chatControls,
        options,
        updateState
    ]);
    const startListening = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "useVoiceAssistant.useCallback[startListening]": ()=>{
            if (!state.isInitialized) {
                updateState({
                    error: 'Voice assistant not initialized'
                });
                return;
            }
            audioControls.startRecording();
            updateState({
                isListening: true,
                currentStage: 'idle',
                error: null
            });
        }
    }["useVoiceAssistant.useCallback[startListening]"], [
        state.isInitialized,
        audioControls,
        updateState
    ]);
    const stopListening = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "useVoiceAssistant.useCallback[stopListening]": ()=>{
            audioControls.stopRecording();
            updateState({
                isListening: false,
                currentStage: 'idle'
            });
        }
    }["useVoiceAssistant.useCallback[stopListening]"], [
        audioControls,
        updateState
    ]);
    const sendTextMessage = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "useVoiceAssistant.useCallback[sendTextMessage]": async (message)=>{
            if (!state.isInitialized) {
                throw new Error('Voice assistant not initialized');
            }
            updateState({
                currentStage: 'thinking'
            });
            try {
                const response = await chatControls.sendMessage(message);
                if (response) {
                    updateState({
                        lastTranscription: message,
                        lastResponse: response.message,
                        currentStage: 'speaking'
                    });
                    // Synthesize response
                    await ttsControls.synthesizeText(response.message);
                }
            } catch (error) {
                const errorMessage = error instanceof Error ? error.message : 'Failed to send message';
                updateState({
                    error: errorMessage,
                    currentStage: 'idle'
                });
                throw error;
            }
        }
    }["useVoiceAssistant.useCallback[sendTextMessage]"], [
        state.isInitialized,
        chatControls,
        ttsControls,
        updateState
    ]);
    const reset = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "useVoiceAssistant.useCallback[reset]": ()=>{
            audioControls.stopRecording();
            whisperControls.reset();
            chatControls.clearHistory();
            ttsControls.reset();
            setState({
                isInitialized: false,
                isListening: false,
                isProcessing: false,
                currentStage: 'idle',
                error: null,
                volume: 0,
                lastTranscription: null,
                lastResponse: null
            });
        }
    }["useVoiceAssistant.useCallback[reset]"], [
        audioControls,
        whisperControls,
        chatControls,
        ttsControls
    ]);
    // Update processing state based on current stage
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "useVoiceAssistant.useEffect": ()=>{
            const isProcessing = [
                'transcribing',
                'thinking',
                'speaking'
            ].includes(state.currentStage);
            if (state.isProcessing !== isProcessing) {
                updateState({
                    isProcessing
                });
            }
        }
    }["useVoiceAssistant.useEffect"], [
        state.currentStage,
        state.isProcessing,
        updateState
    ]);
    const controls = {
        initialize,
        startListening,
        stopListening,
        sendTextMessage,
        reset
    };
    return [
        state,
        controls
    ];
}
_s(useVoiceAssistant, "BvpDyLHba+OObTlwpMICZvBT0e4=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$useAudioRecording$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAudioRecording"],
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$useWhisperSTT$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useWhisperSTT"],
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$useOpenAIChat$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useOpenAIChat"],
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$useTTS$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useTTS"]
    ];
});
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/lib/serviceWorker.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "ServiceWorkerManager": ()=>ServiceWorkerManager,
    "serviceWorkerManager": ()=>serviceWorkerManager
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$swc$2f$helpers$2f$esm$2f$_define_property$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@swc/helpers/esm/_define_property.js [app-client] (ecmascript)");
;
class ServiceWorkerManager {
    async register() {
        if ("object" === 'undefined' || !('serviceWorker' in navigator)) {
            console.warn('Service Worker not supported');
            return null;
        }
        try {
            this.registration = await navigator.serviceWorker.register('/sw.js');
            console.log('Service Worker registered successfully');
            // Listen for updates
            this.registration.addEventListener('updatefound', ()=>{
                var _this_registration;
                const newWorker = (_this_registration = this.registration) === null || _this_registration === void 0 ? void 0 : _this_registration.installing;
                if (newWorker) {
                    newWorker.addEventListener('statechange', ()=>{
                        if (newWorker.state === 'installed' && navigator.serviceWorker.controller) {
                            console.log('New Service Worker available');
                        // Optionally notify user about update
                        }
                    });
                }
            });
            // Listen for messages from service worker
            navigator.serviceWorker.addEventListener('message', (event)=>{
                var _event_data;
                if (((_event_data = event.data) === null || _event_data === void 0 ? void 0 : _event_data.type) === 'CACHE_STATUS') {
                    const status = {
                        ...event.data,
                        isReady: event.data.modelsCached === event.data.totalModels
                    };
                    this.cacheStatusCallbacks.forEach((callback)=>callback(status));
                }
            });
            return this.registration;
        } catch (error) {
            console.error('Service Worker registration failed:', error);
            return null;
        }
    }
    async getCacheStatus() {
        if (!this.registration || !navigator.serviceWorker.controller) {
            return null;
        }
        return new Promise((resolve)=>{
            const channel = new MessageChannel();
            channel.port1.onmessage = (event)=>{
                var _event_data;
                if (((_event_data = event.data) === null || _event_data === void 0 ? void 0 : _event_data.type) === 'CACHE_STATUS') {
                    const status = {
                        ...event.data,
                        isReady: event.data.modelsCached === event.data.totalModels
                    };
                    resolve(status);
                }
            };
            navigator.serviceWorker.controller.postMessage({
                type: 'GET_CACHE_STATUS'
            }, [
                channel.port2
            ]);
        });
    }
    onCacheStatusUpdate(callback) {
        this.cacheStatusCallbacks.push(callback);
        // Return unsubscribe function
        return ()=>{
            const index = this.cacheStatusCallbacks.indexOf(callback);
            if (index > -1) {
                this.cacheStatusCallbacks.splice(index, 1);
            }
        };
    }
    async skipWaiting() {
        var _this_registration;
        if ((_this_registration = this.registration) === null || _this_registration === void 0 ? void 0 : _this_registration.waiting) {
            this.registration.waiting.postMessage({
                type: 'SKIP_WAITING'
            });
        }
    }
    async unregister() {
        if (this.registration) {
            return await this.registration.unregister();
        }
        return false;
    }
    constructor(){
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$swc$2f$helpers$2f$esm$2f$_define_property$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["_"])(this, "registration", null);
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$swc$2f$helpers$2f$esm$2f$_define_property$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["_"])(this, "cacheStatusCallbacks", []);
    }
}
const serviceWorkerManager = new ServiceWorkerManager();
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/components/VoiceAssistant.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": ()=>__TURBOPACK__default__export__
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$useVoiceAssistant$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/hooks/useVoiceAssistant.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$serviceWorker$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/serviceWorker.ts [app-client] (ecmascript)");
;
var _s = __turbopack_context__.k.signature();
'use client';
;
;
;
const VoiceAssistant = ()=>{
    _s();
    const [apiKey, setApiKey] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])('');
    const [showApiKeyInput, setShowApiKeyInput] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(true);
    const [metrics, setMetrics] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(null);
    const [cacheStatus, setCacheStatus] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(null);
    const [voiceState, voiceControls] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$useVoiceAssistant$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useVoiceAssistant"])({
        onStageChange: {
            "VoiceAssistant.useVoiceAssistant": (stage)=>{
                console.log('Stage changed to:', stage);
            }
        }["VoiceAssistant.useVoiceAssistant"],
        onTranscription: {
            "VoiceAssistant.useVoiceAssistant": (text)=>{
                console.log('Transcription:', text);
            }
        }["VoiceAssistant.useVoiceAssistant"],
        onResponse: {
            "VoiceAssistant.useVoiceAssistant": (text)=>{
                console.log('Response:', text);
            }
        }["VoiceAssistant.useVoiceAssistant"],
        onMetrics: {
            "VoiceAssistant.useVoiceAssistant": (newMetrics)=>{
                setMetrics(newMetrics);
                console.log('Pipeline metrics:', newMetrics);
            }
        }["VoiceAssistant.useVoiceAssistant"],
        onError: {
            "VoiceAssistant.useVoiceAssistant": (error)=>{
                console.error('Voice assistant error:', error);
            }
        }["VoiceAssistant.useVoiceAssistant"]
    });
    // Initialize service worker and check cache status
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "VoiceAssistant.useEffect": ()=>{
            const initServiceWorker = {
                "VoiceAssistant.useEffect.initServiceWorker": async ()=>{
                    await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$serviceWorker$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["serviceWorkerManager"].register();
                    const status = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$serviceWorker$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["serviceWorkerManager"].getCacheStatus();
                    setCacheStatus(status);
                }
            }["VoiceAssistant.useEffect.initServiceWorker"];
            initServiceWorker();
            // Update cache status periodically
            const interval = setInterval({
                "VoiceAssistant.useEffect.interval": async ()=>{
                    const status = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$serviceWorker$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["serviceWorkerManager"].getCacheStatus();
                    setCacheStatus(status);
                }
            }["VoiceAssistant.useEffect.interval"], 5000);
            return ({
                "VoiceAssistant.useEffect": ()=>clearInterval(interval)
            })["VoiceAssistant.useEffect"];
        }
    }["VoiceAssistant.useEffect"], []);
    const handleInitialize = async ()=>{
        if (!apiKey.trim()) {
            alert('Please enter your OpenAI API key');
            return;
        }
        try {
            await voiceControls.initialize(apiKey);
            setShowApiKeyInput(false);
        } catch (error) {
            console.error('Initialization failed:', error);
        }
    };
    const handleStartListening = ()=>{
        voiceControls.startListening();
    };
    const handleStopListening = ()=>{
        voiceControls.stopListening();
    };
    const getStageColor = (stage)=>{
        switch(stage){
            case 'listening':
                return 'text-green-400';
            case 'transcribing':
                return 'text-blue-400';
            case 'thinking':
                return 'text-yellow-400';
            case 'speaking':
                return 'text-purple-400';
            default:
                return 'text-gray-400';
        }
    };
    const getStageIcon = (stage)=>{
        switch(stage){
            case 'listening':
                return '🎤';
            case 'transcribing':
                return '📝';
            case 'thinking':
                return '🤔';
            case 'speaking':
                return '🔊';
            default:
                return '⭕';
        }
    };
    if (showApiKeyInput) {
        return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            className: "min-h-screen flex items-center justify-center p-4",
            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "bg-gray-800 rounded-lg p-8 max-w-md w-full",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h1", {
                        className: "text-2xl font-bold mb-6 text-center",
                        children: "Voice Assistant"
                    }, void 0, false, {
                        fileName: "[project]/src/components/VoiceAssistant.tsx",
                        lineNumber: 101,
                        columnNumber: 11
                    }, ("TURBOPACK compile-time value", void 0)),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "mb-6",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("label", {
                                htmlFor: "apiKey",
                                className: "block text-sm font-medium mb-2",
                                children: "OpenAI API Key"
                            }, void 0, false, {
                                fileName: "[project]/src/components/VoiceAssistant.tsx",
                                lineNumber: 104,
                                columnNumber: 13
                            }, ("TURBOPACK compile-time value", void 0)),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("input", {
                                id: "apiKey",
                                type: "password",
                                value: apiKey,
                                onChange: (e)=>setApiKey(e.target.value),
                                placeholder: "sk-...",
                                className: "w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                            }, void 0, false, {
                                fileName: "[project]/src/components/VoiceAssistant.tsx",
                                lineNumber: 107,
                                columnNumber: 13
                            }, ("TURBOPACK compile-time value", void 0))
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/VoiceAssistant.tsx",
                        lineNumber: 103,
                        columnNumber: 11
                    }, ("TURBOPACK compile-time value", void 0)),
                    cacheStatus && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "mb-6 p-3 bg-gray-700 rounded-md",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                                className: "text-sm font-medium mb-2",
                                children: "Cache Status"
                            }, void 0, false, {
                                fileName: "[project]/src/components/VoiceAssistant.tsx",
                                lineNumber: 119,
                                columnNumber: 15
                            }, ("TURBOPACK compile-time value", void 0)),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "text-xs text-gray-300",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        children: [
                                            "Models: ",
                                            cacheStatus.modelsCached,
                                            "/",
                                            cacheStatus.totalModels
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/components/VoiceAssistant.tsx",
                                        lineNumber: 121,
                                        columnNumber: 17
                                    }, ("TURBOPACK compile-time value", void 0)),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        children: [
                                            "Ready: ",
                                            cacheStatus.isReady ? '✅' : '⏳'
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/components/VoiceAssistant.tsx",
                                        lineNumber: 122,
                                        columnNumber: 17
                                    }, ("TURBOPACK compile-time value", void 0))
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/VoiceAssistant.tsx",
                                lineNumber: 120,
                                columnNumber: 15
                            }, ("TURBOPACK compile-time value", void 0))
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/VoiceAssistant.tsx",
                        lineNumber: 118,
                        columnNumber: 13
                    }, ("TURBOPACK compile-time value", void 0)),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                        onClick: handleInitialize,
                        disabled: !apiKey.trim(),
                        className: "w-full bg-blue-600 hover:bg-blue-700 disabled:bg-gray-600 disabled:cursor-not-allowed px-4 py-2 rounded-md font-medium transition-colors",
                        children: "Initialize Voice Assistant"
                    }, void 0, false, {
                        fileName: "[project]/src/components/VoiceAssistant.tsx",
                        lineNumber: 127,
                        columnNumber: 11
                    }, ("TURBOPACK compile-time value", void 0))
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/VoiceAssistant.tsx",
                lineNumber: 100,
                columnNumber: 9
            }, ("TURBOPACK compile-time value", void 0))
        }, void 0, false, {
            fileName: "[project]/src/components/VoiceAssistant.tsx",
            lineNumber: 99,
            columnNumber: 7
        }, ("TURBOPACK compile-time value", void 0));
    }
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: "min-h-screen flex flex-col items-center justify-center p-4",
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            className: "bg-gray-800 rounded-lg p-8 max-w-2xl w-full",
            children: [
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h1", {
                    className: "text-3xl font-bold mb-8 text-center",
                    children: "Voice Assistant"
                }, void 0, false, {
                    fileName: "[project]/src/components/VoiceAssistant.tsx",
                    lineNumber: 142,
                    columnNumber: 9
                }, ("TURBOPACK compile-time value", void 0)),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "text-center mb-8",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "text-6xl mb-4 ".concat(getStageColor(voiceState.currentStage)),
                            children: getStageIcon(voiceState.currentStage)
                        }, void 0, false, {
                            fileName: "[project]/src/components/VoiceAssistant.tsx",
                            lineNumber: 146,
                            columnNumber: 11
                        }, ("TURBOPACK compile-time value", void 0)),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "text-xl font-medium ".concat(getStageColor(voiceState.currentStage)),
                            children: voiceState.currentStage.charAt(0).toUpperCase() + voiceState.currentStage.slice(1)
                        }, void 0, false, {
                            fileName: "[project]/src/components/VoiceAssistant.tsx",
                            lineNumber: 149,
                            columnNumber: 11
                        }, ("TURBOPACK compile-time value", void 0)),
                        voiceState.isListening && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "mt-2",
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "w-full bg-gray-700 rounded-full h-2",
                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "bg-green-400 h-2 rounded-full transition-all duration-100",
                                        style: {
                                            width: "".concat(Math.min(voiceState.volume * 100, 100), "%")
                                        }
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/VoiceAssistant.tsx",
                                        lineNumber: 156,
                                        columnNumber: 17
                                    }, ("TURBOPACK compile-time value", void 0))
                                }, void 0, false, {
                                    fileName: "[project]/src/components/VoiceAssistant.tsx",
                                    lineNumber: 155,
                                    columnNumber: 15
                                }, ("TURBOPACK compile-time value", void 0)),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "text-xs text-gray-400 mt-1",
                                    children: [
                                        "Volume: ",
                                        (voiceState.volume * 100).toFixed(0),
                                        "%"
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/src/components/VoiceAssistant.tsx",
                                    lineNumber: 161,
                                    columnNumber: 15
                                }, ("TURBOPACK compile-time value", void 0))
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/components/VoiceAssistant.tsx",
                            lineNumber: 154,
                            columnNumber: 13
                        }, ("TURBOPACK compile-time value", void 0))
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/components/VoiceAssistant.tsx",
                    lineNumber: 145,
                    columnNumber: 9
                }, ("TURBOPACK compile-time value", void 0)),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "flex justify-center gap-4 mb-8",
                    children: !voiceState.isListening ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                        onClick: handleStartListening,
                        disabled: !voiceState.isInitialized || voiceState.isProcessing,
                        className: "bg-green-600 hover:bg-green-700 disabled:bg-gray-600 disabled:cursor-not-allowed px-6 py-3 rounded-full font-medium transition-colors flex items-center gap-2",
                        children: "🎤 Start Listening"
                    }, void 0, false, {
                        fileName: "[project]/src/components/VoiceAssistant.tsx",
                        lineNumber: 171,
                        columnNumber: 13
                    }, ("TURBOPACK compile-time value", void 0)) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                        onClick: handleStopListening,
                        className: "bg-red-600 hover:bg-red-700 px-6 py-3 rounded-full font-medium transition-colors flex items-center gap-2",
                        children: "⏹️ Stop Listening"
                    }, void 0, false, {
                        fileName: "[project]/src/components/VoiceAssistant.tsx",
                        lineNumber: 179,
                        columnNumber: 13
                    }, ("TURBOPACK compile-time value", void 0))
                }, void 0, false, {
                    fileName: "[project]/src/components/VoiceAssistant.tsx",
                    lineNumber: 169,
                    columnNumber: 9
                }, ("TURBOPACK compile-time value", void 0)),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "space-y-4 mb-6",
                    children: [
                        voiceState.lastTranscription && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "bg-blue-900/30 p-4 rounded-lg",
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "text-sm text-blue-300 mb-1",
                                    children: "You said:"
                                }, void 0, false, {
                                    fileName: "[project]/src/components/VoiceAssistant.tsx",
                                    lineNumber: 192,
                                    columnNumber: 15
                                }, ("TURBOPACK compile-time value", void 0)),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "text-white",
                                    children: voiceState.lastTranscription
                                }, void 0, false, {
                                    fileName: "[project]/src/components/VoiceAssistant.tsx",
                                    lineNumber: 193,
                                    columnNumber: 15
                                }, ("TURBOPACK compile-time value", void 0))
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/components/VoiceAssistant.tsx",
                            lineNumber: 191,
                            columnNumber: 13
                        }, ("TURBOPACK compile-time value", void 0)),
                        voiceState.lastResponse && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "bg-purple-900/30 p-4 rounded-lg",
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "text-sm text-purple-300 mb-1",
                                    children: "Assistant:"
                                }, void 0, false, {
                                    fileName: "[project]/src/components/VoiceAssistant.tsx",
                                    lineNumber: 199,
                                    columnNumber: 15
                                }, ("TURBOPACK compile-time value", void 0)),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "text-white",
                                    children: voiceState.lastResponse
                                }, void 0, false, {
                                    fileName: "[project]/src/components/VoiceAssistant.tsx",
                                    lineNumber: 200,
                                    columnNumber: 15
                                }, ("TURBOPACK compile-time value", void 0))
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/components/VoiceAssistant.tsx",
                            lineNumber: 198,
                            columnNumber: 13
                        }, ("TURBOPACK compile-time value", void 0))
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/components/VoiceAssistant.tsx",
                    lineNumber: 189,
                    columnNumber: 9
                }, ("TURBOPACK compile-time value", void 0)),
                metrics && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "bg-gray-700 p-4 rounded-lg",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                            className: "text-sm font-medium mb-3",
                            children: "Performance Metrics"
                        }, void 0, false, {
                            fileName: "[project]/src/components/VoiceAssistant.tsx",
                            lineNumber: 208,
                            columnNumber: 13
                        }, ("TURBOPACK compile-time value", void 0)),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "grid grid-cols-2 gap-4 text-xs",
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: "text-gray-400",
                                            children: "STT Latency"
                                        }, void 0, false, {
                                            fileName: "[project]/src/components/VoiceAssistant.tsx",
                                            lineNumber: 211,
                                            columnNumber: 17
                                        }, ("TURBOPACK compile-time value", void 0)),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: "font-mono",
                                            children: [
                                                metrics.sttLatency.toFixed(0),
                                                "ms"
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/src/components/VoiceAssistant.tsx",
                                            lineNumber: 212,
                                            columnNumber: 17
                                        }, ("TURBOPACK compile-time value", void 0))
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/src/components/VoiceAssistant.tsx",
                                    lineNumber: 210,
                                    columnNumber: 15
                                }, ("TURBOPACK compile-time value", void 0)),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: "text-gray-400",
                                            children: "LLM Latency"
                                        }, void 0, false, {
                                            fileName: "[project]/src/components/VoiceAssistant.tsx",
                                            lineNumber: 215,
                                            columnNumber: 17
                                        }, ("TURBOPACK compile-time value", void 0)),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: "font-mono",
                                            children: [
                                                metrics.llmLatency.toFixed(0),
                                                "ms"
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/src/components/VoiceAssistant.tsx",
                                            lineNumber: 216,
                                            columnNumber: 17
                                        }, ("TURBOPACK compile-time value", void 0))
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/src/components/VoiceAssistant.tsx",
                                    lineNumber: 214,
                                    columnNumber: 15
                                }, ("TURBOPACK compile-time value", void 0)),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: "text-gray-400",
                                            children: "TTS Latency"
                                        }, void 0, false, {
                                            fileName: "[project]/src/components/VoiceAssistant.tsx",
                                            lineNumber: 219,
                                            columnNumber: 17
                                        }, ("TURBOPACK compile-time value", void 0)),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: "font-mono",
                                            children: [
                                                metrics.ttsLatency.toFixed(0),
                                                "ms"
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/src/components/VoiceAssistant.tsx",
                                            lineNumber: 220,
                                            columnNumber: 17
                                        }, ("TURBOPACK compile-time value", void 0))
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/src/components/VoiceAssistant.tsx",
                                    lineNumber: 218,
                                    columnNumber: 15
                                }, ("TURBOPACK compile-time value", void 0)),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: "text-gray-400",
                                            children: "Total Latency"
                                        }, void 0, false, {
                                            fileName: "[project]/src/components/VoiceAssistant.tsx",
                                            lineNumber: 223,
                                            columnNumber: 17
                                        }, ("TURBOPACK compile-time value", void 0)),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: "font-mono ".concat(metrics.totalLatency < 1200 ? 'text-green-400' : 'text-yellow-400'),
                                            children: [
                                                metrics.totalLatency.toFixed(0),
                                                "ms"
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/src/components/VoiceAssistant.tsx",
                                            lineNumber: 224,
                                            columnNumber: 17
                                        }, ("TURBOPACK compile-time value", void 0))
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/src/components/VoiceAssistant.tsx",
                                    lineNumber: 222,
                                    columnNumber: 15
                                }, ("TURBOPACK compile-time value", void 0))
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/components/VoiceAssistant.tsx",
                            lineNumber: 209,
                            columnNumber: 13
                        }, ("TURBOPACK compile-time value", void 0)),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "mt-2 text-xs text-gray-400",
                            children: [
                                "Audio Length: ",
                                metrics.audioLength.toFixed(1),
                                "s"
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/components/VoiceAssistant.tsx",
                            lineNumber: 229,
                            columnNumber: 13
                        }, ("TURBOPACK compile-time value", void 0))
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/components/VoiceAssistant.tsx",
                    lineNumber: 207,
                    columnNumber: 11
                }, ("TURBOPACK compile-time value", void 0)),
                voiceState.error && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "bg-red-900/30 border border-red-600 p-4 rounded-lg mt-4",
                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "text-red-300 text-sm",
                        children: [
                            "Error: ",
                            voiceState.error
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/VoiceAssistant.tsx",
                        lineNumber: 238,
                        columnNumber: 13
                    }, ("TURBOPACK compile-time value", void 0))
                }, void 0, false, {
                    fileName: "[project]/src/components/VoiceAssistant.tsx",
                    lineNumber: 237,
                    columnNumber: 11
                }, ("TURBOPACK compile-time value", void 0)),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "text-center mt-6",
                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                        onClick: ()=>{
                            voiceControls.reset();
                            setShowApiKeyInput(true);
                            setMetrics(null);
                        },
                        className: "text-gray-400 hover:text-white text-sm underline",
                        children: "Reset & Change API Key"
                    }, void 0, false, {
                        fileName: "[project]/src/components/VoiceAssistant.tsx",
                        lineNumber: 244,
                        columnNumber: 11
                    }, ("TURBOPACK compile-time value", void 0))
                }, void 0, false, {
                    fileName: "[project]/src/components/VoiceAssistant.tsx",
                    lineNumber: 243,
                    columnNumber: 9
                }, ("TURBOPACK compile-time value", void 0))
            ]
        }, void 0, true, {
            fileName: "[project]/src/components/VoiceAssistant.tsx",
            lineNumber: 141,
            columnNumber: 7
        }, ("TURBOPACK compile-time value", void 0))
    }, void 0, false, {
        fileName: "[project]/src/components/VoiceAssistant.tsx",
        lineNumber: 140,
        columnNumber: 5
    }, ("TURBOPACK compile-time value", void 0));
};
_s(VoiceAssistant, "qiH6CVOuPcvXGQ2Z2QpG0U1bfaM=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$useVoiceAssistant$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useVoiceAssistant"]
    ];
});
_c = VoiceAssistant;
const __TURBOPACK__default__export__ = VoiceAssistant;
var _c;
__turbopack_context__.k.register(_c, "VoiceAssistant");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/app/page.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": ()=>Home
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$VoiceAssistant$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/VoiceAssistant.tsx [app-client] (ecmascript)");
'use client';
;
;
function Home() {
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: "min-h-screen bg-gradient-to-br from-gray-900 to-black text-white",
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$VoiceAssistant$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {}, void 0, false, {
            fileName: "[project]/src/app/page.tsx",
            lineNumber: 8,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "[project]/src/app/page.tsx",
        lineNumber: 7,
        columnNumber: 5
    }, this);
}
_c = Home;
var _c;
__turbopack_context__.k.register(_c, "Home");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
}]);

//# sourceMappingURL=src_73f99918._.js.map