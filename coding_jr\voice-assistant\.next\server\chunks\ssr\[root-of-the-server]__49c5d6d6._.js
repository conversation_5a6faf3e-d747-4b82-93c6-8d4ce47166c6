module.exports = {

"[externals]/next/dist/compiled/next-server/app-page-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-page-turbo.runtime.dev.js, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[project]/src/lib/audioManager.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "AudioManager": ()=>AudioManager
});
class AudioManager {
    mediaStream = null;
    audioContext = null;
    analyser = null;
    processor = null;
    workletNode = null;
    isRecording = false;
    events = {};
    // Voice Activity Detection
    vadThreshold = 0.01;
    vadSilenceFrames = 0;
    vadSilenceThreshold = 30;
    vadSpeechFrames = 0;
    vadSpeechThreshold = 5;
    isSpeaking = false;
    // Audio buffer for streaming
    audioBuffer = [];
    bufferSize = 4096;
    constructor(events = {}){
        this.events = events;
    }
    async initialize() {
        try {
            // Request microphone access
            this.mediaStream = await navigator.mediaDevices.getUserMedia({
                audio: {
                    sampleRate: 16000,
                    channelCount: 1,
                    echoCancellation: true,
                    noiseSuppression: true,
                    autoGainControl: true
                }
            });
            // Create audio context
            this.audioContext = new AudioContext({
                sampleRate: 16000
            });
            // Create analyser for volume and VAD
            this.analyser = this.audioContext.createAnalyser();
            this.analyser.fftSize = 2048;
            this.analyser.smoothingTimeConstant = 0.8;
            // Create source from media stream
            const source = this.audioContext.createMediaStreamSource(this.mediaStream);
            source.connect(this.analyser);
            // Try to use AudioWorklet for better performance
            try {
                await this.audioContext.audioWorklet.addModule('/audio-processor.js');
                this.workletNode = new AudioWorkletNode(this.audioContext, 'audio-processor');
                this.workletNode.port.onmessage = (event)=>{
                    const { audioData, timestamp } = event.data;
                    this.handleAudioData(audioData, timestamp);
                };
                source.connect(this.workletNode);
            } catch  {
                console.warn('AudioWorklet not available, falling back to ScriptProcessor');
                // Fallback to ScriptProcessor
                this.processor = this.audioContext.createScriptProcessor(this.bufferSize, 1, 1);
                this.processor.onaudioprocess = (event)=>{
                    const inputBuffer = event.inputBuffer;
                    const audioData = inputBuffer.getChannelData(0);
                    this.handleAudioData(new Float32Array(audioData), this.audioContext.currentTime);
                };
                source.connect(this.processor);
                this.processor.connect(this.audioContext.destination);
            }
            console.log('Audio manager initialized successfully');
        } catch (error) {
            const audioError = new Error(`Failed to initialize audio: ${error}`);
            this.events.onError?.(audioError);
            throw audioError;
        }
    }
    handleAudioData(audioData, timestamp) {
        if (!this.isRecording) return;
        // Calculate RMS for volume and VAD
        const rms = this.calculateRMS(audioData);
        this.events.onVolumeChange?.(rms);
        // Voice Activity Detection
        this.performVAD(rms);
        // Buffer audio data
        this.audioBuffer.push(new Float32Array(audioData));
        // Send audio chunk
        const chunk = {
            data: new Float32Array(audioData),
            timestamp,
            sampleRate: this.audioContext?.sampleRate || 16000
        };
        this.events.onAudioChunk?.(chunk);
        // Send buffered audio when we have enough
        if (this.audioBuffer.length >= 10) {
            this.sendBufferedAudio();
        }
    }
    calculateRMS(audioData) {
        let sum = 0;
        for(let i = 0; i < audioData.length; i++){
            sum += audioData[i] * audioData[i];
        }
        return Math.sqrt(sum / audioData.length);
    }
    performVAD(rms) {
        const isSpeechFrame = rms > this.vadThreshold;
        if (isSpeechFrame) {
            this.vadSpeechFrames++;
            this.vadSilenceFrames = 0;
            if (!this.isSpeaking && this.vadSpeechFrames >= this.vadSpeechThreshold) {
                this.isSpeaking = true;
                this.events.onSpeechStart?.();
            }
        } else {
            this.vadSilenceFrames++;
            this.vadSpeechFrames = 0;
            if (this.isSpeaking && this.vadSilenceFrames >= this.vadSilenceThreshold) {
                this.isSpeaking = false;
                this.events.onSpeechEnd?.();
                this.sendBufferedAudio(); // Send remaining audio
            }
        }
    }
    sendBufferedAudio() {
        if (this.audioBuffer.length === 0) return;
        // Concatenate all buffered audio
        const totalLength = this.audioBuffer.reduce((sum, buffer)=>sum + buffer.length, 0);
        const concatenated = new Float32Array(totalLength);
        let offset = 0;
        for (const buffer of this.audioBuffer){
            concatenated.set(buffer, offset);
            offset += buffer.length;
        }
        // Clear buffer
        this.audioBuffer = [];
        // Send as chunk
        const chunk = {
            data: concatenated,
            timestamp: this.audioContext?.currentTime || Date.now(),
            sampleRate: this.audioContext?.sampleRate || 16000
        };
        this.events.onAudioChunk?.(chunk);
    }
    startRecording() {
        if (!this.audioContext) {
            throw new Error('Audio manager not initialized');
        }
        if (this.audioContext.state === 'suspended') {
            this.audioContext.resume();
        }
        this.isRecording = true;
        this.audioBuffer = [];
        console.log('Recording started');
    }
    stopRecording() {
        this.isRecording = false;
        this.sendBufferedAudio(); // Send any remaining audio
        console.log('Recording stopped');
    }
    setVADThreshold(threshold) {
        this.vadThreshold = threshold;
    }
    getVADThreshold() {
        return this.vadThreshold;
    }
    isCurrentlyRecording() {
        return this.isRecording;
    }
    isCurrentlySpeaking() {
        return this.isSpeaking;
    }
    async cleanup() {
        this.stopRecording();
        if (this.processor) {
            this.processor.disconnect();
            this.processor = null;
        }
        if (this.workletNode) {
            this.workletNode.disconnect();
            this.workletNode = null;
        }
        if (this.analyser) {
            this.analyser.disconnect();
            this.analyser = null;
        }
        if (this.audioContext) {
            await this.audioContext.close();
            this.audioContext = null;
        }
        if (this.mediaStream) {
            this.mediaStream.getTracks().forEach((track)=>track.stop());
            this.mediaStream = null;
        }
        console.log('Audio manager cleaned up');
    }
}
}),
"[project]/src/hooks/useAudioRecording.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "useAudioRecording": ()=>useAudioRecording
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$audioManager$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/audioManager.ts [app-ssr] (ecmascript)");
;
;
function useAudioRecording(options = {}) {
    const audioManagerRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useRef"])(null);
    const [state, setState] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])({
        isInitialized: false,
        isRecording: false,
        isSpeaking: false,
        volume: 0,
        error: null
    });
    const updateState = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])((updates)=>{
        setState((prev)=>({
                ...prev,
                ...updates
            }));
    }, []);
    const initialize = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])(async ()=>{
        try {
            updateState({
                error: null
            });
            const events = {
                onAudioChunk: (chunk)=>{
                    options.onAudioChunk?.(chunk);
                },
                onSpeechStart: ()=>{
                    updateState({
                        isSpeaking: true
                    });
                    options.onSpeechStart?.();
                },
                onSpeechEnd: ()=>{
                    updateState({
                        isSpeaking: false
                    });
                    options.onSpeechEnd?.();
                },
                onError: (error)=>{
                    updateState({
                        error: error.message
                    });
                    console.error('Audio manager error:', error);
                    options.onError?.(error);
                },
                onVolumeChange: (volume)=>{
                    updateState({
                        volume
                    });
                    options.onVolumeChange?.(volume);
                }
            };
            audioManagerRef.current = new __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$audioManager$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["AudioManager"](events);
            await audioManagerRef.current.initialize();
            updateState({
                isInitialized: true
            });
            if (options.autoStart) {
                audioManagerRef.current.startRecording();
                updateState({
                    isRecording: true,
                    error: null
                });
            }
        } catch (error) {
            const errorMessage = error instanceof Error ? error.message : 'Unknown audio error';
            updateState({
                error: errorMessage,
                isInitialized: false
            });
        }
    }, [
        options,
        updateState
    ]);
    const startRecording = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])(()=>{
        if (!audioManagerRef.current) {
            updateState({
                error: 'Audio manager not initialized'
            });
            return;
        }
        try {
            audioManagerRef.current.startRecording();
            updateState({
                isRecording: true,
                error: null
            });
        } catch (error) {
            const errorMessage = error instanceof Error ? error.message : 'Failed to start recording';
            updateState({
                error: errorMessage
            });
        }
    }, [
        updateState
    ]);
    const stopRecording = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])(()=>{
        if (!audioManagerRef.current) {
            return;
        }
        audioManagerRef.current.stopRecording();
        updateState({
            isRecording: false,
            isSpeaking: false
        });
    }, [
        updateState
    ]);
    const cleanup = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])(async ()=>{
        if (audioManagerRef.current) {
            await audioManagerRef.current.cleanup();
            audioManagerRef.current = null;
        }
        setState({
            isInitialized: false,
            isRecording: false,
            isSpeaking: false,
            volume: 0,
            error: null
        });
    }, []);
    const setVADThreshold = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])((threshold)=>{
        audioManagerRef.current?.setVADThreshold(threshold);
    }, []);
    const getVADThreshold = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])(()=>{
        return audioManagerRef.current?.getVADThreshold() || 0.01;
    }, []);
    // Cleanup on unmount
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        return ()=>{
            cleanup();
        };
    }, [
        cleanup
    ]);
    // Update recording state based on audio manager
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        const interval = setInterval(()=>{
            if (audioManagerRef.current) {
                const isRecording = audioManagerRef.current.isCurrentlyRecording();
                const isSpeaking = audioManagerRef.current.isCurrentlySpeaking();
                setState((prev)=>{
                    if (prev.isRecording !== isRecording || prev.isSpeaking !== isSpeaking) {
                        return {
                            ...prev,
                            isRecording,
                            isSpeaking
                        };
                    }
                    return prev;
                });
            }
        }, 100);
        return ()=>clearInterval(interval);
    }, []);
    const controls = {
        initialize,
        startRecording,
        stopRecording,
        cleanup,
        setVADThreshold,
        getVADThreshold
    };
    return [
        state,
        controls
    ];
}
}),
"[project]/src/hooks/useWhisperSTT.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "useWhisperSTT": ()=>useWhisperSTT
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
;
function useWhisperSTT(options = {}) {
    const workerRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useRef"])(null);
    const [state, setState] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])({
        isInitialized: false,
        isTranscribing: false,
        error: null,
        lastTranscription: null,
        initializationProgress: 0
    });
    const updateState = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])((updates)=>{
        setState((prev)=>({
                ...prev,
                ...updates
            }));
    }, []);
    const initialize = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])(async ()=>{
        try {
            updateState({
                error: null,
                initializationProgress: 0
            });
            // Create worker
            workerRef.current = new Worker('/whisper-worker.js');
            // Set up message handler
            workerRef.current.onmessage = (event)=>{
                const { type, ...data } = event.data;
                switch(type){
                    case 'initialized':
                        if (data.success) {
                            updateState({
                                isInitialized: true,
                                initializationProgress: 100,
                                error: null
                            });
                            console.log('Whisper STT initialized successfully');
                        } else {
                            updateState({
                                error: data.error || 'Failed to initialize Whisper',
                                isInitialized: false,
                                initializationProgress: 0
                            });
                        }
                        break;
                    case 'transcription':
                        updateState({
                            isTranscribing: false,
                            lastTranscription: data.result,
                            error: null
                        });
                        options.onTranscription?.(data.result);
                        break;
                    case 'error':
                        const errorMessage = data.error || 'Unknown transcription error';
                        updateState({
                            error: errorMessage,
                            isTranscribing: false
                        });
                        options.onError?.(errorMessage);
                        break;
                    case 'progress':
                        updateState({
                            initializationProgress: data.progress || 0
                        });
                        break;
                    default:
                        console.warn('Unknown message type from Whisper worker:', type);
                }
            };
            workerRef.current.onerror = (error)=>{
                const errorMessage = `Worker error: ${error.message}`;
                updateState({
                    error: errorMessage,
                    isInitialized: false,
                    initializationProgress: 0
                });
                options.onError?.(errorMessage);
            };
            // Initialize the worker
            updateState({
                initializationProgress: 10
            });
            workerRef.current.postMessage({
                type: 'initialize'
            });
        } catch (error) {
            const errorMessage = error instanceof Error ? error.message : 'Failed to create Whisper worker';
            updateState({
                error: errorMessage,
                isInitialized: false,
                initializationProgress: 0
            });
            options.onError?.(errorMessage);
        }
    }, [
        options,
        updateState
    ]);
    const transcribeAudio = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])(async (audioData, transcriptionOptions = {})=>{
        if (!workerRef.current || !state.isInitialized) {
            throw new Error('Whisper STT not initialized');
        }
        updateState({
            isTranscribing: true,
            error: null
        });
        workerRef.current.postMessage({
            type: 'transcribe',
            data: {
                audioData: Array.from(audioData),
                options: transcriptionOptions
            }
        });
    }, [
        state.isInitialized,
        updateState
    ]);
    const bufferAudio = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])((chunk)=>{
        if (!workerRef.current || !state.isInitialized) {
            return;
        }
        workerRef.current.postMessage({
            type: 'buffer-audio',
            data: {
                data: Array.from(chunk.data),
                timestamp: chunk.timestamp,
                sampleRate: chunk.sampleRate
            }
        });
    }, [
        state.isInitialized
    ]);
    const flushBuffer = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])(()=>{
        if (!workerRef.current || !state.isInitialized) {
            return;
        }
        workerRef.current.postMessage({
            type: 'flush-buffer'
        });
    }, [
        state.isInitialized
    ]);
    const reset = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])(()=>{
        if (!workerRef.current) {
            return;
        }
        workerRef.current.postMessage({
            type: 'reset'
        });
        updateState({
            isTranscribing: false,
            lastTranscription: null,
            error: null
        });
    }, [
        updateState
    ]);
    // Auto-initialize if requested
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        if (options.autoInitialize) {
            initialize();
        }
    }, [
        options.autoInitialize,
        initialize
    ]);
    // Cleanup on unmount
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        return ()=>{
            if (workerRef.current) {
                workerRef.current.terminate();
                workerRef.current = null;
            }
        };
    }, []);
    const controls = {
        initialize,
        transcribeAudio,
        bufferAudio,
        flushBuffer,
        reset
    };
    return [
        state,
        controls
    ];
}
}),
"[project]/src/lib/openaiClient.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "OpenAIClient": ()=>OpenAIClient
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$openai$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/openai/index.mjs [app-ssr] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$openai$2f$client$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__OpenAI__as__default$3e$__ = __turbopack_context__.i("[project]/node_modules/openai/client.mjs [app-ssr] (ecmascript) <export OpenAI as default>");
;
class OpenAIClient {
    client;
    conversationHistory = [];
    defaultSystemPrompt = `You are a helpful voice assistant. Keep your responses concise and conversational, as they will be spoken aloud. Aim for responses that are 1-2 sentences unless more detail is specifically requested.`;
    constructor(apiKey){
        this.client = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$openai$2f$client$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__OpenAI__as__default$3e$__["default"]({
            apiKey,
            dangerouslyAllowBrowser: true
        });
    }
    async sendMessage(userMessage, options = {}) {
        const startTime = performance.now();
        try {
            // Add user message to history
            const userChatMessage = {
                role: 'user',
                content: userMessage,
                timestamp: Date.now()
            };
            this.conversationHistory.push(userChatMessage);
            // Prepare messages for API
            const messages = [];
            // Add system prompt
            const systemPrompt = options.systemPrompt || this.defaultSystemPrompt;
            messages.push({
                role: 'system',
                content: systemPrompt
            });
            // Add conversation history (keep last 10 messages to manage context length)
            const recentHistory = this.conversationHistory.slice(-10);
            messages.push(...recentHistory);
            // Make API call
            const completion = await this.client.chat.completions.create({
                model: options.model || 'gpt-3.5-turbo',
                messages: messages.map((msg)=>({
                        role: msg.role,
                        content: msg.content
                    })),
                temperature: options.temperature || 0.7,
                max_tokens: options.maxTokens || 150,
                stream: false
            });
            const endTime = performance.now();
            const processingTime = endTime - startTime;
            const assistantMessage = completion.choices[0]?.message?.content || '';
            // Add assistant response to history
            const assistantChatMessage = {
                role: 'assistant',
                content: assistantMessage,
                timestamp: Date.now()
            };
            this.conversationHistory.push(assistantChatMessage);
            const response = {
                message: assistantMessage,
                usage: completion.usage ? {
                    promptTokens: completion.usage.prompt_tokens,
                    completionTokens: completion.usage.completion_tokens,
                    totalTokens: completion.usage.total_tokens
                } : undefined,
                processingTime
            };
            console.log(`OpenAI response received in ${processingTime.toFixed(2)}ms`);
            return response;
        } catch (error) {
            const endTime = performance.now();
            const processingTime = endTime - startTime;
            console.error('OpenAI API error:', error);
            // Return error response
            return {
                message: 'Sorry, I encountered an error processing your request.',
                processingTime
            };
        }
    }
    getConversationHistory() {
        return [
            ...this.conversationHistory
        ];
    }
    clearConversationHistory() {
        this.conversationHistory = [];
    }
    setSystemPrompt(prompt) {
        this.defaultSystemPrompt = prompt;
    }
    getSystemPrompt() {
        return this.defaultSystemPrompt;
    }
    // Remove old messages to manage memory and context length
    trimConversationHistory(maxMessages = 20) {
        if (this.conversationHistory.length > maxMessages) {
            this.conversationHistory = this.conversationHistory.slice(-maxMessages);
        }
    }
}
}),
"[project]/src/hooks/useOpenAIChat.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "useOpenAIChat": ()=>useOpenAIChat
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$openaiClient$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/openaiClient.ts [app-ssr] (ecmascript)");
;
;
function useOpenAIChat(options = {}) {
    const clientRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useRef"])(null);
    const [state, setState] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])({
        isInitialized: false,
        isProcessing: false,
        error: null,
        conversationHistory: [],
        lastResponse: null
    });
    const updateState = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])((updates)=>{
        setState((prev)=>({
                ...prev,
                ...updates
            }));
    }, []);
    const initialize = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])((apiKey)=>{
        try {
            if (!apiKey || apiKey.trim() === '') {
                throw new Error('OpenAI API key is required');
            }
            clientRef.current = new __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$openaiClient$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["OpenAIClient"](apiKey);
            updateState({
                isInitialized: true,
                error: null,
                conversationHistory: []
            });
            console.log('OpenAI client initialized successfully');
        } catch (error) {
            const errorMessage = error instanceof Error ? error.message : 'Failed to initialize OpenAI client';
            updateState({
                error: errorMessage,
                isInitialized: false
            });
            options.onError?.(errorMessage);
        }
    }, [
        options,
        updateState
    ]);
    const sendMessage = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])(async (message, chatOptions = {})=>{
        if (!clientRef.current || !state.isInitialized) {
            const error = 'OpenAI client not initialized';
            updateState({
                error
            });
            options.onError?.(error);
            return null;
        }
        if (!message || message.trim() === '') {
            const error = 'Message cannot be empty';
            updateState({
                error
            });
            options.onError?.(error);
            return null;
        }
        try {
            updateState({
                isProcessing: true,
                error: null
            });
            const response = await clientRef.current.sendMessage(message, chatOptions);
            // Update conversation history
            const updatedHistory = clientRef.current.getConversationHistory();
            updateState({
                isProcessing: false,
                lastResponse: response,
                conversationHistory: updatedHistory,
                error: null
            });
            // Auto-trim history if enabled
            if (options.autoTrimHistory && options.maxHistoryLength) {
                clientRef.current.trimConversationHistory(options.maxHistoryLength);
            }
            options.onResponse?.(response);
            return response;
        } catch (error) {
            const errorMessage = error instanceof Error ? error.message : 'Failed to send message';
            updateState({
                error: errorMessage,
                isProcessing: false
            });
            options.onError?.(errorMessage);
            return null;
        }
    }, [
        state.isInitialized,
        options,
        updateState
    ]);
    const clearHistory = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])(()=>{
        if (clientRef.current) {
            clientRef.current.clearConversationHistory();
            updateState({
                conversationHistory: [],
                lastResponse: null,
                error: null
            });
        }
    }, [
        updateState
    ]);
    const setSystemPrompt = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])((prompt)=>{
        if (clientRef.current) {
            clientRef.current.setSystemPrompt(prompt);
        }
    }, []);
    const getSystemPrompt = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])(()=>{
        return clientRef.current?.getSystemPrompt() || '';
    }, []);
    const controls = {
        initialize,
        sendMessage,
        clearHistory,
        setSystemPrompt,
        getSystemPrompt
    };
    return [
        state,
        controls
    ];
}
}),
"[project]/src/hooks/useTTS.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "useTTS": ()=>useTTS
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
;
function useTTS(options = {}) {
    const workerRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useRef"])(null);
    const audioContextRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useRef"])(null);
    const [state, setState] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])({
        isInitialized: false,
        isSynthesizing: false,
        error: null,
        lastSynthesis: null,
        initializationProgress: 0
    });
    const updateState = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])((updates)=>{
        setState((prev)=>({
                ...prev,
                ...updates
            }));
    }, []);
    const playAudio = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])(async (audioData, sampleRate)=>{
        if (!audioContextRef.current) {
            throw new Error('Audio context not available');
        }
        try {
            // Resume audio context if suspended
            if (audioContextRef.current.state === 'suspended') {
                await audioContextRef.current.resume();
            }
            // Create audio buffer
            const audioBuffer = audioContextRef.current.createBuffer(1, audioData.length, sampleRate);
            // Copy audio data to buffer
            const channelData = audioBuffer.getChannelData(0);
            channelData.set(audioData);
            // Create and play audio source
            const source = audioContextRef.current.createBufferSource();
            source.buffer = audioBuffer;
            source.connect(audioContextRef.current.destination);
            return new Promise((resolve)=>{
                source.onended = ()=>resolve();
                source.start();
            });
        } catch (error) {
            console.error('Audio playback error:', error);
            throw error;
        }
    }, []);
    const initialize = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])(async ()=>{
        try {
            updateState({
                error: null,
                initializationProgress: 0
            });
            // Create audio context for playback
            audioContextRef.current = new AudioContext();
            // Create worker
            workerRef.current = new Worker('/tts-worker.js');
            // Set up message handler
            workerRef.current.onmessage = async (event)=>{
                const { type, ...data } = event.data;
                switch(type){
                    case 'initialized':
                        if (data.success) {
                            updateState({
                                isInitialized: true,
                                initializationProgress: 100,
                                error: null
                            });
                            console.log('TTS initialized successfully');
                        } else {
                            updateState({
                                error: data.error || 'Failed to initialize TTS',
                                isInitialized: false,
                                initializationProgress: 0
                            });
                        }
                        break;
                    case 'synthesis':
                        const result = {
                            audioData: new Float32Array(data.result.audioData),
                            sampleRate: data.result.sampleRate,
                            duration: data.result.duration,
                            processingTime: data.result.processingTime,
                            text: data.result.text
                        };
                        updateState({
                            isSynthesizing: false,
                            lastSynthesis: result,
                            error: null
                        });
                        options.onSynthesis?.(result);
                        // Auto-play if enabled
                        if (options.autoPlay) {
                            await playAudio(result.audioData, result.sampleRate);
                        }
                        break;
                    case 'error':
                        const errorMessage = data.error || 'Unknown TTS error';
                        updateState({
                            error: errorMessage,
                            isSynthesizing: false
                        });
                        options.onError?.(errorMessage);
                        break;
                    case 'progress':
                        updateState({
                            initializationProgress: data.progress || 0
                        });
                        break;
                    default:
                        console.warn('Unknown message type from TTS worker:', type);
                }
            };
            workerRef.current.onerror = (error)=>{
                const errorMessage = `TTS Worker error: ${error.message}`;
                updateState({
                    error: errorMessage,
                    isInitialized: false,
                    initializationProgress: 0
                });
                options.onError?.(errorMessage);
            };
            // Initialize the worker
            updateState({
                initializationProgress: 10
            });
            workerRef.current.postMessage({
                type: 'initialize'
            });
        } catch (error) {
            const errorMessage = error instanceof Error ? error.message : 'Failed to create TTS worker';
            updateState({
                error: errorMessage,
                isInitialized: false,
                initializationProgress: 0
            });
            options.onError?.(errorMessage);
        }
    }, [
        options,
        updateState,
        playAudio
    ]);
    const synthesizeText = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])(async (text, synthesisOptions = {})=>{
        if (!workerRef.current || !state.isInitialized) {
            throw new Error('TTS not initialized');
        }
        if (!text || text.trim() === '') {
            throw new Error('Text cannot be empty');
        }
        updateState({
            isSynthesizing: true,
            error: null
        });
        workerRef.current.postMessage({
            type: 'synthesize',
            data: {
                text: text.trim(),
                options: synthesisOptions
            }
        });
    }, [
        state.isInitialized,
        updateState
    ]);
    const reset = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])(()=>{
        if (!workerRef.current) {
            return;
        }
        workerRef.current.postMessage({
            type: 'reset'
        });
        updateState({
            isSynthesizing: false,
            lastSynthesis: null,
            error: null
        });
    }, [
        updateState
    ]);
    // Auto-initialize if requested
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        if (options.autoInitialize) {
            initialize();
        }
    }, [
        options.autoInitialize,
        initialize
    ]);
    // Cleanup on unmount
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        return ()=>{
            if (workerRef.current) {
                workerRef.current.terminate();
                workerRef.current = null;
            }
            if (audioContextRef.current) {
                audioContextRef.current.close();
                audioContextRef.current = null;
            }
        };
    }, []);
    const controls = {
        initialize,
        synthesizeText,
        playAudio,
        reset
    };
    return [
        state,
        controls
    ];
}
}),
"[project]/src/lib/performanceMonitor.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "performanceMonitor": ()=>performanceMonitor
});
class PerformanceMonitor {
    metrics = [];
    sessionId;
    maxMetrics = 1000;
    constructor(){
        this.sessionId = this.generateSessionId();
    }
    generateSessionId() {
        return `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    }
    getDeviceInfo() {
        const nav = navigator;
        return {
            platform: navigator.platform,
            memory: nav.deviceMemory,
            cores: nav.hardwareConcurrency
        };
    }
    recordMetrics(metrics) {
        const fullMetrics = {
            ...metrics,
            timestamp: Date.now(),
            sessionId: this.sessionId,
            userAgent: navigator.userAgent,
            deviceInfo: this.getDeviceInfo()
        };
        this.metrics.push(fullMetrics);
        // Keep only the last N metrics
        if (this.metrics.length > this.maxMetrics) {
            this.metrics = this.metrics.slice(-this.maxMetrics);
        }
        // Log performance
        this.logPerformance(fullMetrics);
        // Store in localStorage for persistence
        this.persistMetrics();
    }
    logPerformance(metrics) {
        const { totalLatency, sttLatency, llmLatency, ttsLatency, audioLength } = metrics;
        console.group('🔍 Performance Metrics');
        console.log(`Total Latency: ${totalLatency.toFixed(0)}ms ${totalLatency < 1200 ? '✅' : '⚠️'}`);
        console.log(`STT: ${sttLatency.toFixed(0)}ms`);
        console.log(`LLM: ${llmLatency.toFixed(0)}ms`);
        console.log(`TTS: ${ttsLatency.toFixed(0)}ms`);
        console.log(`Audio Length: ${audioLength.toFixed(1)}s`);
        console.log(`Efficiency: ${(audioLength * 1000 / totalLatency).toFixed(2)}x realtime`);
        if (totalLatency > 1200) {
            console.warn('⚠️ Total latency exceeds 1.2s target');
            this.analyzeBottlenecks(metrics);
        }
        console.groupEnd();
    }
    analyzeBottlenecks(metrics) {
        const { sttLatency, llmLatency, ttsLatency } = metrics;
        const total = sttLatency + llmLatency + ttsLatency;
        console.group('🔍 Bottleneck Analysis');
        if (sttLatency > total * 0.4) {
            console.warn('STT is the bottleneck (>40% of total time)');
            console.log('Recommendations: Use smaller Whisper model, optimize audio preprocessing');
        }
        if (llmLatency > total * 0.5) {
            console.warn('LLM is the bottleneck (>50% of total time)');
            console.log('Recommendations: Use faster model (gpt-3.5-turbo), reduce max_tokens, optimize prompt');
        }
        if (ttsLatency > total * 0.3) {
            console.warn('TTS is the bottleneck (>30% of total time)');
            console.log('Recommendations: Use smaller TTS model, optimize text preprocessing');
        }
        console.groupEnd();
    }
    persistMetrics() {
        try {
            const recentMetrics = this.metrics.slice(-100); // Store last 100
            localStorage.setItem('voiceAssistantMetrics', JSON.stringify(recentMetrics));
        } catch (error) {
            console.warn('Failed to persist metrics:', error);
        }
    }
    loadPersistedMetrics() {
        try {
            const stored = localStorage.getItem('voiceAssistantMetrics');
            if (stored) {
                const parsed = JSON.parse(stored);
                if (Array.isArray(parsed)) {
                    this.metrics = parsed;
                }
            }
        } catch (error) {
            console.warn('Failed to load persisted metrics:', error);
        }
    }
    generateReport() {
        if (this.metrics.length === 0) {
            return {
                totalSessions: 0,
                averageLatency: 0,
                medianLatency: 0,
                p95Latency: 0,
                successRate: 0,
                cacheHitRate: 0,
                breakdown: {
                    stt: {
                        avg: 0,
                        median: 0,
                        p95: 0
                    },
                    llm: {
                        avg: 0,
                        median: 0,
                        p95: 0
                    },
                    tts: {
                        avg: 0,
                        median: 0,
                        p95: 0
                    }
                },
                recommendations: []
            };
        }
        const latencies = this.metrics.map((m)=>m.totalLatency).sort((a, b)=>a - b);
        const sttLatencies = this.metrics.map((m)=>m.sttLatency).sort((a, b)=>a - b);
        const llmLatencies = this.metrics.map((m)=>m.llmLatency).sort((a, b)=>a - b);
        const ttsLatencies = this.metrics.map((m)=>m.ttsLatency).sort((a, b)=>a - b);
        const avg = (arr)=>arr.reduce((a, b)=>a + b, 0) / arr.length;
        const median = (arr)=>arr[Math.floor(arr.length / 2)];
        const p95 = (arr)=>arr[Math.floor(arr.length * 0.95)];
        const cacheHits = this.metrics.filter((m)=>m.cacheHit).length;
        const successfulSessions = this.metrics.filter((m)=>m.totalLatency > 0).length;
        const report = {
            totalSessions: this.metrics.length,
            averageLatency: avg(latencies),
            medianLatency: median(latencies),
            p95Latency: p95(latencies),
            successRate: successfulSessions / this.metrics.length,
            cacheHitRate: cacheHits / this.metrics.length,
            breakdown: {
                stt: {
                    avg: avg(sttLatencies),
                    median: median(sttLatencies),
                    p95: p95(sttLatencies)
                },
                llm: {
                    avg: avg(llmLatencies),
                    median: median(llmLatencies),
                    p95: p95(llmLatencies)
                },
                tts: {
                    avg: avg(ttsLatencies),
                    median: median(ttsLatencies),
                    p95: p95(ttsLatencies)
                }
            },
            recommendations: this.generateRecommendations(report)
        };
        return report;
    }
    generateRecommendations(report) {
        const recommendations = [];
        if (report.averageLatency > 1200) {
            recommendations.push('Average latency exceeds 1.2s target. Consider optimizing the pipeline.');
        }
        if (report.breakdown.stt.avg > 500) {
            recommendations.push('STT latency is high. Consider using whisper-tiny or optimizing audio preprocessing.');
        }
        if (report.breakdown.llm.avg > 800) {
            recommendations.push('LLM latency is high. Consider using gpt-3.5-turbo or reducing max_tokens.');
        }
        if (report.breakdown.tts.avg > 400) {
            recommendations.push('TTS latency is high. Consider using a smaller TTS model or optimizing text preprocessing.');
        }
        if (report.cacheHitRate < 0.8) {
            recommendations.push('Low cache hit rate. Ensure models are properly cached.');
        }
        if (report.successRate < 0.95) {
            recommendations.push('Low success rate. Check for errors in the pipeline.');
        }
        return recommendations;
    }
    exportMetrics() {
        return JSON.stringify(this.metrics, null, 2);
    }
    clearMetrics() {
        this.metrics = [];
        localStorage.removeItem('voiceAssistantMetrics');
    }
    getRecentMetrics(count = 10) {
        return this.metrics.slice(-count);
    }
    // Initialize with persisted data
    init() {
        this.loadPersistedMetrics();
    }
}
const performanceMonitor = new PerformanceMonitor();
// Initialize on import
performanceMonitor.init();
}),
"[project]/src/hooks/useVoiceAssistant.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "useVoiceAssistant": ()=>useVoiceAssistant
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$useAudioRecording$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/hooks/useAudioRecording.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$useWhisperSTT$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/hooks/useWhisperSTT.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$useOpenAIChat$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/hooks/useOpenAIChat.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$useTTS$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/hooks/useTTS.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$performanceMonitor$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/performanceMonitor.ts [app-ssr] (ecmascript)");
;
;
;
;
;
;
function useVoiceAssistant(options = {}) {
    const [state, setState] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])({
        isInitialized: false,
        isListening: false,
        isProcessing: false,
        currentStage: 'idle',
        error: null,
        volume: 0,
        lastTranscription: null,
        lastResponse: null
    });
    const metricsRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useRef"])({});
    const pipelineStartTimeRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useRef"])(0);
    const updateState = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])((updates)=>{
        setState((prev)=>{
            const newState = {
                ...prev,
                ...updates
            };
            // Notify stage change
            if (updates.currentStage && updates.currentStage !== prev.currentStage) {
                options.onStageChange?.(updates.currentStage);
            }
            return newState;
        });
    }, [
        options
    ]);
    // Audio recording hook
    const [, audioControls] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$useAudioRecording$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useAudioRecording"])({
        onAudioChunk: (chunk)=>{
            if (state.currentStage === 'listening') {
                whisperControls.bufferAudio(chunk);
            }
        },
        onSpeechStart: ()=>{
            console.log('Speech started');
            pipelineStartTimeRef.current = performance.now();
            metricsRef.current = {};
            updateState({
                currentStage: 'listening'
            });
        },
        onSpeechEnd: ()=>{
            console.log('Speech ended');
            if (state.currentStage === 'listening') {
                updateState({
                    currentStage: 'transcribing'
                });
                whisperControls.flushBuffer();
            }
        },
        onVolumeChange: (volume)=>{
            updateState({
                volume
            });
        },
        onError: (error)=>{
            updateState({
                error: error.message
            });
            options.onError?.(error.message);
        }
    });
    // Whisper STT hook
    const [, whisperControls] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$useWhisperSTT$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useWhisperSTT"])({
        onTranscription: (result)=>{
            const sttEndTime = performance.now();
            metricsRef.current.sttLatency = sttEndTime - pipelineStartTimeRef.current;
            metricsRef.current.audioLength = result.audioLength;
            console.log(`STT completed: "${result.text}" (${metricsRef.current.sttLatency.toFixed(2)}ms)`);
            updateState({
                lastTranscription: result.text,
                currentStage: 'thinking'
            });
            options.onTranscription?.(result.text);
            // Send to OpenAI
            if (result.text.trim()) {
                const llmStartTime = performance.now();
                chatControls.sendMessage(result.text).then(()=>{
                    const llmEndTime = performance.now();
                    metricsRef.current.llmLatency = llmEndTime - llmStartTime;
                });
            }
        },
        onError: (error)=>{
            updateState({
                error,
                currentStage: 'idle'
            });
            options.onError?.(error);
        }
    });
    // OpenAI Chat hook
    const [, chatControls] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$useOpenAIChat$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useOpenAIChat"])({
        onResponse: (response)=>{
            console.log(`LLM completed: "${response.message}" (${response.processingTime.toFixed(2)}ms)`);
            updateState({
                lastResponse: response.message,
                currentStage: 'speaking'
            });
            options.onResponse?.(response.message);
            // Send to TTS
            ttsControls.synthesizeText(response.message);
        },
        onError: (error)=>{
            updateState({
                error,
                currentStage: 'idle'
            });
            options.onError?.(error);
        },
        autoTrimHistory: true,
        maxHistoryLength: 10
    });
    // TTS hook
    const [, ttsControls] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$useTTS$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useTTS"])({
        onSynthesis: (result)=>{
            const totalEndTime = performance.now();
            metricsRef.current.ttsLatency = result.processingTime;
            metricsRef.current.totalLatency = totalEndTime - pipelineStartTimeRef.current;
            console.log(`TTS completed (${result.processingTime.toFixed(2)}ms)`);
            console.log(`Total pipeline latency: ${metricsRef.current.totalLatency?.toFixed(2)}ms`);
            // Report metrics
            if (metricsRef.current.totalLatency) {
                const metrics = {
                    sttLatency: metricsRef.current.sttLatency || 0,
                    llmLatency: metricsRef.current.llmLatency || 0,
                    ttsLatency: metricsRef.current.ttsLatency || 0,
                    totalLatency: metricsRef.current.totalLatency,
                    audioLength: metricsRef.current.audioLength || 0
                };
                // Record performance metrics
                __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$performanceMonitor$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["performanceMonitor"].recordMetrics({
                    audioLength: metrics.audioLength,
                    sttLatency: metrics.sttLatency,
                    llmLatency: metrics.llmLatency,
                    ttsLatency: metrics.ttsLatency,
                    totalLatency: metrics.totalLatency
                });
                options.onMetrics?.(metrics);
            }
            // Return to listening after speech
            setTimeout(()=>{
                updateState({
                    currentStage: 'idle'
                });
            }, 500);
        },
        onError: (error)=>{
            updateState({
                error,
                currentStage: 'idle'
            });
            options.onError?.(error);
        },
        autoPlay: true
    });
    // Initialize all components
    const initialize = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])(async (openaiApiKey)=>{
        try {
            updateState({
                error: null
            });
            // Initialize all components
            await Promise.all([
                audioControls.initialize(),
                whisperControls.initialize(),
                ttsControls.initialize()
            ]);
            // Initialize OpenAI
            chatControls.initialize(openaiApiKey);
            updateState({
                isInitialized: true
            });
            // Auto-start listening if requested
            if (options.autoStartListening) {
                audioControls.startRecording();
                updateState({
                    isListening: true,
                    currentStage: 'idle',
                    error: null
                });
            }
        } catch (error) {
            const errorMessage = error instanceof Error ? error.message : 'Initialization failed';
            updateState({
                error: errorMessage,
                isInitialized: false
            });
            options.onError?.(errorMessage);
        }
    }, [
        audioControls,
        whisperControls,
        ttsControls,
        chatControls,
        options,
        updateState
    ]);
    const startListening = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])(()=>{
        if (!state.isInitialized) {
            updateState({
                error: 'Voice assistant not initialized'
            });
            return;
        }
        audioControls.startRecording();
        updateState({
            isListening: true,
            currentStage: 'idle',
            error: null
        });
    }, [
        state.isInitialized,
        audioControls,
        updateState
    ]);
    const stopListening = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])(()=>{
        audioControls.stopRecording();
        updateState({
            isListening: false,
            currentStage: 'idle'
        });
    }, [
        audioControls,
        updateState
    ]);
    const sendTextMessage = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])(async (message)=>{
        if (!state.isInitialized) {
            throw new Error('Voice assistant not initialized');
        }
        updateState({
            currentStage: 'thinking'
        });
        try {
            const response = await chatControls.sendMessage(message);
            if (response) {
                updateState({
                    lastTranscription: message,
                    lastResponse: response.message,
                    currentStage: 'speaking'
                });
                // Synthesize response
                await ttsControls.synthesizeText(response.message);
            }
        } catch (error) {
            const errorMessage = error instanceof Error ? error.message : 'Failed to send message';
            updateState({
                error: errorMessage,
                currentStage: 'idle'
            });
            throw error;
        }
    }, [
        state.isInitialized,
        chatControls,
        ttsControls,
        updateState
    ]);
    const reset = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])(()=>{
        audioControls.stopRecording();
        whisperControls.reset();
        chatControls.clearHistory();
        ttsControls.reset();
        setState({
            isInitialized: false,
            isListening: false,
            isProcessing: false,
            currentStage: 'idle',
            error: null,
            volume: 0,
            lastTranscription: null,
            lastResponse: null
        });
    }, [
        audioControls,
        whisperControls,
        chatControls,
        ttsControls
    ]);
    // Update processing state based on current stage
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        const isProcessing = [
            'transcribing',
            'thinking',
            'speaking'
        ].includes(state.currentStage);
        if (state.isProcessing !== isProcessing) {
            updateState({
                isProcessing
            });
        }
    }, [
        state.currentStage,
        state.isProcessing,
        updateState
    ]);
    const controls = {
        initialize,
        startListening,
        stopListening,
        sendTextMessage,
        reset
    };
    return [
        state,
        controls
    ];
}
}),
"[project]/src/lib/serviceWorker.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "ServiceWorkerManager": ()=>ServiceWorkerManager,
    "serviceWorkerManager": ()=>serviceWorkerManager
});
class ServiceWorkerManager {
    registration = null;
    cacheStatusCallbacks = [];
    async register() {
        if ("undefined" === 'undefined' || !('serviceWorker' in navigator)) {
            console.warn('Service Worker not supported');
            return null;
        }
        //TURBOPACK unreachable
        ;
    }
    async getCacheStatus() {
        if (!this.registration || !navigator.serviceWorker.controller) {
            return null;
        }
        return new Promise((resolve)=>{
            const channel = new MessageChannel();
            channel.port1.onmessage = (event)=>{
                if (event.data?.type === 'CACHE_STATUS') {
                    const status = {
                        ...event.data,
                        isReady: event.data.modelsCached === event.data.totalModels
                    };
                    resolve(status);
                }
            };
            navigator.serviceWorker.controller.postMessage({
                type: 'GET_CACHE_STATUS'
            }, [
                channel.port2
            ]);
        });
    }
    onCacheStatusUpdate(callback) {
        this.cacheStatusCallbacks.push(callback);
        // Return unsubscribe function
        return ()=>{
            const index = this.cacheStatusCallbacks.indexOf(callback);
            if (index > -1) {
                this.cacheStatusCallbacks.splice(index, 1);
            }
        };
    }
    async skipWaiting() {
        if (this.registration?.waiting) {
            this.registration.waiting.postMessage({
                type: 'SKIP_WAITING'
            });
        }
    }
    async unregister() {
        if (this.registration) {
            return await this.registration.unregister();
        }
        return false;
    }
}
const serviceWorkerManager = new ServiceWorkerManager();
}),
"[project]/src/components/VoiceAssistant.tsx [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "default": ()=>__TURBOPACK__default__export__
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$useVoiceAssistant$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/hooks/useVoiceAssistant.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$serviceWorker$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/serviceWorker.ts [app-ssr] (ecmascript)");
'use client';
;
;
;
;
const VoiceAssistant = ()=>{
    const [apiKey, setApiKey] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])('');
    const [showApiKeyInput, setShowApiKeyInput] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(true);
    const [metrics, setMetrics] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(null);
    const [cacheStatus, setCacheStatus] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(null);
    const [voiceState, voiceControls] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$useVoiceAssistant$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useVoiceAssistant"])({
        onStageChange: (stage)=>{
            console.log('Stage changed to:', stage);
        },
        onTranscription: (text)=>{
            console.log('Transcription:', text);
        },
        onResponse: (text)=>{
            console.log('Response:', text);
        },
        onMetrics: (newMetrics)=>{
            setMetrics(newMetrics);
            console.log('Pipeline metrics:', newMetrics);
        },
        onError: (error)=>{
            console.error('Voice assistant error:', error);
        }
    });
    // Initialize service worker and check cache status
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        const initServiceWorker = async ()=>{
            await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$serviceWorker$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["serviceWorkerManager"].register();
            const status = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$serviceWorker$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["serviceWorkerManager"].getCacheStatus();
            setCacheStatus(status);
        };
        initServiceWorker();
        // Update cache status periodically
        const interval = setInterval(async ()=>{
            const status = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$serviceWorker$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["serviceWorkerManager"].getCacheStatus();
            setCacheStatus(status);
        }, 5000);
        return ()=>clearInterval(interval);
    }, []);
    const handleInitialize = async ()=>{
        if (!apiKey.trim()) {
            alert('Please enter your OpenAI API key');
            return;
        }
        try {
            await voiceControls.initialize(apiKey);
            setShowApiKeyInput(false);
        } catch (error) {
            console.error('Initialization failed:', error);
        }
    };
    const handleStartListening = ()=>{
        voiceControls.startListening();
    };
    const handleStopListening = ()=>{
        voiceControls.stopListening();
    };
    const getStageColor = (stage)=>{
        switch(stage){
            case 'listening':
                return 'text-green-400';
            case 'transcribing':
                return 'text-blue-400';
            case 'thinking':
                return 'text-yellow-400';
            case 'speaking':
                return 'text-purple-400';
            default:
                return 'text-gray-400';
        }
    };
    const getStageIcon = (stage)=>{
        switch(stage){
            case 'listening':
                return '🎤';
            case 'transcribing':
                return '📝';
            case 'thinking':
                return '🤔';
            case 'speaking':
                return '🔊';
            default:
                return '⭕';
        }
    };
    if (showApiKeyInput) {
        return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            className: "min-h-screen flex items-center justify-center p-4",
            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "bg-gray-800 rounded-lg p-8 max-w-md w-full",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("h1", {
                        className: "text-2xl font-bold mb-6 text-center",
                        children: "Voice Assistant"
                    }, void 0, false, {
                        fileName: "[project]/src/components/VoiceAssistant.tsx",
                        lineNumber: 101,
                        columnNumber: 11
                    }, ("TURBOPACK compile-time value", void 0)),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "mb-6",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("label", {
                                htmlFor: "apiKey",
                                className: "block text-sm font-medium mb-2",
                                children: "OpenAI API Key"
                            }, void 0, false, {
                                fileName: "[project]/src/components/VoiceAssistant.tsx",
                                lineNumber: 104,
                                columnNumber: 13
                            }, ("TURBOPACK compile-time value", void 0)),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("input", {
                                id: "apiKey",
                                type: "password",
                                value: apiKey,
                                onChange: (e)=>setApiKey(e.target.value),
                                placeholder: "sk-...",
                                className: "w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                            }, void 0, false, {
                                fileName: "[project]/src/components/VoiceAssistant.tsx",
                                lineNumber: 107,
                                columnNumber: 13
                            }, ("TURBOPACK compile-time value", void 0))
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/VoiceAssistant.tsx",
                        lineNumber: 103,
                        columnNumber: 11
                    }, ("TURBOPACK compile-time value", void 0)),
                    cacheStatus && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "mb-6 p-3 bg-gray-700 rounded-md",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                                className: "text-sm font-medium mb-2",
                                children: "Cache Status"
                            }, void 0, false, {
                                fileName: "[project]/src/components/VoiceAssistant.tsx",
                                lineNumber: 119,
                                columnNumber: 15
                            }, ("TURBOPACK compile-time value", void 0)),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "text-xs text-gray-300",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        children: [
                                            "Models: ",
                                            cacheStatus.modelsCached,
                                            "/",
                                            cacheStatus.totalModels
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/components/VoiceAssistant.tsx",
                                        lineNumber: 121,
                                        columnNumber: 17
                                    }, ("TURBOPACK compile-time value", void 0)),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        children: [
                                            "Ready: ",
                                            cacheStatus.isReady ? '✅' : '⏳'
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/components/VoiceAssistant.tsx",
                                        lineNumber: 122,
                                        columnNumber: 17
                                    }, ("TURBOPACK compile-time value", void 0))
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/VoiceAssistant.tsx",
                                lineNumber: 120,
                                columnNumber: 15
                            }, ("TURBOPACK compile-time value", void 0))
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/VoiceAssistant.tsx",
                        lineNumber: 118,
                        columnNumber: 13
                    }, ("TURBOPACK compile-time value", void 0)),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                        onClick: handleInitialize,
                        disabled: !apiKey.trim(),
                        className: "w-full bg-blue-600 hover:bg-blue-700 disabled:bg-gray-600 disabled:cursor-not-allowed px-4 py-2 rounded-md font-medium transition-colors",
                        children: "Initialize Voice Assistant"
                    }, void 0, false, {
                        fileName: "[project]/src/components/VoiceAssistant.tsx",
                        lineNumber: 127,
                        columnNumber: 11
                    }, ("TURBOPACK compile-time value", void 0))
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/VoiceAssistant.tsx",
                lineNumber: 100,
                columnNumber: 9
            }, ("TURBOPACK compile-time value", void 0))
        }, void 0, false, {
            fileName: "[project]/src/components/VoiceAssistant.tsx",
            lineNumber: 99,
            columnNumber: 7
        }, ("TURBOPACK compile-time value", void 0));
    }
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: "min-h-screen flex flex-col items-center justify-center p-4",
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            className: "bg-gray-800 rounded-lg p-8 max-w-2xl w-full",
            children: [
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("h1", {
                    className: "text-3xl font-bold mb-8 text-center",
                    children: "Voice Assistant"
                }, void 0, false, {
                    fileName: "[project]/src/components/VoiceAssistant.tsx",
                    lineNumber: 142,
                    columnNumber: 9
                }, ("TURBOPACK compile-time value", void 0)),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "text-center mb-8",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: `text-6xl mb-4 ${getStageColor(voiceState.currentStage)}`,
                            children: getStageIcon(voiceState.currentStage)
                        }, void 0, false, {
                            fileName: "[project]/src/components/VoiceAssistant.tsx",
                            lineNumber: 146,
                            columnNumber: 11
                        }, ("TURBOPACK compile-time value", void 0)),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: `text-xl font-medium ${getStageColor(voiceState.currentStage)}`,
                            children: voiceState.currentStage.charAt(0).toUpperCase() + voiceState.currentStage.slice(1)
                        }, void 0, false, {
                            fileName: "[project]/src/components/VoiceAssistant.tsx",
                            lineNumber: 149,
                            columnNumber: 11
                        }, ("TURBOPACK compile-time value", void 0)),
                        voiceState.isListening && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "mt-2",
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "w-full bg-gray-700 rounded-full h-2",
                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "bg-green-400 h-2 rounded-full transition-all duration-100",
                                        style: {
                                            width: `${Math.min(voiceState.volume * 100, 100)}%`
                                        }
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/VoiceAssistant.tsx",
                                        lineNumber: 156,
                                        columnNumber: 17
                                    }, ("TURBOPACK compile-time value", void 0))
                                }, void 0, false, {
                                    fileName: "[project]/src/components/VoiceAssistant.tsx",
                                    lineNumber: 155,
                                    columnNumber: 15
                                }, ("TURBOPACK compile-time value", void 0)),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "text-xs text-gray-400 mt-1",
                                    children: [
                                        "Volume: ",
                                        (voiceState.volume * 100).toFixed(0),
                                        "%"
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/src/components/VoiceAssistant.tsx",
                                    lineNumber: 161,
                                    columnNumber: 15
                                }, ("TURBOPACK compile-time value", void 0))
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/components/VoiceAssistant.tsx",
                            lineNumber: 154,
                            columnNumber: 13
                        }, ("TURBOPACK compile-time value", void 0))
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/components/VoiceAssistant.tsx",
                    lineNumber: 145,
                    columnNumber: 9
                }, ("TURBOPACK compile-time value", void 0)),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "flex justify-center gap-4 mb-8",
                    children: !voiceState.isListening ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                        onClick: handleStartListening,
                        disabled: !voiceState.isInitialized || voiceState.isProcessing,
                        className: "bg-green-600 hover:bg-green-700 disabled:bg-gray-600 disabled:cursor-not-allowed px-6 py-3 rounded-full font-medium transition-colors flex items-center gap-2",
                        children: "🎤 Start Listening"
                    }, void 0, false, {
                        fileName: "[project]/src/components/VoiceAssistant.tsx",
                        lineNumber: 171,
                        columnNumber: 13
                    }, ("TURBOPACK compile-time value", void 0)) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                        onClick: handleStopListening,
                        className: "bg-red-600 hover:bg-red-700 px-6 py-3 rounded-full font-medium transition-colors flex items-center gap-2",
                        children: "⏹️ Stop Listening"
                    }, void 0, false, {
                        fileName: "[project]/src/components/VoiceAssistant.tsx",
                        lineNumber: 179,
                        columnNumber: 13
                    }, ("TURBOPACK compile-time value", void 0))
                }, void 0, false, {
                    fileName: "[project]/src/components/VoiceAssistant.tsx",
                    lineNumber: 169,
                    columnNumber: 9
                }, ("TURBOPACK compile-time value", void 0)),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "space-y-4 mb-6",
                    children: [
                        voiceState.lastTranscription && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "bg-blue-900/30 p-4 rounded-lg",
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "text-sm text-blue-300 mb-1",
                                    children: "You said:"
                                }, void 0, false, {
                                    fileName: "[project]/src/components/VoiceAssistant.tsx",
                                    lineNumber: 192,
                                    columnNumber: 15
                                }, ("TURBOPACK compile-time value", void 0)),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "text-white",
                                    children: voiceState.lastTranscription
                                }, void 0, false, {
                                    fileName: "[project]/src/components/VoiceAssistant.tsx",
                                    lineNumber: 193,
                                    columnNumber: 15
                                }, ("TURBOPACK compile-time value", void 0))
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/components/VoiceAssistant.tsx",
                            lineNumber: 191,
                            columnNumber: 13
                        }, ("TURBOPACK compile-time value", void 0)),
                        voiceState.lastResponse && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "bg-purple-900/30 p-4 rounded-lg",
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "text-sm text-purple-300 mb-1",
                                    children: "Assistant:"
                                }, void 0, false, {
                                    fileName: "[project]/src/components/VoiceAssistant.tsx",
                                    lineNumber: 199,
                                    columnNumber: 15
                                }, ("TURBOPACK compile-time value", void 0)),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "text-white",
                                    children: voiceState.lastResponse
                                }, void 0, false, {
                                    fileName: "[project]/src/components/VoiceAssistant.tsx",
                                    lineNumber: 200,
                                    columnNumber: 15
                                }, ("TURBOPACK compile-time value", void 0))
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/components/VoiceAssistant.tsx",
                            lineNumber: 198,
                            columnNumber: 13
                        }, ("TURBOPACK compile-time value", void 0))
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/components/VoiceAssistant.tsx",
                    lineNumber: 189,
                    columnNumber: 9
                }, ("TURBOPACK compile-time value", void 0)),
                metrics && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "bg-gray-700 p-4 rounded-lg",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                            className: "text-sm font-medium mb-3",
                            children: "Performance Metrics"
                        }, void 0, false, {
                            fileName: "[project]/src/components/VoiceAssistant.tsx",
                            lineNumber: 208,
                            columnNumber: 13
                        }, ("TURBOPACK compile-time value", void 0)),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "grid grid-cols-2 gap-4 text-xs",
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: "text-gray-400",
                                            children: "STT Latency"
                                        }, void 0, false, {
                                            fileName: "[project]/src/components/VoiceAssistant.tsx",
                                            lineNumber: 211,
                                            columnNumber: 17
                                        }, ("TURBOPACK compile-time value", void 0)),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: "font-mono",
                                            children: [
                                                metrics.sttLatency.toFixed(0),
                                                "ms"
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/src/components/VoiceAssistant.tsx",
                                            lineNumber: 212,
                                            columnNumber: 17
                                        }, ("TURBOPACK compile-time value", void 0))
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/src/components/VoiceAssistant.tsx",
                                    lineNumber: 210,
                                    columnNumber: 15
                                }, ("TURBOPACK compile-time value", void 0)),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: "text-gray-400",
                                            children: "LLM Latency"
                                        }, void 0, false, {
                                            fileName: "[project]/src/components/VoiceAssistant.tsx",
                                            lineNumber: 215,
                                            columnNumber: 17
                                        }, ("TURBOPACK compile-time value", void 0)),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: "font-mono",
                                            children: [
                                                metrics.llmLatency.toFixed(0),
                                                "ms"
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/src/components/VoiceAssistant.tsx",
                                            lineNumber: 216,
                                            columnNumber: 17
                                        }, ("TURBOPACK compile-time value", void 0))
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/src/components/VoiceAssistant.tsx",
                                    lineNumber: 214,
                                    columnNumber: 15
                                }, ("TURBOPACK compile-time value", void 0)),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: "text-gray-400",
                                            children: "TTS Latency"
                                        }, void 0, false, {
                                            fileName: "[project]/src/components/VoiceAssistant.tsx",
                                            lineNumber: 219,
                                            columnNumber: 17
                                        }, ("TURBOPACK compile-time value", void 0)),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: "font-mono",
                                            children: [
                                                metrics.ttsLatency.toFixed(0),
                                                "ms"
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/src/components/VoiceAssistant.tsx",
                                            lineNumber: 220,
                                            columnNumber: 17
                                        }, ("TURBOPACK compile-time value", void 0))
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/src/components/VoiceAssistant.tsx",
                                    lineNumber: 218,
                                    columnNumber: 15
                                }, ("TURBOPACK compile-time value", void 0)),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: "text-gray-400",
                                            children: "Total Latency"
                                        }, void 0, false, {
                                            fileName: "[project]/src/components/VoiceAssistant.tsx",
                                            lineNumber: 223,
                                            columnNumber: 17
                                        }, ("TURBOPACK compile-time value", void 0)),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: `font-mono ${metrics.totalLatency < 1200 ? 'text-green-400' : 'text-yellow-400'}`,
                                            children: [
                                                metrics.totalLatency.toFixed(0),
                                                "ms"
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/src/components/VoiceAssistant.tsx",
                                            lineNumber: 224,
                                            columnNumber: 17
                                        }, ("TURBOPACK compile-time value", void 0))
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/src/components/VoiceAssistant.tsx",
                                    lineNumber: 222,
                                    columnNumber: 15
                                }, ("TURBOPACK compile-time value", void 0))
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/components/VoiceAssistant.tsx",
                            lineNumber: 209,
                            columnNumber: 13
                        }, ("TURBOPACK compile-time value", void 0)),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "mt-2 text-xs text-gray-400",
                            children: [
                                "Audio Length: ",
                                metrics.audioLength.toFixed(1),
                                "s"
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/components/VoiceAssistant.tsx",
                            lineNumber: 229,
                            columnNumber: 13
                        }, ("TURBOPACK compile-time value", void 0))
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/components/VoiceAssistant.tsx",
                    lineNumber: 207,
                    columnNumber: 11
                }, ("TURBOPACK compile-time value", void 0)),
                voiceState.error && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "bg-red-900/30 border border-red-600 p-4 rounded-lg mt-4",
                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "text-red-300 text-sm",
                        children: [
                            "Error: ",
                            voiceState.error
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/VoiceAssistant.tsx",
                        lineNumber: 238,
                        columnNumber: 13
                    }, ("TURBOPACK compile-time value", void 0))
                }, void 0, false, {
                    fileName: "[project]/src/components/VoiceAssistant.tsx",
                    lineNumber: 237,
                    columnNumber: 11
                }, ("TURBOPACK compile-time value", void 0)),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "text-center mt-6",
                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                        onClick: ()=>{
                            voiceControls.reset();
                            setShowApiKeyInput(true);
                            setMetrics(null);
                        },
                        className: "text-gray-400 hover:text-white text-sm underline",
                        children: "Reset & Change API Key"
                    }, void 0, false, {
                        fileName: "[project]/src/components/VoiceAssistant.tsx",
                        lineNumber: 244,
                        columnNumber: 11
                    }, ("TURBOPACK compile-time value", void 0))
                }, void 0, false, {
                    fileName: "[project]/src/components/VoiceAssistant.tsx",
                    lineNumber: 243,
                    columnNumber: 9
                }, ("TURBOPACK compile-time value", void 0))
            ]
        }, void 0, true, {
            fileName: "[project]/src/components/VoiceAssistant.tsx",
            lineNumber: 141,
            columnNumber: 7
        }, ("TURBOPACK compile-time value", void 0))
    }, void 0, false, {
        fileName: "[project]/src/components/VoiceAssistant.tsx",
        lineNumber: 140,
        columnNumber: 5
    }, ("TURBOPACK compile-time value", void 0));
};
const __TURBOPACK__default__export__ = VoiceAssistant;
}),
"[project]/src/app/page.tsx [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "default": ()=>Home
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$VoiceAssistant$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/VoiceAssistant.tsx [app-ssr] (ecmascript)");
'use client';
;
;
function Home() {
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: "min-h-screen bg-gradient-to-br from-gray-900 to-black text-white",
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$VoiceAssistant$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {}, void 0, false, {
            fileName: "[project]/src/app/page.tsx",
            lineNumber: 8,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "[project]/src/app/page.tsx",
        lineNumber: 7,
        columnNumber: 5
    }, this);
}
}),

};

//# sourceMappingURL=%5Broot-of-the-server%5D__49c5d6d6._.js.map