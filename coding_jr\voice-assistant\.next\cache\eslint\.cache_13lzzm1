[{"C:\\Web_dev\\coding_jr\\voice-assistant\\src\\app\\layout.tsx": "1", "C:\\Web_dev\\coding_jr\\voice-assistant\\src\\app\\page.tsx": "2", "C:\\Web_dev\\coding_jr\\voice-assistant\\src\\components\\VoiceAssistant.tsx": "3", "C:\\Web_dev\\coding_jr\\voice-assistant\\src\\hooks\\useAudioRecording.ts": "4", "C:\\Web_dev\\coding_jr\\voice-assistant\\src\\hooks\\useOpenAIChat.ts": "5", "C:\\Web_dev\\coding_jr\\voice-assistant\\src\\hooks\\useTTS.ts": "6", "C:\\Web_dev\\coding_jr\\voice-assistant\\src\\hooks\\useVoiceAssistant.ts": "7", "C:\\Web_dev\\coding_jr\\voice-assistant\\src\\hooks\\useWhisperSTT.ts": "8", "C:\\Web_dev\\coding_jr\\voice-assistant\\src\\lib\\audioManager.ts": "9", "C:\\Web_dev\\coding_jr\\voice-assistant\\src\\lib\\openaiClient.ts": "10", "C:\\Web_dev\\coding_jr\\voice-assistant\\src\\lib\\performanceMonitor.ts": "11", "C:\\Web_dev\\coding_jr\\voice-assistant\\src\\lib\\serviceWorker.ts": "12"}, {"size": 1232, "mtime": 1753954581401, "results": "13", "hashOfConfig": "14"}, {"size": 248, "mtime": 1753955292802, "results": "15", "hashOfConfig": "14"}, {"size": 9089, "mtime": 1753955872877, "results": "16", "hashOfConfig": "14"}, {"size": 4790, "mtime": 1753956140200, "results": "17", "hashOfConfig": "14"}, {"size": 4261, "mtime": 1753955113193, "results": "18", "hashOfConfig": "14"}, {"size": 6985, "mtime": 1753955945283, "results": "19", "hashOfConfig": "14"}, {"size": 9799, "mtime": 1753955895533, "results": "20", "hashOfConfig": "14"}, {"size": 5941, "mtime": 1753955024423, "results": "21", "hashOfConfig": "14"}, {"size": 7086, "mtime": 1753955822880, "results": "22", "hashOfConfig": "14"}, {"size": 4006, "mtime": 1753955088119, "results": "23", "hashOfConfig": "14"}, {"size": 7833, "mtime": 1753955773395, "results": "24", "hashOfConfig": "14"}, {"size": 3125, "mtime": 1753954736517, "results": "25", "hashOfConfig": "14"}, {"filePath": "26", "messages": "27", "suppressedMessages": "28", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "678sc5", {"filePath": "29", "messages": "30", "suppressedMessages": "31", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "32", "messages": "33", "suppressedMessages": "34", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "35", "messages": "36", "suppressedMessages": "37", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "38", "messages": "39", "suppressedMessages": "40", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "41", "messages": "42", "suppressedMessages": "43", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "44", "messages": "45", "suppressedMessages": "46", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "47", "messages": "48", "suppressedMessages": "49", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "50", "messages": "51", "suppressedMessages": "52", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "53", "messages": "54", "suppressedMessages": "55", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "56", "messages": "57", "suppressedMessages": "58", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "59", "messages": "60", "suppressedMessages": "61", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Web_dev\\coding_jr\\voice-assistant\\src\\app\\layout.tsx", [], [], "C:\\Web_dev\\coding_jr\\voice-assistant\\src\\app\\page.tsx", [], [], "C:\\Web_dev\\coding_jr\\voice-assistant\\src\\components\\VoiceAssistant.tsx", [], [], "C:\\Web_dev\\coding_jr\\voice-assistant\\src\\hooks\\useAudioRecording.ts", [], [], "C:\\Web_dev\\coding_jr\\voice-assistant\\src\\hooks\\useOpenAIChat.ts", [], [], "C:\\Web_dev\\coding_jr\\voice-assistant\\src\\hooks\\useTTS.ts", [], [], "C:\\Web_dev\\coding_jr\\voice-assistant\\src\\hooks\\useVoiceAssistant.ts", [], [], "C:\\Web_dev\\coding_jr\\voice-assistant\\src\\hooks\\useWhisperSTT.ts", [], [], "C:\\Web_dev\\coding_jr\\voice-assistant\\src\\lib\\audioManager.ts", [], [], "C:\\Web_dev\\coding_jr\\voice-assistant\\src\\lib\\openaiClient.ts", [], [], "C:\\Web_dev\\coding_jr\\voice-assistant\\src\\lib\\performanceMonitor.ts", [], [], "C:\\Web_dev\\coding_jr\\voice-assistant\\src\\lib\\serviceWorker.ts", [], []]